
-- 案件模板 同步
UPDATE tmpl_basis_module_field SET field_data_type='system_dict',field_values=NULL WHERE field_name = 'mediate_status';
UPDATE tmpl_case_module_field SET field_data_type='system_dict',field_values=NULL WHERE field_name = 'mediate_status';


UPDATE tmpl_basis_module_field SET field_dict_type='mediate_status',field_values=NULL WHERE field_name = 'mediate_status';
UPDATE tmpl_case_module_field SET field_dict_type='mediate_status',field_values=NULL WHERE field_name = 'mediate_status';


-- 插入字典类型
INSERT INTO `sys_dict` (`dict_type`, `dict_name`, `dict_status`, `sys_flag`, `remark`, `creator_id`, `updater_id`)
VALUES ('mediate_status', '调解状态', 1, 0, '', 1, 1);

-- 获取 dict_id
SET @dict_id = (SELECT dict_id FROM sys_dict WHERE dict_type = 'mediate_status');

-- 插入字典数据
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES
    (NULL, @dict_id, 1, '已分派调解组织', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 2, '已分派调解团队', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 3, '已分派调解员', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 4, '继续触达', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 5, '继续调解', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 6, '意向调解', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 7, '调解完成', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 0, '空', 1, '', 1, 1, NULL);
