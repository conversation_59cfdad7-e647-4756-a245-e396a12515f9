-- 原案件调解结束时间更换备注为案件关闭时间
ALTER TABLE mdt_case MODIFY COLUMN `mediate_close_time` datetime COMMENT '案件关闭时间';


UPDATE tmpl_basis_module_field SET field_title = '案件关闭时间',is_update=0 WHERE basis_module_field_id = 72;
UPDATE tmpl_case_module_field SET field_title = '案件关闭时间',is_update=0 WHERE basis_module_field_id = 72;

-- 案件新增调解结束时间

ALTER TABLE `mdt_case` ADD COLUMN `mediate_done_time` datetime COMMENT '调解完成时间' AFTER `expiration_time`;


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (76, 3, 'mediate_done_time', '调解完成时间', NULL, 30, NULL, 'datetime'
,NULL
, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);


-- 模板字段新增 是否结案审批检验
ALTER TABLE `tmpl_basis_module_field` ADD COLUMN `is_approval_validated` tinyint(1) DEFAULT 0 COMMENT '是否结案审批检验' AFTER `is_require`;

ALTER TABLE `tmpl_case_module_field` ADD COLUMN `is_approval_validated` tinyint(1) DEFAULT 0 COMMENT '是否结案审批检验' AFTER `is_require`;

