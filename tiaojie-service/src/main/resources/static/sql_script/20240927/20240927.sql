

ALTER TABLE `mdt_task_complete_register` ADD COLUMN `delivered_task_account_id` bigint NULL COMMENT '送达任务处理人' AFTER `delivered_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `scheduling_task_account_id` bigint NULL COMMENT '排期任务处理人' AFTER `scheduling_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `evaluation_task_account_id` bigint NULL COMMENT '审评鉴处理人' AFTER `evaluation_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `closed_task_account_id` bigint NULL COMMENT '结案任务处理人' AFTER `closed_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `reach_task_account_id` bigint NULL COMMENT '案件触达处理人' AFTER `reach_task_status`;

ALTER TABLE `mdt_task_complete_register` ADD COLUMN `repair_task_status` int NOT NULL DEFAULT '1' COMMENT '信修任务状态' AFTER `reach_task_account_id`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `repair_task_account_id` bigint NULL COMMENT '信修任务处理人' AFTER `repair_task_status`;

ALTER TABLE `mdt_task_complete_register` ADD COLUMN `mediate_task_status` int NOT NULL DEFAULT '1' COMMENT '调解任务状态' AFTER `repair_task_account_id`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `mediate_task_account_id` bigint NULL COMMENT '调解任务处理人' AFTER `mediate_task_status`;

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (********, ********, '任务分派', '1', 3, 3, NULL, 120, NULL, 'mdt:case:task:assign', 'caseTaskAssign', 1, '2022-09-27 15:53:13', 1, '2024-09-27 11:29:02', 1);

UPDATE `tmpl_basis_module_field` SET `field_title` = '送达任务' WHERE `basis_module_field_id` = 58;
UPDATE `tmpl_basis_module_field` SET `field_title` = '排期任务' WHERE `basis_module_field_id` = 59;
UPDATE `tmpl_basis_module_field` SET `field_title` = '审评鉴任务' WHERE `basis_module_field_id` = 60;
UPDATE `tmpl_basis_module_field` SET `field_title` = '结案任务' WHERE `basis_module_field_id` = 61;
UPDATE `tmpl_basis_module_field` SET `field_title` = '触达任务' WHERE `basis_module_field_id` = 62;

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (63, 4, 'repair_task_status', '信修任务', NULL, 6, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"待处理\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"进行中\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"已完成\\\"}]\",\"name\":\"TaskStatusEnum\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (64, 4, 'mediate_task_status', '调解任务', NULL, 7, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"待处理\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"进行中\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"已完成\\\"}]\",\"name\":\"TaskStatusEnum\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (65, 4, 'delivered_task_account_id', '送达任务负责人', NULL, 8, NULL, 'varchar', '{"type":"foreign_key","tableName":"auth_employee","keyFieldName":"account_id","valueFieldName":"employee_name"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (66, 4, 'scheduling_task_account_id', '排期任务负责人', NULL, 9, NULL, 'varchar', '{"type":"foreign_key","tableName":"auth_employee","keyFieldName":"account_id","valueFieldName":"employee_name"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (67, 4, 'evaluation_task_account_id', '审评鉴负责人', NULL, 10, NULL, 'varchar', '{"type":"foreign_key","tableName":"auth_employee","keyFieldName":"account_id","valueFieldName":"employee_name"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (68, 4, 'closed_task_account_id', '结案任务负责人', NULL, 11, NULL, 'varchar', '{"type":"foreign_key","tableName":"auth_employee","keyFieldName":"account_id","valueFieldName":"employee_name"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (69, 4, 'reach_task_account_id', '案件触达负责人', NULL, 12, NULL, 'varchar', '{"type":"foreign_key","tableName":"auth_employee","keyFieldName":"account_id","valueFieldName":"employee_name"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (70, 4, 'repair_task_account_id', '信修任务负责人', NULL, 13, NULL, 'varchar', '{"type":"foreign_key","tableName":"auth_employee","keyFieldName":"account_id","valueFieldName":"employee_name"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (71, 4, 'mediate_task_account_id', '调解任务负责人', NULL, 14, NULL, 'varchar', '{"type":"foreign_key","tableName":"auth_employee","keyFieldName":"account_id","valueFieldName":"employee_name"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

UPDATE `sys_parameter` SET `param_value` = '@2024 北京星川律政科技有限责任公司 版权所有' WHERE `module_code` = 'RECORD_INFORMATION' AND `param_code` = 'BAN_QUAN_CONTENT';

UPDATE `sys_parameter` SET `param_value` = '京ICP备**********号 京ICP备**********号-1' WHERE `module_code` = 'RECORD_INFORMATION' AND `param_code` = 'ICP_CONTENT';