-- 案件新增调解类型

ALTER TABLE `mdt_case` ADD COLUMN `case_mediate_type` varchar(50) NULL  COMMENT '调解类型';


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (74, 3, 'case_mediate_type', '调解类型', NULL, 28, NULL, 'dict'
,'{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"先行调解\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"诉前调解\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"非前调解\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"诉保调解\\\"}]\",\"name\":\"mediateType\"}'
, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);

-- 案件新增业务来源

ALTER TABLE `mdt_case` ADD COLUMN `business_source` varchar(50) NULL  COMMENT '业务来源';


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (75, 3, 'business_source', '业务来源', NULL, 29, NULL, 'dict'
,'{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"司法机关委派\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"商业调解\\\"}]\",\"name\":\"businessSource\"}'
, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

-- 导入记录新增调解类型
ALTER TABLE `mdt_case_import_record` ADD COLUMN `case_mediate_type` varchar(50) NULL  COMMENT '调解类型';