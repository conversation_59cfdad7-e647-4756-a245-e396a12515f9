ALTER TABLE `tiaojie`.`sys_parameter`
    MODIFY COLUMN `param_value` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数值' AFTER `param_name`;

-- 案件编号隐私规则配置
INSERT INTO sys_parameter (module_code, param_code, param_name, param_value, param_cmnt, create_time, creator_id, update_time, updater_id) 
VALUES (
    'CASE',
    'CASE_NUMBER_PRIVACY_RULE',
    '案件编号隐私规则',
    '[
               {"remark": "去掉空格", "key": " ", "value": ""},
               {"remark": "年份", "key": "（2024）", "value": "24-"},
               {"remark": "年份", "key": "(2024)", "value": "24-"},
               {"remark": "年份", "key": "（2025）", "value": "25-"},
               {"remark": "年份", "key": "(2025)", "value": "25-"},
               {"remark": "省份代字", "key": "沪", "value": "DJ-"},
               {"remark": "省份代字", "key": "京", "value": "NK-"},
               {"remark": "省份代字", "key": "粤", "value": "HF-"},
               {"remark": "法院代码", "key": "0112", "value": "ng-"},
               {"remark": "法院代码", "key": "0104", "value": "cj-"},
               {"remark": "法院代码", "key": "0102", "value": "cv-"},
               {"remark": "法院代码", "key": "0309", "value": "kg-"},
               {"remark": "类型代字", "key": "立案", "value": "la-"},
               {"remark": "类型代字", "key": "民委调", "value": "wt-"},
               {"remark": "类型代字", "key": "民诉前调", "value": "sq-"},
               {"remark": "类型代字", "key": "民初", "value": "mc-"},
               {"remark": "类型代字", "key": "民调", "value": "mt-"},
               {"remark": "类型代字", "key": "徐综治", "value": "cj-zz-"},
               {"remark": "类型代字", "key": "杨综治", "value": "to-zz-"},
               {"remark": "类型代字", "key": "综治", "value": "zz-"},
               {"remark": "类型代字", "key": "执恢", "value": "zh-"},
               {"remark": "类型代字", "key": "执", "value": "zx-"}
           ]',
    '案件编号隐私规则配置，用于将案件编号中的敏感信息进行脱敏处理',
    NOW(),
    1,
    NOW(),
    1
);
