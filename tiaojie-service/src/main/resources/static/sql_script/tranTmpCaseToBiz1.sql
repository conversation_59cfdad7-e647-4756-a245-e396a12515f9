INSERT INTO mdt_case(
case_id, case_batch_id, case_type_id, case_status, case_no,
case_nature_content, claimant_name, product_name, respond_name, respond_gender,
respond_birth, respond_id_cert_no, respond_mobile, respond_Household, respond_address,
loan_start_date, loan_end_date, loan_term, month_repay_day, repaid_period,
repaid_amount, annual_interest_rate, loan_total, service_fee, overdue_date,
overdue_principal, overdue_interest, penalty_interest, arb_mdt_amount, contacts1,
contacts1_mobile, contacts2, contacts2_mobile, contacts3, contacts3_mobile,
mortgage_car_no, mortgage_car_brand, mortgage_car_model, mortgage_car_worth, mortgage_car_remark,
current_mediator_id, current_mediator_name,
creator_id, updater_id
)
SELECT
case_id, ${case_batch_id}, case_type_id, case_status, case_no,
case_nature_content, claimant_name, product_name, respond_name, respond_gender,
respond_birth, respond_id_cert_no, respond_mobile, respond_Household, respond_address,
loan_start_date, loan_end_date, loan_term, month_repay_day, repaid_period,
repaid_amount, annual_interest_rate, loan_total, service_fee, overdue_date,
overdue_principal, overdue_interest, penalty_interest, arb_mdt_amount, contacts1,
contacts1_mobile, contacts2, contacts2_mobile, contacts3, contacts3_mobile,
mortgage_car_no, mortgage_car_brand, mortgage_car_model, mortgage_car_worth, mortgage_car_remark,
current_mediator_id, current_mediator_name,
creator_id, updater_id

from tmp_mdt_case
where case_batch_id = #{task_log_id}

ON DUPLICATE KEY UPDATE
case_no = VALUES(case_no),
case_nature_content = VALUES(case_nature_content), claimant_name = VALUES(claimant_name), product_name = VALUES(product_name), respond_name = VALUES(respond_name), respond_gender = VALUES(respond_gender),
respond_birth = VALUES(respond_birth), respond_id_cert_no = VALUES(respond_id_cert_no), respond_mobile = VALUES(respond_mobile), respond_Household = VALUES(respond_Household), respond_address = VALUES(respond_address),
loan_start_date = VALUES(loan_start_date), loan_end_date = VALUES(loan_end_date), loan_term = VALUES(loan_term), month_repay_day = VALUES(month_repay_day), repaid_period = VALUES(repaid_period),
repaid_amount = VALUES(repaid_amount), annual_interest_rate = VALUES(annual_interest_rate), loan_total = VALUES(loan_total), service_fee = VALUES(service_fee), overdue_date = VALUES(overdue_date),
overdue_principal = VALUES(overdue_principal), overdue_interest = VALUES(overdue_interest), penalty_interest = VALUES(penalty_interest), arb_mdt_amount = VALUES(arb_mdt_amount), contacts1 = VALUES(contacts1),
contacts1_mobile = VALUES(contacts1_mobile), contacts2 = VALUES(contacts2), contacts2_mobile = VALUES(contacts2_mobile), contacts3 = VALUES(contacts3), contacts3_mobile = VALUES(contacts3_mobile),
mortgage_car_no = VALUES(mortgage_car_no), mortgage_car_brand = VALUES(mortgage_car_brand), mortgage_car_model = VALUES(mortgage_car_model), mortgage_car_worth = VALUES(mortgage_car_worth), mortgage_car_remark = VALUES(mortgage_car_remark),
current_mediator_id = VALUES(current_mediator_id), current_mediator_name = VALUES(current_mediator_name),
updater_id = VALUES(updater_id)