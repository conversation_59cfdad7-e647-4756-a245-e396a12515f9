-- 修改案件详情表标的字段类型
ALTER TABLE mdt_case MODIFY `case_subject_matter` decimal(18,2) DEFAULT NULL COMMENT '标的';

-- 模板增加标的字段配置
INSERT INTO `tmpl_basis_module_field` (`basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (3, 'case_subject_matter', '标的额', '标的额', 31, NULL, 'decimal', NULL, NULL, 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

-- 修改案件表 标的额字段
-- 满足数字条件的
UPDATE mdt_case c
    JOIN (
    SELECT
    case_id,
    CAST(field_value->>'$."标的金额"' AS CHAR) as vals,
    cast(REPLACE(CAST(field_value->>'$."标的金额"' AS CHAR), ',', '') AS DECIMAL(18,2)) as subject_matter_value
    from `mdt_case_custom_data`
    where  field_value->>'$."标的金额"' REGEXP '^(?:[0-9]+(?:,[0-9]+)*(?:\.[0-9]{1,2})?|[0-9]+(?:\.[0-9]+)?)$'
    and `case_id` not in(1359393970353999872,1359393970316251137,1358682705075769344) -- 这几个case有问题
    ) AS custom_data ON c.case_id = custom_data.case_id
    SET c.case_subject_matter = custom_data.subject_matter_value
WHERE  c.case_subject_matter is null;

-- 带元的
UPDATE mdt_case c
    JOIN (
    SELECT
    case_id,
    CAST(field_value->>'$."标的金额"' AS CHAR) as vals,
    cast(REPLACE(REPLACE(CAST(field_value->>'$."标的金额"' AS CHAR), ',', ''),'元','') AS DECIMAL(18,2)) as subject_matter_value
    from `mdt_case_custom_data`
    where  field_value->>'$."标的金额"' REGEXP '^(?:[0-9]{1,3}(?:,[0-9]{3})*(?:\.[0-9]{1,2})?元|[0-9]+(?:\.[0-9]{1,2})?元)$'
    ) AS custom_data ON c.case_id = custom_data.case_id
    SET c.case_subject_matter = custom_data.subject_matter_value
WHERE  c.case_subject_matter is null;

-- 带万元的
UPDATE mdt_case c
    JOIN (
    SELECT
    case_id,
    CAST(field_value->>'$."标的金额"' AS CHAR) as vals,
    cast(REPLACE(REPLACE(CAST(field_value->>'$."标的金额"' AS CHAR), ',', ''),'万元','')*10000 AS DECIMAL(18,2)) as subject_matter_value
    from `mdt_case_custom_data`
    where  field_value->>'$."标的金额"' REGEXP '^(?:[0-9]{1,3}(?:,[0-9]{3})*(?:\.[0-9]+)?万元|[0-9]+(?:\.[0-9]+)?万元)$'
    ) AS custom_data ON c.case_id = custom_data.case_id
    SET c.case_subject_matter = custom_data.subject_matter_value
WHERE  c.case_subject_matter is null;

-- 带万的
UPDATE mdt_case c
    JOIN (
    SELECT
    case_id,
    CAST(field_value->>'$."标的金额"' AS CHAR) as vals,
    cast(REPLACE(REPLACE(CAST(field_value->>'$."标的金额"' AS CHAR), ',', ''),'万','')*10000 AS DECIMAL(18,2)) as subject_matter_value
    from `mdt_case_custom_data`
    where  field_value->>'$."标的金额"' REGEXP '^(?:[0-9]{1,3}(?:,[0-9]{3})*(?:\.[0-9]+)?万|[0-9]+(?:\.[0-9]+)?万)$'
    ) AS custom_data ON c.case_id = custom_data.case_id
    SET c.case_subject_matter = custom_data.subject_matter_value
WHERE  c.case_subject_matter is null;

-- 查找需要手动处理的
(
    SELECT
        ccd.case_id,
        c.`case_no` ,
        CAST(field_value->>'$."标的金额"' AS CHAR) as val
    from `mdt_case_custom_data` ccd
             LEFT JOIN `mdt_case` c on c.`case_id` = ccd.`case_id`
    where
        field_value->>'$."标的金额"' NOT REGEXP '^(?:[0-9]+(?:,[0-9]+)*(?:\.[0-9]{1,2})?|[0-9]+(?:\.[0-9]+)?)$'
      and field_value->>'$."标的金额"' NOT REGEXP '^(?:[0-9]{1,3}(?:,[0-9]{3})*(?:\.[0-9]{1,2})?元|[0-9]+(?:\.[0-9]{1,2})?元)$'
      and field_value->>'$."标的金额"' NOT REGEXP '^(?:[0-9]{1,3}(?:,[0-9]{3})*(?:\.[0-9]+)?万元|[0-9]+(?:\.[0-9]+)?万元)$'
      and field_value->>'$."标的金额"' not REGEXP '^(?:[0-9]{1,3}(?:,[0-9]{3})*(?:\.[0-9]+)?万|[0-9]+(?:\.[0-9]+)?万)$'
      and field_value->>'$."标的金额"' != 'null'
      and field_value->>'$."标的金额"' != '/'
      and CAST(field_value->>'$."标的金额"' AS CHAR) != "暂无数据"
)
UNION
(
    SELECT
        ccd.case_id,
        c.`case_no` ,
        CAST(field_value->>'$."标的金额"' AS CHAR) as val
    from `mdt_case_custom_data` ccd
             LEFT JOIN `mdt_case` c on c.`case_id` = ccd.`case_id`
    where c.`case_id` in(1359393970353999872,1359393970316251137,1358682705075769344) HAVING val is not null
)

-- 基本模板添加标的额字段
INSERT INTO tmpl_case_module_field(tmpl_module_id,tmpl_id,basis_module_field_id,field_sn,field_name,field_title,field_desc,field_data_type,is_validated,is_require,is_approval_validated,is_update,is_desensitize,create_time,creator_id,update_time,updater_id)
SELECT
    cm.tmpl_module_id,
    c.tmpl_id,
    (SELECT bmf.basis_module_field_id from tmpl_basis_module_field bmf WHERE field_name="case_subject_matter" and field_title="标的额" limit 1)  as basis_module_field_id,
(SELECT cmf.field_sn from tmpl_case_module_field cmf WHERE cm.tmpl_module_id = cmf.tmpl_module_id ORDER BY cmf.field_sn desc limit 1)+1 as field_sn,
"case_subject_matter" as field_name,
"标的额" as field_title,
"标的额" as field_desc,
"decimal" as field_data_type,
"0" as is_validated,
"0" as is_require,
"0" as is_approval_validated,
"1" as is_update,
"0" as is_desensitize,
now() as create_time,
"1" as creator_id,
now() as update_time,
"1" as updater_id
from tmpl_case c
    LEFT JOIN tmpl_case_module cm on c.tmpl_id = cm.tmpl_id and cm.basis_module_id = 3
WHERE
    not EXISTS (SELECT 1 from tmpl_case_module_field cmf2 WHERE cmf2.tmpl_id = c.tmpl_id and field_name = "case_subject_matter" and field_title = "标的额")
HAVING field_sn is not null


-- 导入导出模板添加标的额字段
INSERT into deputy_tmpl_mapping(deputy_tmpl_id,field_name,tmpl_module_field_id,field_sn,create_time,creator_id,update_time,updater_id)
SELECT
    t.deputy_tmpl_id,
    "标的额" as field_name,
    (SELECT cmf.tmpl_module_field_id FROM tmpl_case_module_field cmf WHERE cmf.tmpl_id = t.tmpl_id and field_name = "case_subject_matter" and field_title = "标的额") as tmpl_module_field_id,
    1 as field_sn,
    now() as create_time,
    "1" as creator_id,
    now() as update_time,
    "1" as updater_id
from deputy_tmpl t
WHERE
    not EXISTS (
        SELECT 1 from deputy_tmpl_mapping tm,tmpl_case_module_field cmf2 WHERE tm.deputy_tmpl_id = t.deputy_tmpl_id and tm.tmpl_module_field_id = cmf2.tmpl_module_field_id and cmf2.field_name="case_subject_matter"
    )
HAVING tmpl_module_field_id is not null