INSERT INTO `sys_role`(`role_id`, `company_id`, `group_name`, `role_name`, `role_remark`, `role_code`, `role_status`,
                       `is_system`, `create_time`, `creator_id`, `update_time`, `updater_id`)
VALUES (1273485533103919104, NULL, '3', '庭室负责人', '', NULL, NULL, 0, '2024-08-15 13:56:44', 1, '2024-08-15 13:56:44', 1);

INSERT INTO `sys_role`(`role_id`, `company_id`, `group_name`, `role_name`, `role_remark`, `role_code`, `role_status`,
                       `is_system`, `create_time`, `creator_id`, `update_time`, `updater_id`)
VALUES (1273485575592218624, NULL, '3', '案源方负责人', '', NULL, NULL, 0, '2024-08-15 13:56:54', 1, '2024-08-15 13:56:54',
        1);


CREATE TABLE mdt_task_complete_register
(
    `task_complete_id`       bigint(20) NOT NULL COMMENT '任务完成登记id',
    `delivered_task_status`  int(11) DEFAULT 1 NOT NULL COMMENT '送达任务状态',
    `scheduling_task_status` int(11) DEFAULT 1 NOT NULL COMMENT '排期任务状态',
    `evaluation_task_status` int(11) DEFAULT 1 NOT NULL COMMENT '审评鉴状态',
    `closed_task_status`     int(11) DEFAULT 1 NOT NULL COMMENT '结案任务状态',
    `reach_task_status`      int(11) DEFAULT 1 NOT NULL COMMENT '案件触达状态',
    `case_id`                bigint(20) NOT NULL COMMENT '案件ID',
    `creator_id`             bigint(20) COMMENT '创建人',
    `create_time`            datetime COMMENT '创建时间',
    `updater_id`             bigint(20) COMMENT '修改人id',
    `update_time`            datetime COMMENT '修改时间',
    PRIMARY KEY (task_complete_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC  COMMENT = '任务完成情况登记表';

ALTER TABLE `mdt_task_complete_register`
    ADD UNIQUE INDEX `case_id_unique_index`(`case_id`) USING BTREE COMMENT 'case_id唯一约束';

-- 20240820未运行在生产 --
INSERT INTO `sys_role`(`role_id`, `company_id`, `group_name`, `role_name`, `role_remark`, `role_code`, `role_status`,
                       `is_system`, `create_time`, `creator_id`, `update_time`, `updater_id`)
VALUES (1275239743709384704, NULL, '4', '电话调解', '', NULL, NULL, 0, '2024-08-20 10:07:21', 1, '2024-08-20 10:07:21', 1);

INSERT INTO `sys_role`(`role_id`, `company_id`, `group_name`, `role_name`, `role_remark`, `role_code`, `role_status`,
                       `is_system`, `create_time`, `creator_id`, `update_time`, `updater_id`)
VALUES (1275239743709384805, NULL, '4', '视频调解', '', NULL, NULL, 0, '2024-08-20 10:07:21', 1, '2024-08-20 10:07:21', 1);

-- 20240822未运行 --
ALTER TABLE `tmpl_basis_module`
DROP COLUMN `mapping_type`,
ADD COLUMN `allow_multiple_record` tinyint(1) NULL DEFAULT 0 COMMENT '是否允许多条记录' AFTER `is_fixed`;

ALTER TABLE `tmpl_case_module`
DROP COLUMN `mapping_type`,
ADD COLUMN `allow_multiple_record` tinyint(1) NULL DEFAULT 0 COMMENT '是否允许多条记录' AFTER `updater_id`;

ALTER TABLE `doc_tmpl_mapping`
    ADD COLUMN `allow_multiple_record` tinyint(1) NULL COMMENT '是否允许多条记录' AFTER `updater_id`;

ALTER TABLE `doc_tmpl_mapping`
DROP COLUMN `mapping_type`;

UPDATE tmpl_basis_module SET allow_multiple_record = 1 WHERE module_title = "当事人信息" OR module_title = "调解记录";

UPDATE tmpl_case_module SET allow_multiple_record = 1 WHERE module_title = "当事人信息" OR module_title = "调解记录";

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (********, ********, '批量文书生成', '1', 3, 3, NULL, 110, NULL, 'mdt:case:doc:docGenerate', 'batchGenerateDoc', 1, '2022-02-10 15:53:13', 1, '2024-08-20 11:29:02', 1);

ALTER TABLE `tmpl_case_module_field`
    MODIFY COLUMN `is_update` tinyint(1) DEFAULT 1 COMMENT '是否可修改';

ALTER TABLE `sys_log`
    MODIFY COLUMN `log_param` json NULL COMMENT '日志参数' AFTER `log_title`,
    ADD COLUMN `account_id` bigint(20) NULL COMMENT '账号ID' AFTER `update_time`,
    ADD COLUMN `account_org_id` bigint(20) NULL COMMENT '员工所属组织ID' AFTER `account_id`,
    ADD COLUMN `account_org_name` varchar(50) NULL COMMENT '员工所属组织名称' AFTER `account_org_id`,
    ADD COLUMN `account_dept_id` bigint(20) NULL COMMENT '员工所属部门ID' AFTER `account_org_name`,
    ADD COLUMN `account_dept_name` varchar(50) NULL COMMENT '员工所属部门名称' AFTER `account_dept_id`,
    ADD COLUMN `operate_type` varchar(50) NULL COMMENT '操作分类' AFTER `account_dept_name`,
    ADD COLUMN `cases` json NULL COMMENT '案件信息' AFTER `operate_type`;


CREATE TABLE `sys_login_log` (
  `login_log_id` bigint NOT NULL,
  `login_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录名',
  `login_pwd` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录密码',
  `account_id` bigint DEFAULT NULL COMMENT '账号ID',
  `request_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求IP',
  `token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '成功所返回的token',
  `employee_name` varchar(50) DEFAULT NULL COMMENT '员工名称',
  `org_id` bigint DEFAULT NULL COMMENT '所属组织ID',
  `org_name` varchar(20) DEFAULT NULL COMMENT '所属组织名称',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`login_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (********, ********, '操作日志', '1', 1, 2, NULL, 60, NULL, NULL, 'operationLog', 1, '2022-02-10 15:53:13', 1, '2024-06-26 17:03:10', 1);

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (14070000, ********, '登录日志', '1', 1, 2, NULL, 70, NULL, NULL, 'loginLog', 1, '2022-02-10 15:53:13', 1, '2024-06-26 17:03:10', 1);

CREATE TABLE `sys_log_case` (
  `log_case_id` bigint NOT NULL,
  `log_id` bigint DEFAULT NULL COMMENT '日志ID',
  `case_id` bigint DEFAULT NULL COMMENT '案件ID',
  `case_no` varchar(150) DEFAULT NULL COMMENT '案件编号',
  `entrusts_id` bigint DEFAULT NULL COMMENT '案件委托方ID',
  `entrusts_dept_id` bigint DEFAULT '-1' COMMENT '案源方部门id',
  `entrusts_name` varchar(50) DEFAULT NULL COMMENT '案源方名称',
  `entrusts_dept_name` varchar(50) DEFAULT NULL COMMENT '案源方部门名称',
  `log_param` json DEFAULT NULL COMMENT '日志参数',
  PRIMARY KEY (`log_case_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

ALTER TABLE `sys_log`
DROP COLUMN `cases`;

ALTER TABLE `sys_dict_data`
ADD COLUMN `sys_flag` tinyint(1) NOT NULL COMMENT '是否系统字典字段;0非系统字典字段，1系统字典字段' AFTER `updater_id`;

UPDATE sys_dict_data SET sys_flag = 1;

UPDATE sys_dict_data SET sys_flag = 0 WHERE dict_id IN (9,11,12,13,15);

INSERT INTO `sys_dict`(`dict_id`, `company_id`, `dict_type`, `dict_name`, `dict_status`, `sys_flag`, `remark`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (21, NULL, 'nature', '案由', 1, 1, '', '2022-08-05 16:56:19', 1, '2024-04-11 16:44:18', 1);

DELETE FROM sys_dict WHERE dict_id IN (1,2,4,5,6,7,8,10,14,16,17);

DELETE FROM sys_dict_data WHERE dict_id IN (1,2,4,5,6,7,8,10,14,16,17);

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (11031000, 11030000, '案件新增文书', '1', 3, 3, NULL, 100, NULL, 'mdt:case:document:add', 'caseDocumentAdd', 1, '2022-02-10 15:53:13', 1, '2024-07-05 15:39:37', 1);

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (11031100, 11030000, '案件任务登记', '1', 3, 3, NULL, 110, NULL, 'mdt:case:task:register', 'caseTaskRegister', 1, '2022-02-10 15:53:13', 1, '2024-07-05 15:39:37', 1);

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (11031200, 11030000, '案件失联修复', '1', 3, 3, NULL, 120, NULL, 'mdt:case:contact:restoration', 'caseContactRestoration', 1, '2022-02-10 15:53:13', 1, '2024-07-05 15:39:37', 1);

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (11031300, 11030000, '案件号码检测', '1', 3, 3, NULL, 130, NULL, 'mdt:case:number:test', 'caseNumberTest', 1, '2022-02-10 15:53:13', 1, '2024-07-05 15:39:37', 1);

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (11031400, 11030000, '案件短信发送', '1', 3, 3, NULL, 140, NULL, 'mdt:case:SMS:send', 'caseSMSSend', 1, '2022-02-10 15:53:13', 1, '2024-07-05 15:39:37', 1);

DELETE FROM sys_menu WHERE menu_id = 10030400;

UPDATE sys_dict SET sys_flag = 0 WHERE dict_id = 21;

ALTER TABLE `sys_dict_data`
MODIFY COLUMN `sys_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统字典字段;0非系统字典字段，1系统字典字段' AFTER `updater_id`;