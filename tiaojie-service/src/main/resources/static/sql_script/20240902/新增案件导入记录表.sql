CREATE TABLE `mdt_case_import_record` (
  `case_import_id` bigint NOT NULL COMMENT '案件导入ID',
  `entrusts_id` bigint DEFAULT NULL COMMENT '案件委托方ID',
  `entrusts_dept_id` bigint NOT NULL DEFAULT '-1' COMMENT '案源方部门id',
  `deputy_tmpl_id` bigint DEFAULT NULL COMMENT '导入模板ID',
  `complete_review` tinyint NOT NULL DEFAULT '1' COMMENT '是否办结审核 0否，1是',
  `mediate_begin_time` datetime DEFAULT NULL COMMENT '案件开始时间',
  `mediate_end_time` datetime DEFAULT NULL COMMENT '案件到期时间',
  `date_config_type` tinyint DEFAULT NULL COMMENT '时限设置方式(1:模板导入;2:页面设置)',
  `imp_flag` tinyint DEFAULT NULL COMMENT '操作类型(1:新增；2:更新)',
  `import_account_id` bigint DEFAULT NULL COMMENT '导入人ID',
  `import_file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源上传excel文件名',
  `import_file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源上传excel文件路径',
  `error_mark_file_path` varchar(255) DEFAULT NULL COMMENT '错误内容标记的excel路径',
  `total_mark_file_path` varchar(255) DEFAULT NULL COMMENT '全部内容标记的excel路径',
  `total_cases` int DEFAULT NULL COMMENT '总案件数',
  `failed_cases` int DEFAULT NULL COMMENT '导入失败的案件数',
  `success_cases` int DEFAULT NULL COMMENT '导入成功的案件数',
  `import_status` tinyint DEFAULT NULL COMMENT '导入状态 -1系统异常 1正在导入 2成功 3部分成功 4失败',
  `is_ignore_error` tinyint(1) DEFAULT NULL COMMENT '是否，继续导入有效数据（忽略错误）',
  `creator_id` bigint NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`case_import_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='案件导入记录表';


INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (13010800, 13010000, '案件导入记录', '1', 1, 3, NULL, 80, NULL, NULL, 'caseImportRecord', 1, '2022-02-10 15:53:13', 1, '2024-09-02 14:32:50', 1);
