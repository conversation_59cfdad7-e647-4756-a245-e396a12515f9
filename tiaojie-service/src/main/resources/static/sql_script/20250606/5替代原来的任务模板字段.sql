-- 删除原来该模块下的所有字段
DELETE FROM `tmpl_basis_module_field` WHERE `basis_module_id` = 4;

-- 删除该模块的副模板映射
DELETE dtm
FROM deputy_tmpl_mapping dtm
JOIN tmpl_case_module_field tcmf ON dtm.tmpl_module_field_id = tcmf.tmpl_module_field_id
JOIN tmpl_case_module tcm ON tcmf.tmpl_module_id = tcm.tmpl_module_id
WHERE tcm.basis_module_id = 4;

-- 删除该模块的主模板字段
DELETE tcmf FROM tmpl_case_module_field tcmf
JOIN tmpl_case_module tcm ON tcmf.tmpl_module_id = tcm.tmpl_module_id
WHERE tcm.basis_module_id = 4;

-- 更新原模块对应的数据表名
UPDATE `tmpl_basis_module` SET `table_name` = 'task_instance' WHERE `basis_module_id` = 4;
UPDATE `tmpl_case_module` SET `table_name` = 'task_instance' WHERE `basis_module_id` = 4;

-- 任务实例表字段配置
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'task_type', '任务类型', '任务的分类类型', 5, NULL, 'system_dict', NULL, 'task_type', 0, 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'time_limit', '任务时限', '任务完成时限(小时)', 6, NULL, 'number', NULL, NULL, 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'deadline', '截止时间', '任务必须完成的最后期限', 7, NULL, 'datetime', NULL, NULL, 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'manager_id_list', '任务负责人列表', '负责该任务的人员account_id集合', 8, NULL, 'varchar', '{\"type\":\"multiple_foreign_key\",\"tableName\":\"auth_employee\",\"keyFieldName\":\"account_id\",\"valueFieldName\":\"employee_name\"}', NULL, 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'task_status', '任务状态', '当前任务的执行状态', 9, NULL, 'system_dict', NULL, 'task_status', 0, 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'remark', '备注', '任务相关的备注信息', 10, NULL, 'varchar', NULL, NULL, 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'assign_account_id', '发起人', '任务发起人账户ID', 11, NULL, 'varchar', '{\"type\":\"foreign_key\",\"tableName\":\"auth_employee\",\"keyFieldName\":\"account_id\",\"valueFieldName\":\"employee_name\"}', NULL, 0, 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'assign_time', '发起时间', '任务发起的时间', 12, NULL, 'datetime', NULL, NULL, 0, 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'finish_account_id', '完成人', '任务完成人账户ID', 13, NULL, 'varchar', '{\"type\":\"foreign_key\",\"tableName\":\"auth_employee\",\"keyFieldName\":\"account_id\",\"valueFieldName\":\"employee_name\"}', NULL, 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tiaojie`.`tmpl_basis_module_field` ( `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES ( 4, 'finish_time', '完成时间', '任务完成的时间', 14, NULL, 'datetime', NULL, NULL, 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


-- 同步某个baiss模块的所有 字段到 case_feld 中，如果存在就不新建

INSERT INTO tmpl_case_module_field (
    tmpl_module_id,
    tmpl_id,
    basis_module_field_id,
    field_name,
    field_title,
    field_desc,
    field_sn,
    field_ui,
    field_data_type,
    field_values,
    field_dict_type,
    is_validated,
    is_require,
    is_approval_validated,
    is_update,
    is_disable,
    is_desensitize,
    desensitize_rule,
    validation,
    file_semantics,
    show_format,
    original_name,
    create_time,
    creator_id,
    update_time,
    updater_id
)
SELECT
    m.tmpl_module_id,
    m.tmpl_id,
    f.basis_module_field_id,
    f.field_name,
    f.field_title,
    f.field_desc,
    f.field_sn,
    f.field_ui,
    f.field_data_type,
    f.field_values,
    f.field_dict_type,
    f.is_validated,
    f.is_require,
    f.is_approval_validated,
    f.is_update,
    f.is_disable,
    f.is_desensitize,
    f.desensitize_rule,
    f.validation,
    f.file_semantics,
    f.show_format,
    f.field_name AS original_name,
    NOW(), 1, NOW(), 1
FROM
    tmpl_case_module m
        JOIN tmpl_basis_module_field f ON m.basis_module_id = f.basis_module_id
        LEFT JOIN tmpl_case_module_field cf
                  ON cf.tmpl_module_id = m.tmpl_module_id AND cf.basis_module_field_id = f.basis_module_field_id
WHERE
    m.basis_module_id = 4
  AND cf.tmpl_module_field_id IS NULL;