
CREATE TABLE `tiaojie`.`task_workflow`  (
                                            `workflow_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '流程ID',
                                            `workflow_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流程名称',
                                            `entrusts_id` bigint NOT NULL COMMENT '案源方ID',
                                            `entrusts_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '案源方名称',
                                            `template_id` bigint NULL DEFAULT NULL COMMENT '案件模板ID',
                                            `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '案件模板名称',
                                            `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
                                            `trigger_condition` tinyint DEFAULT NULL COMMENT '触发条件(1:案件导入 2:案件分派)',
                                            `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                            `creator_id` bigint NOT NULL COMMENT '创建人',
                                            `updater_id` bigint DEFAULT NULL COMMENT '更新人',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                            PRIMARY KEY (`workflow_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务流程主表' ROW_FORMAT = Dynamic;


CREATE TABLE `tiaojie`.`task_config`  (
                                          `config_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
                                          `workflow_id` bigint NOT NULL COMMENT '流程ID',
                                          `task_type` tinyint NOT NULL COMMENT '任务类型(对应sys_dict_data type = task_type)',
                                          `time_limit` int NULL DEFAULT NULL COMMENT '任务时限(小时)',
                                          `manager_id_list` json NULL COMMENT '负责人accountId集合',
                                          PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务配置表' ROW_FORMAT = Dynamic;

CREATE TABLE `tiaojie`.`task_trigger_condition_group`  (
                                                           `group_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '条件组ID',
                                                           `config_id` bigint NOT NULL COMMENT '配置ID',
                                                           `parent_group_id` bigint NULL DEFAULT NULL COMMENT '父组ID',
                                                           `logic_operator` tinyint NULL DEFAULT NULL COMMENT '组间逻辑运算符(1:AND 2:OR)',
                                                           `sort_order` int NOT NULL COMMENT '排序序号',
                                                           PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '触发条件组表' ROW_FORMAT = Dynamic;

CREATE TABLE `tiaojie`.`task_trigger_condition`  (
                                                     `condition_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '条件项ID',
                                                     `group_id` bigint UNSIGNED NOT NULL COMMENT '组ID',
                                                     `tmpl_module_id` bigint NOT NULL COMMENT '模板模块id',
                                                     `tmpl_module_field_id` bigint NOT NULL COMMENT '模板字段id',
                                                     `operator` tinyint NOT NULL COMMENT '运算符(1:有值 2:等于 3:包含)',
                                                     `target_value` varchar(300) NULL DEFAULT NULL COMMENT '目标值(字符串或字典值)只会是单个值',
                                                     `logic_operator` tinyint NULL COMMENT '行间逻辑运算符(1:AND 2:OR)',
                                                     PRIMARY KEY (`condition_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '触发条件项表' ROW_FORMAT = Dynamic;



CREATE TABLE `tiaojie`.`task_instance`  (
                                            `instance_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '实例ID',
                                            `case_id` bigint NOT NULL COMMENT '案件ID',
                                            `workflow_id` bigint COMMENT '流程ID',
                                            `config_id` bigint COMMENT '配置ID',
                                            `task_type` tinyint NOT NULL COMMENT '任务类型',
                                            `time_limit` int NULL DEFAULT NULL COMMENT '任务时限(小时)',
                                            `deadline` datetime NULL COMMENT '截至时间',
                                            `manager_id_list` json NULL COMMENT '任务负责人acount_id集合',
                                            `task_status` tinyint NOT NULL DEFAULT 1 COMMENT '当前状态(1:未开始(暂不存在) 2:进行中 3:已完成 4:异常)',
                                            `remark` varchar(500) NULL COMMENT '备注',
                                            `assign_account_id` bigint NULL COMMENT '发起人id',
                                            `assign_time` datetime  NULL COMMENT '发起时间',
                                            `finish_account_id` bigint NULL COMMENT '完成人id',
                                            `finish_time` datetime NULL COMMENT '完成时间',
                                            `creator_id` bigint NOT NULL COMMENT '创建人',
                                            `updater_id` bigint NULL COMMENT '更新人',
                                            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                            `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                            PRIMARY KEY (`instance_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务实例表' ROW_FORMAT = Dynamic;
