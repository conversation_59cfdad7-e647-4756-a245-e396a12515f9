
-- 创建字典类型
INSERT INTO `sys_dict` (`dict_type`, `dict_name`, `dict_status`, `sys_flag`, `remark`, `creator_id`, `updater_id`)
VALUES ('task_type', '任务类型', 1, 0, '', 1, 1);

-- 获取字典ID
SET @dict_id = (SELECT dict_id FROM sys_dict WHERE dict_type = 'task_type');

-- 插入字典数据
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES
    (NULL, @dict_id, 1, '送达', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 2, '触达', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 3, '失联修复', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 4, '排期', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 5, '审评鉴', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 6, '结案', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 7, '文书', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 8, '归档', 1, '', 1, 1, NULL);



-- 插入字典类型
INSERT INTO `sys_dict` (`dict_type`, `dict_name`, `dict_status`, `sys_flag`, `remark`, `creator_id`, `updater_id`)
VALUES ('task_status', '任务状态', 1, 0, '', 1, 1);

-- 获取 dict_id
SET @dict_id = (SELECT dict_id FROM sys_dict WHERE dict_type = 'task_status');

-- 插入字典数据
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES (NULL, @dict_id, 1, '待处理', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 2, '进行中', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 3, '已完成（正常）', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 4, '已完成（异常）', 1, '', 1, 1, NULL);
