INSERT INTO tiaojie.task_instance (
    case_id, workflow_id, config_id, task_type, time_limit, deadline, manager_id_list, task_status, remark,
    assign_account_id, assign_time, finish_account_id, finish_time, creator_id, updater_id, create_time, update_time
)
SELECT
    case_id,
    NULL AS workflow_id,
    NULL AS config_id,
    1 AS task_type, -- 送达
    NULL AS time_limit,
    NULL AS deadline,
    CASE
        WHEN delivered_task_account_id IS NOT NULL THEN CONCAT('[', CAST(delivered_task_account_id AS CHAR), ']')
        ELSE NULL
        END AS manager_id_list,
    delivered_task_status,
    NULL AS remark,
    delivered_task_account_id,
    delivered_task_assigned_time,
    CASE WHEN delivered_task_status = 3 THEN delivered_task_account_id ELSE NULL END,
    CASE WHEN delivered_task_status = 3 THEN delivered_task_done_time ELSE NULL END,
    COALESCE(creator_id, 1),
    updater_id,
    COALESCE(create_time, NOW()),
    update_time
FROM tiaojie.mdt_task_complete_register t
WHERE
    delivered_task_status IS NOT NULL
  AND EXISTS (SELECT 1 FROM tiaojie.mdt_case c WHERE c.case_id = t.case_id)

UNION ALL

SELECT
    case_id,
    NULL,
    NULL,
    2,
    NULL,
    NULL,
    CASE
        WHEN reach_task_account_id IS NOT NULL THEN CONCAT('[', CAST(reach_task_account_id AS CHAR), ']')
        ELSE NULL
        END,
    reach_task_status,
    NULL,
    reach_task_account_id,
    reach_task_assigned_time,
    CASE WHEN reach_task_status = 3 THEN reach_task_account_id ELSE NULL END,
    CASE WHEN reach_task_status = 3 THEN reach_task_done_time ELSE NULL END,
    COALESCE(creator_id, 1),
    updater_id,
    COALESCE(create_time, NOW()),
    update_time
FROM tiaojie.mdt_task_complete_register t
WHERE
    reach_task_status IS NOT NULL
  AND EXISTS (SELECT 1 FROM tiaojie.mdt_case c WHERE c.case_id = t.case_id)

UNION ALL

SELECT
    case_id,
    NULL,
    NULL,
    3,
    NULL,
    NULL,
    CASE
        WHEN repair_task_account_id IS NOT NULL THEN CONCAT('[', CAST(repair_task_account_id AS CHAR), ']')
        ELSE NULL
        END,
    repair_task_status,
    NULL,
    repair_task_account_id,
    repair_task_assigned_time,
    CASE WHEN repair_task_status = 3 THEN repair_task_account_id ELSE NULL END,
    CASE WHEN repair_task_status = 3 THEN repair_task_done_time ELSE NULL END,
    COALESCE(creator_id, 1),
    updater_id,
    COALESCE(create_time, NOW()),
    update_time
FROM tiaojie.mdt_task_complete_register t
WHERE
    repair_task_status IS NOT NULL
  AND EXISTS (SELECT 1 FROM tiaojie.mdt_case c WHERE c.case_id = t.case_id)

UNION ALL

SELECT
    case_id,
    NULL,
    NULL,
    4,
    NULL,
    NULL,
    CASE
        WHEN scheduling_task_account_id IS NOT NULL THEN CONCAT('[', CAST(scheduling_task_account_id AS CHAR), ']')
        ELSE NULL
        END,
    scheduling_task_status,
    NULL,
    scheduling_task_account_id,
    scheduling_task_assigned_time,
    CASE WHEN scheduling_task_status = 3 THEN scheduling_task_account_id ELSE NULL END,
    CASE WHEN scheduling_task_status = 3 THEN scheduling_task_done_time ELSE NULL END,
    COALESCE(creator_id, 1),
    updater_id,
    COALESCE(create_time, NOW()),
    update_time
FROM tiaojie.mdt_task_complete_register t
WHERE
    scheduling_task_status IS NOT NULL
  AND EXISTS (SELECT 1 FROM tiaojie.mdt_case c WHERE c.case_id = t.case_id)

UNION ALL

SELECT
    case_id,
    NULL,
    NULL,
    5,
    NULL,
    NULL,
    CASE
        WHEN evaluation_task_account_id IS NOT NULL THEN CONCAT('[', CAST(evaluation_task_account_id AS CHAR), ']')
        ELSE NULL
        END,
    evaluation_task_status,
    NULL,
    evaluation_task_account_id,
    evaluation_task_assigned_time,
    CASE WHEN evaluation_task_status = 3 THEN evaluation_task_account_id ELSE NULL END,
    CASE WHEN evaluation_task_status = 3 THEN evaluation_task_done_time ELSE NULL END,
    COALESCE(creator_id, 1),
    updater_id,
    COALESCE(create_time, NOW()),
    update_time
FROM tiaojie.mdt_task_complete_register t
WHERE
    evaluation_task_status IS NOT NULL
  AND EXISTS (SELECT 1 FROM tiaojie.mdt_case c WHERE c.case_id = t.case_id)

UNION ALL

SELECT
    case_id,
    NULL,
    NULL,
    6,
    NULL,
    NULL,
    CASE
        WHEN closed_task_account_id IS NOT NULL THEN CONCAT('[', CAST(closed_task_account_id AS CHAR), ']')
        ELSE NULL
        END,
    closed_task_status,
    NULL,
    closed_task_account_id,
    closed_task_assigned_time,
    CASE WHEN closed_task_status = 3 THEN closed_task_account_id ELSE NULL END,
    CASE WHEN closed_task_status = 3 THEN closed_task_done_time ELSE NULL END,
    COALESCE(creator_id, 1),
    updater_id,
    COALESCE(create_time, NOW()),
    update_time
FROM tiaojie.mdt_task_complete_register t
WHERE
    closed_task_status IS NOT NULL
  AND EXISTS (SELECT 1 FROM tiaojie.mdt_case c WHERE c.case_id = t.case_id)
;