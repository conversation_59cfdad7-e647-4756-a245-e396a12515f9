INSERT INTO deputy_tmpl_mapping (
    deputy_tmpl_id,
    field_name,
    tmpl_module_field_id,
    field_sn,
    converter,
    create_time,
    creator_id,
    update_time,
    updater_id
)
SELECT
    dt.deputy_tmpl_id,
    tcmf.field_title,
    tcmf.tmpl_module_field_id,
    tcmf.field_sn,
    NULL,        -- converter 可用默认NULL
    NOW(),
    1,           -- create人
    NOW(),
    1            -- update人
FROM deputy_tmpl dt
         JOIN tmpl_case_module tcm
              ON tcm.basis_module_id = 4
                  AND tcm.tmpl_id = dt.tmpl_id
         JOIN tmpl_case_module_field tcmf
              ON tcmf.tmpl_module_id = tcm.tmpl_module_id
WHERE dt.tmpl_type = 4
  AND NOT EXISTS (
    SELECT 1 FROM deputy_tmpl_mapping dtm
    WHERE dtm.deputy_tmpl_id = dt.deputy_tmpl_id
      AND dtm.tmpl_module_field_id = tcmf.tmpl_module_field_id
)
;