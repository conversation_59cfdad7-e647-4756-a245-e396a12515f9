-- 插入字典类型
INSERT INTO `sys_dict` (`dict_type`, `dict_name`, `dict_status`, `sys_flag`, `remark`, `creator_id`, `updater_id`)
VALUES ('litigant_contact_status', '当事人联系状态', 1, 0, '', 1, 1);

-- 获取 dict_id
SET @dict_id = (SELECT dict_id FROM sys_dict WHERE dict_type = 'litigant_contact_status');

-- 插入字典数据
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES (NULL, @dict_id, 1, '已接通', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 2, '未接通', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 3, '空号', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 4, '号码注销', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 5, '关机', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 6, '用户正忙', 1, '', 1, 1, NULL);



ALTER TABLE `tiaojie`.`mdt_case_litigant`
    ADD COLUMN `contact_status` tinyint NULL COMMENT '联系状态' AFTER `phone_status`;

-- 基础模块新增
SET @basis_module_field_id = 88;

DELETE FROM tmpl_basis_module_field where basis_module_field_id = @basis_module_field_id;
DELETE FROM tmpl_case_module_field where basis_module_field_id = @basis_module_field_id;

INSERT INTO `tmpl_basis_module_field` (`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (@basis_module_field_id, 1, 'contact_status', '联系状态', NULL, 17, NULL, 'system_dict', NULL, 'litigant_contact_status', 0, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

-- 同步到所有的案件模板字段中
INSERT INTO tmpl_case_module_field (
    tmpl_module_id,
    tmpl_id,
    basis_module_field_id,
    field_name,
    field_title,
    field_desc,
    field_sn,
    field_ui,
    field_data_type,
    field_dict_type,
    field_values,
    is_validated,
    is_require,
    is_update,
    is_disable,
    is_desensitize,
    original_name,
    desensitize_rule,
    validation,
    show_format,
    file_semantics,
    to_case_ext_field,
    component_relation,
    create_time,
    creator_id,
    update_time,
    updater_id
)
WITH MissingTmplModuleIds AS (
    SELECT tcm.tmpl_module_id, tcm.tmpl_id
    FROM tmpl_basis_module_field tbmf
             LEFT JOIN tmpl_case_module tcm ON tbmf.basis_module_id = tcm.basis_module_id
             LEFT JOIN tmpl_case_module_field tcmf ON tcm.tmpl_module_id = tcmf.tmpl_module_id  AND tbmf.basis_module_field_id = tcmf.basis_module_field_id
    WHERE
        tcmf.basis_module_field_id IS NULL
      AND tbmf.basis_module_field_id = @basis_module_field_id
), BasisField AS (
    SELECT *
    FROM tmpl_basis_module_field
    WHERE basis_module_field_id = @basis_module_field_id
)
SELECT
    m.tmpl_module_id,
    m.tmpl_id,
    bf.basis_module_field_id,
    bf.field_name,
    bf.field_title,
    bf.field_desc,
    bf.field_sn,
    bf.field_ui,
    bf.field_data_type,
    bf.field_dict_type,
    bf.field_values,
    bf.is_validated,
    bf.is_require,
    bf.is_update,
    bf.is_disable,
    bf.is_desensitize,
    NULL,
    bf.desensitize_rule,
    bf.validation,
    bf.show_format,
    bf.file_semantics,
    NULL,
    NULL,
    CURRENT_TIMESTAMP,
    1 AS creator_id,
    CURRENT_TIMESTAMP,
    1 AS updater_id
FROM MissingTmplModuleIds m
         CROSS JOIN BasisField bf;