CREATE TABLE `mdt_repair` (
  `repair_id` bigint NOT NULL COMMENT '批次Id',
  `account_id` bigint DEFAULT NULL COMMENT '发起人Id',
  `account_name` varchar(255) DEFAULT NULL COMMENT '发起人姓名',
  `company_id` bigint DEFAULT NULL COMMENT '发起组织Id',
  `company_name` varchar(255) DEFAULT NULL COMMENT '发起组织名称',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `repair_status` varchar(255) DEFAULT NULL COMMENT '批次修复状态（修复中，修复完成）',
  `repair_complete_time` datetime DEFAULT NULL COMMENT '修复完成时间',
  `is_batch` int DEFAULT NULL COMMENT '是否批量',
  PRIMARY KEY (`repair_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `mdt_repair_config` (
  `repair_config_id` bigint NOT NULL COMMENT '失联修复配置id',
  `company_id` bigint DEFAULT NULL COMMENT '组织id',
  `order_config` json DEFAULT NULL COMMENT '配置信息',
  `order_status` varchar(255) DEFAULT NULL COMMENT '1:单个修复，2：批量修复',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`repair_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `mdt_repair_litigant` (
  `repair_litigant_id` bigint NOT NULL COMMENT '修复当事人Id',
  `repair_id` bigint DEFAULT NULL COMMENT '批次Id',
  `case_id` bigint DEFAULT NULL COMMENT '案件Id',
  `litigant_id` bigint DEFAULT NULL COMMENT '当事人Id',
  `litigant_name` varchar(255) DEFAULT NULL COMMENT '当事人名称',
  `phone_number` varchar(255) DEFAULT NULL COMMENT '曾用电话',
  `id_card` varchar(255) DEFAULT NULL COMMENT '当事人身份证',
  `id_card_sha` varchar(255) DEFAULT NULL COMMENT '身份证sha256加密',
  `id_card_md` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '身份证MD5加密',
  `litigant_type` varchar(255) DEFAULT NULL COMMENT '当事人类型',
  `identity_type` varchar(255) DEFAULT NULL COMMENT '当事人身份',
  `repair_status` varchar(255) DEFAULT NULL COMMENT '修复状态',
  `repair_result` varchar(255) DEFAULT NULL COMMENT '修复结果',
  `repair_success_net` varchar(255) DEFAULT NULL COMMENT '修复成功渠道',
  `repair_success_time` datetime DEFAULT NULL COMMENT '修复成功时间',
  `account_id` bigint DEFAULT NULL COMMENT '发起人id',
  `account_name` varchar(255) DEFAULT NULL COMMENT '发起人姓名',
  `company_id` bigint DEFAULT NULL COMMENT '发起组织Id',
  `company_name` varchar(255) DEFAULT NULL COMMENT '发起组织名称',
  PRIMARY KEY (`repair_litigant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `mdt_repair_phone` (
  `repair_phone_id` bigint NOT NULL COMMENT '获取号码记录id',
  `repair_id` bigint DEFAULT NULL COMMENT '批次id',
  `repair_litigant_id` bigint DEFAULT NULL COMMENT '修复当事人Id',
  `repair_record_id` bigint DEFAULT NULL COMMENT '修复记录id',
  `case_id` bigint DEFAULT NULL COMMENT '案件id',
  `litigant_id` bigint DEFAULT NULL COMMENT '当事人id',
  `call_no` varchar(255) DEFAULT NULL COMMENT '主叫号',
  `virtual_no` varchar(255) DEFAULT NULL COMMENT '虚拟号',
  `bind_id` varchar(255) DEFAULT NULL COMMENT '绑定标识',
  `virtual_id` varchar(255) DEFAULT NULL COMMENT '(云宝宝)虚拟id',
  `acquire_result` varchar(255) DEFAULT NULL COMMENT '获取结果',
  `acquire_status` varchar(255) DEFAULT NULL COMMENT '获取状态',
  `number_acquire_time` datetime DEFAULT NULL COMMENT '号码获取时间',
  `fail_code` varchar(255) DEFAULT NULL COMMENT '失败错误码',
  `fail_msg` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `number_expire_time` datetime DEFAULT NULL COMMENT '号码过期时间',
  PRIMARY KEY (`repair_phone_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `mdt_repair_record` (
  `repair_record_id` bigint NOT NULL COMMENT '修复记录id',
  `repair_id` bigint DEFAULT NULL COMMENT '批次Id',
  `repair_litigant_id` bigint DEFAULT NULL COMMENT '修复当事人Id',
  `case_id` bigint DEFAULT NULL COMMENT '案件Id',
  `litigant_id` bigint DEFAULT NULL COMMENT '当事人Id',
  `task_no` varchar(255) DEFAULT NULL COMMENT '批次号',
  `req_id` varchar(255) DEFAULT NULL COMMENT '(云宝宝)修复客户请求Id',
  `repair_net` int DEFAULT NULL COMMENT '修复渠道',
  `mask_model` varchar(255) DEFAULT NULL COMMENT '加密方式',
  `repair_status` varchar(255) DEFAULT NULL COMMENT '修复状态',
  `repair_result` varchar(255) DEFAULT NULL COMMENT '修复结果',
  `repair_fail_code` varchar(255) DEFAULT NULL COMMENT '修复失败错误码',
  `repair_fail_msg` varchar(255) DEFAULT NULL COMMENT '修复失败错误信息',
  `phone_list` json DEFAULT NULL COMMENT '（全网、异网、电信)修复成功后的号码列表',
  `result_id` varchar(255) DEFAULT NULL COMMENT '(全网、异网)结果id',
  `phone_number` int DEFAULT NULL COMMENT '修复成功号码数量',
  `repair_complete_time` datetime DEFAULT NULL COMMENT '修复完成时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `upload_request_body` json DEFAULT NULL COMMENT '上传失联修复数据原样记录',
  `next_order` int DEFAULT NULL COMMENT '下一个执行失联修复渠道',
  `company_id` bigint DEFAULT NULL COMMENT '发起组织id',
  `account_id` bigint DEFAULT NULL COMMENT '发起人id',
  PRIMARY KEY (`repair_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;