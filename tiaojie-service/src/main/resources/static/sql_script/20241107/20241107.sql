INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_SETTING', 'SETTING_SERVICE_PASSWORD_DEFAULT', '初始密码', 'XingChuan_2024!', '新建账号的初始密码', '2024-09-20 10:50:14', 1, '2024-10-17 14:24:15', 1);



ALTER TABLE `mdt_sms_template`
ADD COLUMN `sms_sign` varchar(255) NULL COMMENT '短信签名' AFTER `template_params`,
ADD COLUMN `scene` varchar(255) NULL COMMENT '所属场景' AFTER `sms_sign`,
ADD COLUMN `channel` varchar(255) NULL COMMENT '所属渠道' AFTER `scene`,
ADD COLUMN `dept_id_list` json NULL COMMENT '可见部门' AFTER `channel`,
ADD COLUMN `role_name_list` json NULL COMMENT '可见角色' AFTER `dept_id_list`;

ALTER TABLE `mdt_sms_template`
ADD COLUMN `template_type` varchar(255) NULL COMMENT '模板类型' AFTER `template_params`;