ALTER TABLE `mdt_case_custom_data`
DROP INDEX `c_t_id`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `delivered_task_assigned_time` datetime NULL COMMENT '送达任务分派时间' AFTER `delivered_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `delivered_task_start_time` datetime NULL COMMENT '送达任务开始时间' AFTER `delivered_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `delivered_task_done_time` datetime NULL COMMENT '送达任务完成时间' AFTER `delivered_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `scheduling_task_assigned_time` datetime NULL COMMENT '排期任务分派时间' AFTER `scheduling_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `scheduling_task_start_time` datetime NULL COMMENT '排期任务开始时间' AFTER `scheduling_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `scheduling_task_done_time` datetime NULL COMMENT '排期任务完成时间' AFTER `scheduling_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `evaluation_task_assigned_time` datetime NULL COMMENT '审评鉴分派时间' AFTER `evaluation_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `evaluation_task_start_time` datetime NULL COMMENT '审评鉴开始时间' AFTER `evaluation_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `evaluation_task_done_time` datetime NULL COMMENT '审评鉴完成时间' AFTER `evaluation_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `closed_task_assigned_time` datetime NULL COMMENT '结案任务分派时间' AFTER `closed_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `closed_task_start_time` datetime NULL COMMENT '结案任务开始时间' AFTER `closed_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `closed_task_done_time` datetime NULL COMMENT '结案任务完成时间' AFTER `closed_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `reach_task_assigned_time` datetime NULL COMMENT '案件触达分派时间' AFTER `reach_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `reach_task_start_time` datetime NULL COMMENT '案件触达开始时间' AFTER `reach_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `reach_task_done_time` datetime NULL COMMENT '案件触达完成时间' AFTER `reach_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `repair_task_assigned_time` datetime NULL COMMENT '信修任务分派时间' AFTER `repair_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `repair_task_start_time` datetime NULL COMMENT '信修任务开始时间' AFTER `repair_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `repair_task_done_time` datetime NULL COMMENT '信修任务完成时间' AFTER `repair_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `mediate_task_assigned_time` datetime NULL COMMENT '调解任务分派时间' AFTER `mediate_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `mediate_task_start_time` datetime NULL COMMENT '调解任务开始时间' AFTER `mediate_task_status`;
ALTER TABLE `mdt_task_complete_register` ADD COLUMN `mediate_task_done_time` datetime NULL COMMENT '调解任务完成时间' AFTER `mediate_task_status`;