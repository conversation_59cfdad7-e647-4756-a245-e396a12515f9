-- 失联修复脚本 --
ALTER TABLE `mdt_contact_repair`
ADD COLUMN `repair_net_status` int(11) DEFAULT 1 COMMENT '修复网络号(1.全网2.异网)' AFTER `repair_time`;

ALTER TABLE `mdt_contact_repair_record` ADD COLUMN `litigant_id` bigint NOT NULL COMMENT '当事人ID';

ALTER TABLE `mdt_contact_repair` ADD COLUMN `company_id` bigint NOT NULL COMMENT '组织ID';

UPDATE mdt_contact_repair_record a JOIN mdt_case_litigant b ON b.case_id = a.case_id AND b.id_no = a.id_card SET a.litigant_id = b.litigant_id;


UPDATE mdt_contact_repair a JOIN auth_employee b ON b.account_id = a.user_id SET a.company_id = b.company_id;

TRUNCATE TABLE sys_log_case;

ALTER TABLE `sys_log_case`
MODIFY COLUMN `log_case_id` bigint(0) NOT NULL AUTO_INCREMENT FIRST;

ALTER TABLE `mdt_contact_repair_record`
ADD COLUMN `record_repair_status` int(11) NOT NULL DEFAULT 1 COMMENT '记录修复状态(1.待修复2.修复中3.修复完成)' AFTER `repair_virtual_no_time`;