
-- 案件模板 同步
UPDATE tmpl_basis_module_field SET field_data_type='system_dict',field_values=NULL WHERE field_name = 'identity_type';
UPDATE tmpl_case_module_field SET field_data_type='system_dict',field_values=NULL WHERE field_name = 'identity_type';


UPDATE tmpl_basis_module_field SET field_dict_type='litigant_identity_type',field_values=NULL WHERE field_name = 'identity_type';
UPDATE tmpl_case_module_field SET field_dict_type='litigant_identity_type',field_values=NULL WHERE field_name = 'identity_type';



-- 创建字典类型
INSERT INTO `sys_dict` (`dict_type`, `dict_name`, `dict_status`, `sys_flag`, `remark`, `creator_id`, `updater_id`)
VALUES ('litigant_identity_type', '当事人身份类型', 1, 0, '', 1, 1);

-- 获取字典ID
SET @dict_id = (SELECT dict_id FROM sys_dict WHERE dict_type = 'litigant_identity_type');

-- 插入字典数据
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES
    (NULL, @dict_id, 1, '申请人', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 2, '被申请人', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 3, '申请人代理', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 4, '被申请人代理', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 5, '第三人', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 6, '申请执行人', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 7, '被执行人', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 8, '案外人', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 9, '抵押权人', 1, '', 1, 1, NULL),
    (NULL, @dict_id, 10, '普通债权人', 1, '', 1, 1, NULL);


