ALTER TABLE `mdt_case_import_record`
    ADD COLUMN `business_type` bigint NULL COMMENT '业务类型' AFTER `case_mediate_type`;

ALTER TABLE `mdt_case_import_record`
    ADD COLUMN `is_desensitize` tinyint(1) NULL COMMENT '是否自动脱敏' AFTER `business_type`;

-- 案件信息表以及审批表增加字段

ALTER TABLE `tiaojie`.`mdt_case`
    MODIFY COLUMN `close_time` datetime NULL DEFAULT NULL COMMENT '调解办结发起时间(原结案时间)' AFTER `updater_id`,
    ADD COLUMN `case_manage_close_reason` varchar(50) NULL COMMENT '案管办结原因' AFTER `close_time`,
    ADD COLUMN `suspend_reason` varchar(50) NULL COMMENT '中止原因' AFTER `case_manage_close_reason`,
    ADD COLUMN `case_manage_close_time` datetime NULL COMMENT '案管办结发起时间' AFTER `suspend_reason`;



ALTER TABLE `tiaojie`.`mdt_case_approval`
    MODIFY COLUMN `case_close_time` datetime NULL DEFAULT NULL COMMENT '（调解/案管）办结发起时间' AFTER `mdt_result`,
    ADD COLUMN `case_manage_close_reason` varchar(50) NULL COMMENT '案管办结原因' AFTER `close_reason`,
    ADD COLUMN `suspend_reason` varchar(50) NULL COMMENT '中止原因' AFTER `case_manage_close_reason`;


-- 更新当前系统中的案件业务类型
-- 非集团默认就是政务
UPDATE `mdt_case` set `business_type` =1 where `entrusts_id` !=1276017123935784960;
UPDATE `mdt_case` set `business_type` =8 where tmpl_case_id = 77;
UPDATE `mdt_case` set `business_type` =4 where tmpl_case_id = 67;
UPDATE `mdt_case` set `business_type` =3 where tmpl_case_id = 57;
UPDATE `mdt_case` set `business_type` =2 where tmpl_case_id = 76;



-- 导出模板增加字段

-- 更新为系统字典
UPDATE tmpl_basis_module_field set field_data_type='system_dict',field_dict_type = 'case_success_reason' where `basis_module_field_id` = 45;
UPDATE tmpl_case_module_field set field_data_type='system_dict',field_dict_type = 'case_success_reason' where `basis_module_field_id` = 45;


-- 基础模块新增
INSERT INTO `tmpl_basis_module_field` (`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (90, 1, 'case_manage_close_reason', '案管办结发起时间', NULL, 13, NULL, 'datetime', NULL, NULL, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tmpl_basis_module_field` (`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (91, 1, 'case_close_time', '案管办结原因', NULL, 13, NULL, 'system_dict', NULL, 'case_manage_close_reason', 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `tmpl_basis_module_field` (`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `field_dict_type`, `is_validated`, `is_require`, `is_approval_validated`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (92, 1, 'suspend_reason', '中止原因', NULL, 13, NULL, 'system_dict', NULL, 'case_suspend_reason', 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);



-- 同步到所有的案件模板字段中
INSERT INTO tmpl_case_module_field (
    tmpl_module_id,
    tmpl_id,
    basis_module_field_id,
    field_name,
    field_title,
    field_desc,
    field_sn,
    field_ui,
    field_data_type,
    field_values,
    is_validated,
    is_require,
    is_update,
    is_disable,
    is_desensitize,
    original_name,
    desensitize_rule,
    validation,
    show_format,
    file_semantics,
    to_case_ext_field,
    component_relation,
    create_time,
    creator_id,
    update_time,
    updater_id
)
WITH MissingTmplModuleIds AS (
    SELECT tcm.tmpl_module_id, tcm.tmpl_id
    FROM tmpl_basis_module_field tbmf
             LEFT JOIN tmpl_case_module tcm ON tbmf.basis_module_id = tcm.basis_module_id
             LEFT JOIN tmpl_case_module_field tcmf ON tcm.tmpl_module_id = tcmf.tmpl_module_id  AND tbmf.basis_module_field_id = tcmf.basis_module_field_id
    WHERE
        tcmf.basis_module_field_id IS NULL
      AND tbmf.basis_module_field_id in (90,91,92)
), BasisField AS (
    SELECT *
    FROM tmpl_basis_module_field
    WHERE basis_module_field_id in (90,91,92)
)
SELECT
    m.tmpl_module_id AS tmpl_module_id,
    m.tmpl_id AS tmpl_id,
    bf.basis_module_field_id AS basis_module_field_id,
    bf.field_name AS field_name,
    bf.field_title AS field_title,
    bf.field_desc AS field_desc,
    bf.field_sn AS field_sn,
    bf.field_ui AS field_ui,
    bf.field_data_type AS field_data_type,
    bf.field_values AS field_values,
    bf.is_validated AS is_validated,
    bf.is_require AS is_require,
    bf.is_update AS is_update,
    bf.is_disable AS is_disable,
    bf.is_desensitize AS is_desensitize,
    NULL AS original_name,
    bf.desensitize_rule AS desensitize_rule,
    bf.validation AS validation,
    bf.show_format AS show_format,
    bf.file_semantics AS file_semantics,
    NULL AS to_case_ext_field,
    NULL AS component_relation,
    CURRENT_TIMESTAMP AS create_time,
    1  AS creator_id,
    CURRENT_TIMESTAMP AS update_time,
    1   AS updater_id
FROM MissingTmplModuleIds m
         CROSS JOIN BasisField bf;