# 案件新增业务类型


ALTER TABLE `mdt_case` ADD COLUMN `business_type` varchar(50) NULL  COMMENT '业务类型(1:政务,2:金融)' AFTER `mediate_close_time`;


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (73, 3, 'business_type', '业务类型', NULL, 27, NULL, 'dict'
,'{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"政务\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"金融\\\"}]\",\"name\":\"CaseBusinessTypeStatus\"}'
, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

# 成功原因备注为结案方式

ALTER TABLE mdt_case MODIFY COLUMN success_reason VARCHAR(255) COMMENT '成功原因(界面显示为：结案方式)';

# 案件更新权限

UPDATE `zd_mediate_xingchuan`.`sys_menu` SET `menu_route_name` = 'caseUpdate' WHERE `menu_id` = 11010700;

# 批量办结审批权限

INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (11011300, 11010000, '批量办结申请', '1', 3, 3, NULL, 130, NULL, 'mdt:case:approval:batchApproval', 'batchApproval', 1, '2022-11-11 15:53:13', 1, '2024-11-11 11:29:02', 1);


# 成功原因模板字段改为结案方式
UPDATE tmpl_basis_module_field SET field_title = '结案方式' WHERE basis_module_field_id = 46;
UPDATE tmpl_case_module_field SET field_title = '结案方式' WHERE basis_module_field_id = 46;

# 结案方式与失败原因模板修改

ALTER TABLE tmpl_basis_module_field MODIFY field_values VARCHAR(1000);
ALTER TABLE tmpl_case_module_field MODIFY field_values VARCHAR(1000);

UPDATE `tmpl_basis_module_field` SET `field_values` = '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"提起诉讼\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"当事人拒绝调解\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"当事人申请保全\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"双方意愿距过大\\\"},{\\\"key\\\":5,\\\"value\\\":\\\"要求行政部门处理\\\"},{\\\"key\\\":6,\\\"value\\\":\\\"调解期限届满\\\"},{\\\"key\\\":7,\\\"value\\\":\\\"其他\\\"},{\\\"key\\\":201,\\\"value\\\":\\\"案件事实有争议\\\"},{\\\"key\\\":202,\\\"value\\\":\\\"当事人失联\\\"},{\\\"key\\\":203,\\\"value\\\":\\\"当事人拒绝调解\\\"},{\\\"key\\\":204,\\\"value\\\":\\\"双方意愿差距过大\\\"},{\\\"key\\\":205,\\\"value\\\":\\\"分期违约\\\"},{\\\"key\\\":206,\\\"value\\\":\\\"调解期限届满退案\\\"},{\\\"key\\\":207,\\\"value\\\":\\\"客户要求退案\\\"}]\",\"name\":\"TaskStatusEnum\"}' WHERE `basis_module_field_id` = 44;
UPDATE tmpl_case_module_field SET `field_values` = '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"提起诉讼\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"当事人拒绝调解\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"当事人申请保全\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"双方意愿距过大\\\"},{\\\"key\\\":5,\\\"value\\\":\\\"要求行政部门处理\\\"},{\\\"key\\\":6,\\\"value\\\":\\\"调解期限届满\\\"},{\\\"key\\\":7,\\\"value\\\":\\\"其他\\\"},{\\\"key\\\":201,\\\"value\\\":\\\"案件事实有争议\\\"},{\\\"key\\\":202,\\\"value\\\":\\\"当事人失联\\\"},{\\\"key\\\":203,\\\"value\\\":\\\"当事人拒绝调解\\\"},{\\\"key\\\":204,\\\"value\\\":\\\"双方意愿差距过大\\\"},{\\\"key\\\":205,\\\"value\\\":\\\"分期违约\\\"},{\\\"key\\\":206,\\\"value\\\":\\\"调解期限届满退案\\\"},{\\\"key\\\":207,\\\"value\\\":\\\"客户要求退案\\\"}]\",\"name\":\"TaskStatusEnum\"}' WHERE `basis_module_field_id` = 44;

UPDATE `tmpl_basis_module_field` SET `field_values` = '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"不起诉\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"立案后撤诉\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"出具调解书\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"出确认裁定\\\"},{\\\"key\\\":5,\\\"value\\\":\\\"转民初出调解书\\\"},{\\\"key\\\":201,\\\"value\\\":\\\"一次性结清\\\"},{\\\"key\\\":202,\\\"value\\\":\\\"分期结清\\\"}]\",\"name\":\"TaskStatusEnum\"}' WHERE `basis_module_field_id` = 46;
UPDATE tmpl_case_module_field SET `field_values` = '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"不起诉\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"立案后撤诉\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"出具调解书\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"出确认裁定\\\"},{\\\"key\\\":5,\\\"value\\\":\\\"转民初出调解书\\\"},{\\\"key\\\":201,\\\"value\\\":\\\"一次性结清\\\"},{\\\"key\\\":202,\\\"value\\\":\\\"分期结清\\\"}]\",\"name\":\"TaskStatusEnum\"}' WHERE `basis_module_field_id` = 46;


# 单个案件创建权限修复

UPDATE sys_menu SET parent_menu_id = 11010000 where menu_id = 11040000;

