DELETE
FROM `tmpl_basis_module_field`
WHERE `basis_module_field_id` = 31;

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (32, 3, 'case_no', '案件编号', NULL, 1, NULL, 'varchar', NULL, 0, 1, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (33, 3, 'case_apply_time', '案件申请时间', NULL, 1, NULL, 'datetime', NULL, 0, 1, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (34, 3, 'mediate_begin_time', '调解开始时间', NULL, 1, NULL, 'datetime', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL,
        NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (35, 3, 'close_time', '调解结束时间', NULL, 1, NULL, 'datetime', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

ALTER TABLE `mdt_case`
    MODIFY COLUMN `case_name` varchar (100) NULL COMMENT '案件名称' AFTER `org_id`;


ALTER TABLE `mdt_case`
ADD COLUMN `case_apply_time` datetime NULL COMMENT '案件申请时间' AFTER `score`,
ADD COLUMN `mediate_begin_time` datetime NULL COMMENT '调解开始时间' AFTER `case_apply_time`;

ALTER TABLE `mdt_case`
    ADD COLUMN `entrusts_dept_id` bigint(20) NOT NULL DEFAULT -1 COMMENT '案源方部门id' AFTER `entrusts_id`;

ALTER TABLE `mdt_case`
    ADD COLUMN `complete_review` int(11) NOT NULL DEFAULT 1 COMMENT '办结审核';

UPDATE sys_menu SET enable_flag = 1 WHERE menu_id = 10030603;

ALTER TABLE `mdt_instruments`
    ADD COLUMN `create_type` varchar(255) NULL COMMENT '创建方式' AFTER `delete_flag`;


ALTER TABLE `tmpl_case_module`
    ADD COLUMN `mapping_type` varchar(255) NULL COMMENT '映射方式' AFTER `updater_id`;

ALTER TABLE `tmpl_basis_module`
    ADD COLUMN `mapping_type` varchar(255) NULL COMMENT '映射方式' AFTER `is_fixed`;

ALTER TABLE `mdt_instruments`
    MODIFY COLUMN `case_id` bigint(20) NULL COMMENT '案件id' AFTER `instruments_id`;

ALTER TABLE `mdt_instruments`
    MODIFY COLUMN `case_id` bigint(20) NULL COMMENT '案件id' AFTER `instruments_id`;

ALTER TABLE `tmpl_basis_module` DROP COLUMN `mapping_type`;

ALTER TABLE `tmpl_basis_module` ADD COLUMN `mapping_type` varchar(255) NULL DEFAULT 1 COMMENT '映射方式' AFTER `is_fixed`;

ALTER TABLE `tmpl_case_module` DROP COLUMN `mapping_type`;

ALTER TABLE `tmpl_case_module`
    ADD COLUMN `mapping_type` varchar(255) NULL DEFAULT 1 COMMENT '映射方式' AFTER `updater_id`;

UPDATE tmpl_basis_module SET mapping_type = 2 WHERE module_title = "当事人信息" OR module_title = "调解记录";

UPDATE tmpl_case_module SET mapping_type = 2 WHERE module_title = "当事人信息" OR module_title = "调解记录";

UPDATE `tmpl_basis_module_field` SET `field_sn` = 2 WHERE `basis_module_field_id` = 33;

UPDATE `tmpl_basis_module_field` SET `field_sn` = 3 WHERE `basis_module_field_id` = 34;

UPDATE `tmpl_basis_module_field` SET `field_sn` = 4 WHERE `basis_module_field_id` = 35;

#--------20240818运行的sql-------
ALTER TABLE `mdt_case_litigant`
    MODIFY COLUMN  litigant_phone VARCHAR(125) NOT NULL COMMENT '当事人电话' AFTER `litigant_name`;

ALTER TABLE `mdt_case_litigant`
    MODIFY COLUMN  litigant_name VARCHAR(125) NOT NULL COMMENT '当事人名称' AFTER `case_id`;

#-----------20240819运行的sql-----------
UPDATE mdt_case SET mediate_status = 1 WHERE org_id IS NOT NULL AND dept_id IS NULL AND current_mediator_id IS NULL;

UPDATE mdt_case SET mediate_status = 2 WHERE org_id IS NOT NULL AND dept_id IS NOT NULL AND current_mediator_id IS NULL;

UPDATE mdt_case SET mediate_status = 3 WHERE org_id IS NOT NULL AND dept_id IS NOT NULL AND current_mediator_id IS NOT NULL;