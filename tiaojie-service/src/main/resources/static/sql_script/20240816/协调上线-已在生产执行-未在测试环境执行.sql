INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (36, 3, 'case_nature_content', '案由', NULL, 5, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (37, 3, 'case_subject_matter', '标的', NULL, 6, NULL, 'number', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (38, 3, 'current_mediator_name', '当前仲调员', NULL, 7, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL,
        NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (39, 3, 'create_time', '创建时间', NULL, 8, NULL, 'datetime', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (40, 3, 'update_time', '更新时间', NULL, 9, NULL, 'datetime', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);



INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (41, 3, 'mediate_status', '调解状态', NULL, 10, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (42, 3, 'mediate_result', '调解结果', NULL, 11, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (43, 3, 'mdt_case_status', '案件状态', NULL, 12, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (44, 3, 'close_reason', '终止原因', NULL, 13, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (45, 3, 'case_name', '案件名称', NULL, 14, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (46, 3, 'success_reason', '成功原因', NULL, 15, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (47, 3, 'case_touched', '案件已触达', NULL, 16, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (48, 3, 'score', '繁简分流打分', NULL, 17, NULL, 'number', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (49, 3, 'expiration_time', '案件到期时间', NULL, 18, NULL, 'datetime', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (50, 3, 'entrusts_dept_name', '案源方部门', NULL, 19, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (51, 3, 'creator_name', '承办法官', NULL, 20, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (52, 3, 'mediate_org_company_name', '调解组织', NULL, 21, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (53, 3, 'mediate_org_dept_name', '调解团队', NULL, 22, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (54, 3, 'mediate_org_name', '调解员', NULL, 23, NULL, 'varchar', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`,
                                      `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`,
                                      `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`,
                                      `is_disable`, `validation`, `file_semantics`, `show_format`)
VALUES (55, 3, 'case_end_time', '办结时间', NULL, 24, NULL, 'datetime', NULL, 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);


