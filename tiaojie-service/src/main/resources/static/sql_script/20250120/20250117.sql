
-- 当事人修改名字

UPDATE `tmpl_basis_module_field` SET `field_values` = '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"申请人\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"被申请人\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"申请人代理\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"被申请人代理\\\"},{\\\"key\\\":5,\\\"value\\\":\\\"第三人\\\"},{\\\"key\\\":6,\\\"value\\\":\\\"申请执行人\\\"},{\\\"key\\\":7,\\\"value\\\":\\\"被执行人\\\"},{\\\"key\\\":8,\\\"value\\\":\\\"案外人\\\"},{\\\"key\\\":9,\\\"value\\\":\\\"抵押权人\\\"},{\\\"key\\\":10,\\\"value\\\":\\\"普通债权人\\\"}]\",\"name\":\"LitigantIdentityType\"}' WHERE `basis_module_field_id` = 4;

UPDATE `tmpl_basis_module_field` as t1 JOIN (SELECT `field_values` FROM `tmpl_basis_module_field` WHERE `basis_module_field_id` = 4) AS t2 set t1.`field_values` = t2.`field_values`  WHERE t1.`basis_module_field_id` = 18;

UPDATE `tmpl_case_module_field` as t1 JOIN (SELECT `field_values` FROM `tmpl_basis_module_field` WHERE `basis_module_field_id` = 4) AS t2 set t1.`field_values` = t2.`field_values`  WHERE t1.`basis_module_field_id` in (18,4);


