ALTER TABLE `auth_ten_agent` ADD COLUMN `entrusts_id` bigint NULL COMMENT '委托方ID' AFTER `org_id`;

ALTER TABLE `auth_ten_agent` ADD COLUMN `company_id` bigint NULL COMMENT '公司ID' AFTER `org_id`;

ALTER TABLE `auth_ten_agent` ADD COLUMN `company_type` varchar(12) DEFAULT NULL COMMENT '企业类型：1-平台，2-调解组织，3-案源方' AFTER `org_id`;

ALTER TABLE `mdt_case_approval`
ADD COLUMN `case_delay_time` datetime(0) NULL COMMENT '案件延期日期' AFTER `case_close_time`;

INSERT INTO `tmpl_basis_module`(`basis_module_id`, `module_title`, `module_desc`, `module_type`, `table_name`, `module_sn`, `is_fixed`, `allow_multiple_record`) VALUES (4, '任务登记', NULL, '2', 'mdt_task_complete_register', 4, 1, 0);


INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (58, 4, 'delivered_task_status', '送达任务状态', NULL, 1, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"待处理\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"进行中\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"已完成\\\"}]\",\"name\":\"TaskStatusEnum\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (59, 4, 'scheduling_task_status', '排期任务状态', NULL, 2, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"待处理\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"进行中\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"已完成\\\"}]\",\"name\":\"TaskStatusEnum\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (60, 4, 'evaluation_task_status', '审评鉴状态', NULL, 3, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"待处理\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"进行中\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"已完成\\\"}]\",\"name\":\"TaskStatusEnum\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (61, 4, 'closed_task_status', '结案任务状态', NULL, 4, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"待处理\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"进行中\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"已完成\\\"}]\",\"name\":\"TaskStatusEnum\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (62, 4, 'reach_task_status', '案件触达状态', NULL, 5, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"待处理\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"进行中\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"已完成\\\"}]\",\"name\":\"TaskStatusEnum\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (44, 3, 'close_reason', '终止原因', NULL, 13, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"提起诉讼\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"当事人拒绝调解\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"当事人申请保全\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"双方意愿距过大\\\"},{\\\"key\\\":5,\\\"value\\\":\\\"要求行政部门处理\\\"},{\\\"key\\\":6,\\\"value\\\":\\\"调解期限届满\\\"},{\\\"key\\\":7,\\\"value\\\":\\\"其他\\\"}]\",\"name\":\"CaseStatus\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `tmpl_basis_module_field`(`basis_module_field_id`, `basis_module_id`, `field_name`, `field_title`, `field_desc`, `field_sn`, `field_ui`, `field_data_type`, `field_values`, `is_validated`, `is_require`, `is_update`, `is_desensitize`, `desensitize_rule`, `is_disable`, `validation`, `file_semantics`, `show_format`) VALUES (45, 3, 'success_reason', '成功原因', NULL, 14, NULL, 'dict', '{\"type\":\"enumeration\",\"values\":\"[{\\\"key\\\":1,\\\"value\\\":\\\"不起诉\\\"},{\\\"key\\\":2,\\\"value\\\":\\\"立案后撤诉\\\"},{\\\"key\\\":3,\\\"value\\\":\\\"出具调解书\\\"},{\\\"key\\\":4,\\\"value\\\":\\\"出确认裁定\\\"},{\\\"key\\\":5,\\\"value\\\":\\\"转民初出调解书\\\"}]\",\"name\":\"CaseStatus\"}', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL);

ALTER TABLE `tmpl_case_module_field`
ADD COLUMN `original_name` varchar(255) NULL COMMENT '原始的字段名' AFTER `is_desensitize`;
