ALTER TABLE `tiaojie`.`call_record`
    ADD COLUMN `litigant_id_list` json NULL COMMENT '当事人id集合' AFTER `callee_name`;


-- 补全历史数据
update call_record t0 LEFT JOIN mdt_case_litigant t1 on t1.case_id = t0.case_id  and (t1.litigant_name = t0.callee_name or t0.callee_phone like CONCAT('%',t1.litigant_phone))
    set t0.litigant_id_list = JSON_ARRAY(t1.litigant_id)
where t1.litigant_id is not null and (t1.litigant_name is not null or t1.litigant_phone is not null)

