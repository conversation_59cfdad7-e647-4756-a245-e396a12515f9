DROP TABLE IF EXISTS mdt_instruments;
CREATE TABLE `mdt_instruments`
(
    `instruments_id`            bigint(20) NOT NULL COMMENT '文书id',
    `case_id`                   bigint(20) NOT NULL COMMENT '案件id',
    `instruments_name`          varchar(255) NOT NULL COMMENT '文书名称',
    `instruments_file_path`     varchar(255) DEFAULT NULL COMMENT '文书文件地址',
    `instruments_file_type`     varchar(125) DEFAULT NULL COMMENT '文件类型',
    `instruments_identity_type` varchar(255) DEFAULT NULL COMMENT '文书签名人身份',
    `instruments_litigant_ids`  json         DEFAULT NULL COMMENT '文书签名人id',
    `file_sign_info_id`         bigint(20) DEFAULT NULL COMMENT '文件签名id',
    `instruments_sign_status`   int(11) DEFAULT NULL COMMENT '文书签名状态',
    `instruments_tmpl_id`       bigint(20) DEFAULT NULL COMMENT '文书模板id',
    `creator_id`                bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time`               datetime     DEFAULT NULL COMMENT '创建时间',
    `updater_id`                bigint(20) DEFAULT NULL COMMENT '修改人id',
    `update_time`               datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_flag`               tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志',
    PRIMARY KEY (`instruments_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='案件文书';

DROP TABLE IF EXISTS sign_info;
CREATE TABLE `sign_info`
(
    `sign_info_id`         bigint(22) NOT NULL COMMENT '签名信息ID',
    `person_id`            varchar(300)      DEFAULT NULL COMMENT 'person认证Key(手机号-身份证)',
    `trust_sign_id`        varchar(128)      DEFAULT NULL COMMENT '安心签ID',
    `sign_image_file`      varchar(500)      DEFAULT NULL COMMENT '签名图片',
    `sign_auth_flag`       tinyint(1) DEFAULT NULL COMMENT '是否授权签名',
    `project_code`         varchar(32)       DEFAULT NULL COMMENT '安心签项目code',
    `trust_sign_mobile`    varchar(32)       DEFAULT NULL COMMENT '安心签预留手机号',
    `trust_sign_status`    tinyint(1) DEFAULT NULL COMMENT '安心签状态',
    `trust_sign_cert_type` varchar(64)       DEFAULT NULL COMMENT '安心签证件类型',
    `trust_sign_cert_no`   varchar(64)       DEFAULT NULL COMMENT '安心签证件号码',
    `create_time`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_man_id`        bigint(22) NOT NULL COMMENT '创建人ID',
    PRIMARY KEY (`sign_info_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='签名信息表';


DROP TABLE IF EXISTS sign_file_info;
CREATE TABLE `sign_file_info`
(
    `file_sign_info_id`          bigint(22) NOT NULL COMMENT '主键ID',
    `file_path`                  varchar(500)      DEFAULT NULL COMMENT '被签名文件全路径',
    `ref_id`                     bigint(22) DEFAULT NULL COMMENT '关联业务ID',
    `file_nature`                tinyint(4) DEFAULT NULL COMMENT '文书类型：1庭审笔录',
    `file_sign_qrcode_file_path` varchar(500)      DEFAULT NULL COMMENT '签名二维码图片路径',
    `status`                     varchar(64)       DEFAULT NULL COMMENT '状态：1签名中，2待签名，3已完成, 4已作废',
    `can_sign_flag`              tinyint(1) DEFAULT NULL COMMENT '是否可以签名',
    `sign_complete_time`         datetime          DEFAULT NULL COMMENT '签名完成时间',
    `create_time`                datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_man_id`              bigint(22) NOT NULL COMMENT '创建人ID',
    `update_time`                datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_man_id`              bigint(22) NOT NULL COMMENT '最后更新人ID',
    PRIMARY KEY (`file_sign_info_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件签名信息表';

DROP TABLE IF EXISTS sign_file_record;
CREATE TABLE `sign_file_record`
(
    `file_sign_record_id` bigint(22) NOT NULL COMMENT '文件签名记录ID',
    `file_sign_info_id`   bigint(22) DEFAULT NULL COMMENT '文件签名信息',
    `sign_man_name`       varchar(128)      DEFAULT NULL COMMENT '签名人名字',
    `sign_man_biz_type`   tinyint(4) DEFAULT NULL COMMENT '签名人类型：参考参会人员类型',
    `sign_man_biz_id`     bigint(22) DEFAULT NULL COMMENT '签名人对应业务ID（当事人或代理人或仲裁员）',
    `sign_man_person_id`  bigint(22) DEFAULT NULL COMMENT '签名人实名personID',
    `keyword`             varchar(128)      DEFAULT NULL COMMENT '签名关键字',
    `location_x`          float             DEFAULT NULL COMMENT '签名位置横坐标',
    `location_y`          float             DEFAULT NULL COMMENT '签名位置纵坐标',
    `status`              tinyint(4) DEFAULT NULL COMMENT '状态：1待签名，2拒绝签名，3签名完成',
    `process_time`        datetime          DEFAULT NULL COMMENT '操作时间，签名时间或拒绝签名时间',
    `reject_reason`       varchar(128)      DEFAULT NULL COMMENT '拒绝签名的理由',
    `create_time`         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_man_id`       bigint(22) NOT NULL COMMENT '创建人ID',
    `update_time`         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_man_id`       bigint(22) NOT NULL COMMENT '最后更新人ID',
    PRIMARY KEY (`file_sign_record_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件签名记录表';


ALTER TABLE mdt_case_collect_record
    MODIFY COLUMN other_demand TEXT COMMENT '其他诉求';

ALTER TABLE sys_phone_check_record
    MODIFY COLUMN litigant_type varchar (12) NULL COMMENT '当事人类型';

ALTER TABLE sys_phone_check_record
    MODIFY COLUMN identity_type varchar (12) NULL COMMENT '当事人身份';


ALTER TABLE `mdt_contact_repair_record`
    ADD COLUMN `repair_virtual_no_time` datetime NULL COMMENT '获取虚拟号时间' AFTER `number_fail_message`;

ALTER TABLE `mdt_call_statistic`
    ADD COLUMN `entrusts_id` bigint(20) NULL COMMENT '案源方ID' AFTER `create_time`;

ALTER TABLE `mdt_case`
    ADD COLUMN `score` int(11) NULL COMMENT '繁简分流打分' AFTER `case_touched`;

ALTER TABLE `mdt_case_approval`
    ADD COLUMN `score` int(10) NULL COMMENT '繁简分流打分' AFTER `success_reason`;


DROP TABLE IF EXISTS auth_tc_realname;
CREATE TABLE `auth_tc_realname`
(
    `realname_id`                 bigint(22) NOT NULL COMMENT '实名认证ID',
    `realname_server`             tinyint(4) NOT NULL DEFAULT '1' COMMENT '实名服务类型(1:h5;2:E证通)',
    `user_id`                     bigint(22) NOT NULL COMMENT '实名用户ID',
    `ref_id`                      varchar(32)  NOT NULL COMMENT '关联业务ID',
    `ref_id2`                     varchar(32)           DEFAULT NULL COMMENT '关联业务ID_2',
    `ref_type`                    tinyint(4) NOT NULL COMMENT '关联业务类型',
    `platform_id`                 bigint(22) NOT NULL COMMENT '平台ID',
    `idcard_type`                 tinyint(4) DEFAULT NULL COMMENT '证件类型(内部字典)',
    `biz_token`                   varchar(255) NOT NULL COMMENT 'tc实名业务token',
    `biz_token_expire_time`       datetime     NOT NULL COMMENT 'biz_token过期时间',
    `request_id`                  varchar(255) NOT NULL COMMENT 'tc实名请求业务id',
    `real_name_url`               varchar(700) NOT NULL COMMENT 'tc实名认证url',
    `err_code`                    bigint(22) DEFAULT NULL COMMENT '实名结果code',
    `err_msg`                     varchar(255)          DEFAULT NULL COMMENT '实名结果提示',
    `id_card`                     varchar(50)           DEFAULT NULL COMMENT '证件号',
    `name`                        varchar(50)           DEFAULT NULL COMMENT '姓名',
    `ocr_gender`                  varchar(5)            DEFAULT NULL COMMENT 'ocr 性别',
    `ocr_nation`                  varchar(50)           DEFAULT NULL COMMENT '民族',
    `ocr_address`                 varchar(255)          DEFAULT NULL COMMENT '地址',
    `ocr_authority`               varchar(255)          DEFAULT NULL COMMENT '证件权威机构',
    `ocr_valid_date`              varchar(255)          DEFAULT NULL COMMENT '证件有效期',
    `compare_lib_type`            varchar(10)           DEFAULT NULL COMMENT '实名比对库源类型',
    `ocr_front`                   mediumtext COMMENT 'ocr证件正面照base64编码',
    `ocr_back`                    mediumtext COMMENT 'ocr证件反面照base64编码',
    `best_frame`                  mediumtext COMMENT '活体比对最佳帧base64编码',
    `liveness_video`              mediumtext COMMENT '活体比对视频信息base64编码',
    `intention_question_video`    mediumtext COMMENT '意愿核身问答模式结果,其中包含全程问题和回答音频，mp4格式（base64）',
    `intention_verify_best_frame` mediumtext COMMENT '意愿确认环节中录制视频的最佳帧（base64）',
    `create_time`                 datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_man_id`               bigint(22) NOT NULL COMMENT '创建人ID',
    `update_time`                 datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_man_id`               bigint(22) NOT NULL COMMENT '最后更新人ID',
    PRIMARY KEY (`realname_id`) USING BTREE,
    UNIQUE KEY `biz_token` (`biz_token`) USING BTREE,
    KEY                           `biz_id_index` (`ref_id`,`ref_type`,`platform_id`) USING BTREE,
    KEY                           `biz_id2_index` (`ref_id2`,`ref_type`,`platform_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='腾讯实名认证信息表';


INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (10030600, 10030000, '文书模板管理', '1', 1, 3, NULL, 20, NULL, NULL, 'documentTemplateList', 1, '2022-02-10 15:53:13', 1, '2024-06-06 15:56:34', 1);
INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (10030601, 10030600, '文书模板创建', '1', 3, 4, NULL, 20, NULL, 'tmpl:doc:create', 'docCreate', 1, '2022-02-10 15:53:13', 1, '2024-08-07 18:06:38', 1);
INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (10030602, 10030600, '文书模板删除', '1', 3, 4, NULL, 30, NULL, 'tmpl:doc:delete', 'docDelete', 1, '2022-02-10 15:53:13', 1, '2024-06-18 14:32:33', 1);
INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (10030700, 10030000, '创建文书模板', '1', 3, 3, NULL, 10, NULL, NULL, 'documentTemplateCreate', 1, '2022-02-10 15:53:13', 1, '2024-08-08 16:49:04', 1);
INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (10030800, 10030000, '文书模板配置', '1', 3, 3, NULL, 20, NULL, NULL, 'documentTemplateConfig', 1, '2022-02-10 15:53:13', 1, '2024-08-08 16:49:24', 1);
INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES (10030603, 10030600, '文书模板配置', '1', 3, 4, NULL, 40, NULL, 'tmpl:doc:conf', 'docConf', 0, '2022-02-10 15:53:13', 1, '2024-08-09 18:09:10', 1);



CREATE TABLE `document_template` (
                                     `doc_tmpl_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文书模板编号',
                                     `doc_tmpl_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文书模板名称',
                                     `doc_tmpl_type` varchar(100) NOT NULL COMMENT '制作方式',
                                     `tmpl_id` int(11) NOT NULL COMMENT '所属案件模板id',
                                     `tmpl_status` tinyint(1) NOT NULL COMMENT '模板状态',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `creator_id` bigint(20) NOT NULL COMMENT '创建者',
                                     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                     `updater_id` bigint(20) NOT NULL COMMENT '修改者',
                                     `is_delete` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '是否删除',
                                     `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                     `doc_path` varchar(255) DEFAULT NULL COMMENT '文书模板路径',
                                     PRIMARY KEY (`doc_tmpl_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `doc_tmpl_mapping` (
                                    `mapping_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '映射id',
                                    `doc_tmpl_id` bigint(20) NOT NULL COMMENT '文书模板id',
                                    `mapping_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '映射方式',
                                    `parameter_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参数名称',
                                    `case_fields` json DEFAULT NULL COMMENT '所属案件字段',
                                    `tmpl_id` int(11) NOT NULL COMMENT '所属案件模板id',
                                    `delimiter` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分隔符',
                                    `parameter_content` text COMMENT '参数内容',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `creator_id` bigint(20) NOT NULL COMMENT '创建者',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                    `updater_id` bigint(20) NOT NULL COMMENT '修改者',
                                    PRIMARY KEY (`mapping_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
