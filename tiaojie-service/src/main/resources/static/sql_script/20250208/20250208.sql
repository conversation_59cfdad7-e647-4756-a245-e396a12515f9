SET @basis_module_field_id = 74;

INSERT INTO tmpl_case_module_field (
    tmpl_module_id,
    tmpl_id,
    basis_module_field_id,
    field_name,
    field_title,
    field_desc,
    field_sn,
    field_ui,
    field_data_type,
    field_values,
    is_validated,
    is_require,
    is_update,
    is_disable,
    is_desensitize,
    original_name,
    desensitize_rule,
    validation,
    show_format,
    file_semantics,
    to_case_ext_field,
    component_relation,
    create_time,
    creator_id,
    update_time,
    updater_id
)
WITH MissingTmplModuleIds AS (
    SELECT tcm.tmpl_module_id, tcm.tmpl_id
    FROM tmpl_basis_module_field tbmf
             LEFT JOIN tmpl_case_module tcm ON tbmf.basis_module_id = tcm.basis_module_id
             LEFT JOIN tmpl_case_module_field tcmf ON tcm.tmpl_module_id = tcmf.tmpl_module_id  AND tbmf.basis_module_field_id = tcmf.basis_module_field_id
    WHERE
        tcmf.basis_module_field_id IS NULL
      AND tbmf.basis_module_field_id = @basis_module_field_id
), BasisField AS (
    SELECT *
    FROM tmpl_basis_module_field
    WHERE basis_module_field_id = @basis_module_field_id
)
SELECT
    m.tmpl_module_id,
    m.tmpl_id,
    bf.basis_module_field_id,
    bf.field_name,
    bf.field_title,
    bf.field_desc,
    bf.field_sn,
    bf.field_ui,
    bf.field_data_type,
    bf.field_values,
    bf.is_validated,
    bf.is_require,
    bf.is_update,
    bf.is_disable,
    bf.is_desensitize,
    NULL,
    bf.desensitize_rule,
    bf.validation,
    bf.show_format,
    bf.file_semantics,
    NULL,
    NULL,
    CURRENT_TIMESTAMP,
    1 AS creator_id,
    CURRENT_TIMESTAMP,
    1 AS updater_id
FROM MissingTmplModuleIds m
         CROSS JOIN BasisField bf;