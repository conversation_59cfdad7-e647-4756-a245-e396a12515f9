-- 案件当事人联系方式表
CREATE TABLE `mdt_case_litigant_phone_tag` (
   `litigant_phone_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
   `case_id` bigint DEFAULT NULL COMMENT '案件id',
   `litigant_id` bigint DEFAULT NULL COMMENT '当事人id',
   `phone` varchar(255) DEFAULT NULL COMMENT '手机号',
   `check_tag` tinyint(1) DEFAULT NULL COMMENT '号码检测标记',
   `pre_tag` tinyint(1) DEFAULT NULL COMMENT '上次联系标记',
   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `creator_id` bigint DEFAULT NULL COMMENT '创建人',
   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
   `updater_id` bigint DEFAULT NULL COMMENT '更新人',
   PRIMARY KEY (`litigant_phone_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='案件当事人联系方式标记表（标记号码检测和上次联系）';

-- 修改注释
ALTER TABLE mdt_case_litigant MODIFY litigant_phone VARCHAR(500) not null COMMENT "当事人电话,多个电话用英文','逗号分隔";
ALTER TABLE mdt_case_litigant MODIFY  phone_status VARCHAR(255) COMMENT "当事人电话状态（弃用）";

-- 初始化
INSERT INTO `mdt_case_litigant_phone_tag` (
    `case_id`,
    `litigant_id`,
    `phone`,
    `check_tag`
)
SELECT
    `case_id`,
    `litigant_id`,
    `litigant_phone`,
    `phone_status`
FROM `mdt_case_litigant`
WHERE `litigant_phone` IS NOT NULL
  AND `litigant_phone` != ''
AND `phone_status` IS NOT NULL
AND `phone_status` != '';

-- 手机号码检测记录添加手机号字段
alter table sys_phone_check_record add COLUMN litigant_phone VARCHAR(50) DEFAULT null COMMENT "检测的手机号码";

-- 更新老数据
UPDATE sys_phone_check_record r,mdt_case_litigant l set r.litigant_phone = l.litigant_phone WHERE r.litigant_id = l.litigant_id

