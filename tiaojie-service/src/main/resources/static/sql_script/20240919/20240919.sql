ALTER TABLE `tmpl_case_module_field`
ADD COLUMN `component_relation` varchar(255) NULL COMMENT '组件关联' AFTER `to_case_ext_field`;

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`)
VALUES
('SETTING_SERVICE', 'SETTING_SERVICE_PHONE_SIGN', '电话脱敏', '0', '是否开启电话脱敏', '2024-09-20 10:50:14', 1, '2024-09-20 10:50:14', 1);


INSERT INTO `sys_menu`(`menu_id`, `parent_menu_id`, `menu_name`, `menu_module`, `menu_type`, `tree_level`, `menu_icon`, `sort_no`, `menu_href`, `menu_flag`, `menu_route_name`, `enable_flag`, `create_time`, `creator_id`, `update_time`, `updater_id`)
VALUES
(14080000, 14000000, '系统配置', '1', 1, 2, NULL, 70, NULL, NULL, 'systemConf', 1, '2022-02-10 15:53:13', 1, '2024-08-28 11:38:41', 1);