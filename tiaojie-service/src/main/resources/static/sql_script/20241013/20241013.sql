ALTER TABLE `mdt_case_collect_record` ADD COLUMN `litigant_id` bigint NULL COMMENT '当事人Id' AFTER `contactor_user`;

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE', 'SETTING_SERVICE_PASSWORD_SIGN', '密码强度', '0', '是否开启密码强度', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_SETTING', 'SETTING_SERVICE_PASSWORD_SETTING_NUMBER', '数字', '0', '密码是否包含数字', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_SETTING', 'SETTING_SERVICE_PASSWORD_SETTING_UPPERCASE', '大写字母', '0', '密码是否包含大写字母', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_SETTING', 'SETTING_SERVICE_PASSWORD_SETTING_LOWERCASE', '小写字母', '0', '密码是否包含小写字母', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_SETTING', 'SETTING_SERVICE_PASSWORD_SETTING_SPECIAL', '特殊字符', '0', '密码是否包含特殊字符', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_SETTING', 'SETTING_SERVICE_PASSWORD_SETTING_SIZE', '最小长度', '0', '密码最小长度', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE', 'SETTING_SERVICE_TIMEOUT_LOGOUT', '账号超时自动退出', '0', '是否开启账号超时自动退出', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_LOGOUT_SETTING', 'SETTING_SERVICE_LOGOUT_TIME', '账号超时时间', '0', '账号超时时间', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE', 'SETTING_SERVICE_PASSWORD_LOCK', '密码多次错误自动锁定', '0', '是否开启密码多次错误自动锁定', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_LOCK', 'SETTING_SERVICE_ERROR_NUMBER', '错误次数', '0', '错误次数', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_LOCK', 'SETTING_SERVICE_UNLOCK_TIME', '自动解锁时间', '0', '自动解锁时间', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE', 'SETTING_SERVICE_PASSWORD_UPDATE', '定期更新密码', '0', '是否开启定期更新密码', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE_PASSWORD_UPDATE', 'SETTING_SERVICE_UPDATE_CYCLE', '更新周期', '0', '更新周期', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE', 'SETTING_SERVICE_PASSWORD_MODIFY_FIRSTTIME', '初次登录修改密码', '0', '是否开启初次登录修改密码', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

INSERT INTO `sys_parameter`(`module_code`, `param_code`, `param_name`, `param_value`, `param_cmnt`, `create_time`, `creator_id`, `update_time`, `updater_id`) VALUES ('SETTING_SERVICE', 'SETTING_SERVICE_LOGIN_AUTHENTICATION', '登录双因子验证', '0', '是否开启登录双因子验证', '2024-09-20 10:50:14', 1, '2024-10-12 10:33:33', 1);

ALTER TABLE `auth_account`
ADD COLUMN `is_first_login` tinyint(1) NULL DEFAULT 1 COMMENT '是否首次登录' AFTER `isplateform`,
ADD COLUMN `lock_time` datetime(0) NULL COMMENT '账号锁定时间' AFTER `is_first_login`;

ALTER TABLE `auth_account`
ADD COLUMN `pwd_update_time` datetime(0) NULL COMMENT '密码更新时间' AFTER `lock_time`;

ALTER TABLE `auth_account`
ADD COLUMN `ip_config` varchar(255) NULL COMMENT '登录IP限制' AFTER `pwd_update_time`;

ALTER TABLE `mdt_case`
ADD COLUMN `mediate_close_time` datetime(0) NULL COMMENT '调解结束时间' AFTER `expiration_time`;