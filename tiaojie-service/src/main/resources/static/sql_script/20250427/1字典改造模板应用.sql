ALTER TABLE `sys_dict_data` ADD COLUMN `business_type` varchar(50) NULL COMMENT '业务类型(1:政务,2:金融)' AFTER `sys_flag`;




ALTER TABLE `tmpl_basis_module_field` ADD COLUMN `field_dict_type` varchar(255) NULL COMMENT '字段字典对应的type，将过去的字典逐步替换为这个' AFTER `field_values`;
ALTER TABLE `tmpl_case_module_field` ADD COLUMN `field_dict_type` varchar(255) NULL COMMENT '字段字典对应的type，将过去的字典逐步替换为这个' AFTER `field_values`;

-- 删除重复的字典tag
DELETE FROM sys_dict_data WHERE dict_id = 20;

-- 人格权纠纷
UPDATE sys_dict_data SET parent_id=1262 WHERE parent_id=1263;
DELETE FROM sys_dict_data WHERE dict_data_id = 1263;


-- 不当得利纠纷
DELETE FROM sys_dict_data WHERE dict_data_id = 1583;


-- 无因管理纠纷
DELETE FROM sys_dict_data WHERE dict_data_id = 1585;

-- 海事海商纠纷
UPDATE sys_dict_data SET parent_id=1262 WHERE parent_id=1778;
DELETE FROM sys_dict_data WHERE dict_data_id = 1778;

-- 侵权责任纠纷
UPDATE sys_dict_data SET parent_id=1262 WHERE parent_id=2006;
DELETE FROM sys_dict_data WHERE dict_data_id = 2006;

-- 第三人撤销之诉
DELETE FROM sys_dict_data WHERE dict_data_id = 2183;

-- 增加唯一索引
ALTER TABLE `tiaojie`.`sys_dict_data` ADD UNIQUE INDEX `tag_unique`(`dict_id`, `dict_tag`) USING BTREE;
ALTER TABLE `tiaojie`.`sys_dict_data` ADD UNIQUE INDEX `key_unique`(`dict_id`, `dict_key`) USING BTREE;