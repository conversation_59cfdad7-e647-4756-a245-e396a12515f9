
-- 案件模板 终止原因改为系统字典类型
UPDATE tmpl_basis_module_field SET field_data_type='system_dict',field_values=NULL WHERE field_name = 'close_reason';
UPDATE tmpl_case_module_field SET field_data_type='system_dict',field_values=NULL WHERE field_name = 'close_reason';


UPDATE tmpl_basis_module_field SET field_dict_type='case_close_reason',field_values=NULL WHERE field_name = 'close_reason';
UPDATE tmpl_case_module_field SET field_dict_type='case_close_reason',field_values=NULL WHERE field_name = 'close_reason';

-- 插入字典类型
INSERT INTO `sys_dict` (`dict_type`, `dict_name`, `dict_status`, `sys_flag`, `remark`, `creator_id`, `updater_id`)
VALUES ('case_close_reason', '案件办结终止原因', 1, 0, '', 1, 1);

-- 获取 dict_id
SET @dict_id = (SELECT dict_id FROM sys_dict WHERE dict_type = 'case_close_reason');

-- 业务类型 1
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES (NULL, @dict_id, 1, '提起诉讼', 1, '', 1, 1, '1'),
       (NULL, @dict_id, 2, '当事人拒绝调解', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 3, '当事人申请保全', 1, '', 1, 1, '1'),
       (NULL, @dict_id, 4, '双方意愿差距过大', 1, '', 1, 1, NULL),
       (NULL, @dict_id, 5, '要求行政部门处理', 1, '', 1, 1, '1'),
       (NULL, @dict_id, 6, '调解期限届满', 1, '', 1, 1, '1'),
       (NULL, @dict_id, 7, '其他', 1, '', 1, 1, '1');

-- 获取 "其他" 的 dict_data_id
SET @other_id = (SELECT dict_data_id FROM sys_dict_data WHERE dict_id = @dict_id AND dict_key = 7);

-- 插入 "其他" 的子项
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES (@other_id, @dict_id, 8, '当事人联系不上', 1, '', 1, 1, '1'),
       (@other_id, @dict_id, 9, '不属于调解范畴', 1, '', 1, 1, '1'),
       (@other_id, @dict_id, 10, '其他情形', 1, '', 1, 1, '1');

-- 获取 "不属于调解范畴" 的 dict_data_id
SET @not_mediation_id = (SELECT dict_data_id FROM sys_dict_data WHERE dict_id = @dict_id AND dict_key = 9);

-- 插入 "不属于调解范畴" 的子项
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES (@not_mediation_id, @dict_id, 11, '纠纷类型不适合调解', 1, '', 1, 1, '1'),
       (@not_mediation_id, @dict_id, 12, '有另案诉讼无法继续处理', 1, '', 1, 1, '1'),
       (@not_mediation_id, @dict_id, 13, '涉及刑事诉讼', 1, '', 1, 1, '1');

-- 业务类型 2
INSERT INTO `sys_dict_data` (`parent_id`, `dict_id`, `dict_key`, `dict_tag`, `enable_flag`, `remark`, `creator_id`, `updater_id`, `business_type`)
VALUES (NULL, @dict_id, 201, '案件事实有争议', 1, '', 1, 1, '2'),
       (NULL, @dict_id, 202, '当事人失联', 1, '', 1, 1, '2'),
       (NULL, @dict_id, 205, '分期违约', 1, '', 1, 1, '2'),
       (NULL, @dict_id, 206, '调解期限届满退案', 1, '', 1, 1, '2'),
       (NULL, @dict_id, 207, '客户要求退案', 1, '', 1, 1, '2');
