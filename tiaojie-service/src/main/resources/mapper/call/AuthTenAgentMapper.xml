<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthTenAgentMapper">

    <select id="list" resultType="com.sct.tiaojie.service.call.dto.TenAgentDTO">
        SELECT t1.*,
        t2.org_name,
        t3.dept_name,
        t4.employee_name as accountName
        FROM auth_ten_agent t1
        left join sys_mdt_org t2
        on t1.org_id = t2.org_id
        left join auth_dept t3
        on t1.dept_id = t3.dept_id
        left join auth_employee t4
        on t1.account_id = t4.account_id
        where t1.account_id is not null
    </select>
</mapper>