<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.call.mapper.CallStatisticMapper">

    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.call.entity.CallStatistic">
            <id property="statisticId" column="statistic_id" jdbcType="BIGINT"/>
            <result property="callType" column="call_type" jdbcType="TINYINT"/>
            <result property="statisticYear" column="statistic_year" jdbcType="VARCHAR"/>
            <result property="statisticMonth" column="statistic_month" jdbcType="VARCHAR"/>
            <result property="statisticDay" column="statistic_day" jdbcType="VARCHAR"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
            <result property="caseCause" column="case_cause" jdbcType="VARCHAR"/>
            <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
            <result property="mediatorId" column="mediator_id" jdbcType="BIGINT"/>
            <result property="mediatorName" column="mediator_name" jdbcType="VARCHAR"/>
            <result property="callCount" column="call_count" jdbcType="BIGINT"/>
            <result property="throughCount" column="through_count" jdbcType="BIGINT"/>
            <result property="validCallCount" column="valid_call_count" jdbcType="BIGINT"/>
            <result property="callTimeTotal" column="call_time_total" jdbcType="DOUBLE"/>
            <result property="callMissedCount" column="call_missed_count" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        statistic_id,call_type,statistic_year,
        statistic_month,statistic_day,org_id,
        org_name,dept_id,dept_name,entrusts_id,
        case_cause,agent_id,mediator_id,
        mediator_name,call_count,through_count,
        valid_call_count,call_time_total,call_missed_count,
        create_time
    </sql>

    <select id="getCallStatisticOverviewList" resultType="com.sct.tiaojie.repository.call.entity.CallStatisticOverview">
        SELECT
            statistic_year,
            statistic_month,
            statistic_day,
            SUM(call_count) AS callCount,
            SUM(through_count) AS throughCount,
            SUM(call_time_total) AS callTimeTotal,
            COUNT(DISTINCT agent_id) AS agentCount
        FROM
            mdt_call_statistic
        WHERE
            mdt_call_statistic.statistic_id IN
            <foreach item="callStatisticId" collection="callStatisticIds" open="(" separator="," close=")">
                #{callStatisticId}
            </foreach>
            AND
            STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
        GROUP BY
            statistic_year,
            statistic_month,
            statistic_day
        ORDER BY
            statistic_year,
            statistic_month,
            statistic_day
    </select>


    <sql id="caseJoin">
        <choose>
            <when test="groupCondition == 1">
                LEFT JOIN sys_entrusts ON mdt_call_statistic.entrusts_id = sys_entrusts.entrusts_id
            </when>
            <when test="groupCondition == 2">
                LEFT JOIN sys_mdt_org ON mdt_call_statistic.org_id = sys_mdt_org.org_id
            </when>
            <when test="groupCondition == 3">
                LEFT JOIN auth_dept ON mdt_call_statistic.dept_id = auth_dept.dept_id
            </when>
            <when test="groupCondition == 4">
                LEFT JOIN auth_employee ON mdt_call_statistic.mediator_id = auth_employee.account_id
            </when>
        </choose>
    </sql>

    <select id="getCallStatisticDTO" resultType="com.sct.tiaojie.service.call.dto.CallStatisticDTO">
        SELECT
        SUM(call_count) AS callCount,
        SUM(through_count) AS throughCount,
        SUM(call_time_total) AS callTimeTotal,
        COUNT(DISTINCT agent_id) AS agentCount
        FROM
        mdt_call_statistic
        WHERE
        statistic_id IN
        <foreach item="callStatisticId" collection="callStatisticIds" open="(" separator="," close=")">
            #{callStatisticId}
        </foreach>
        AND
        STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
    </select>


    <sql id="groupSelect">
        <choose>
            <when test="groupCondition == 1">
                mdt_call_statistic.entrusts_id AS groupId, sys_entrusts.entrusts_name AS groupName
            </when>
            <when test="groupCondition == 2">
                mdt_call_statistic.org_id AS groupId, sys_mdt_org.org_name AS groupName
            </when>
            <when test="groupCondition == 3">
                mdt_call_statistic.dept_id AS groupId, auth_dept.dept_name AS groupName
            </when>
            <when test="groupCondition == 4">
                mdt_call_statistic.mediator_id AS groupId, auth_employee.employee_name AS groupName
            </when>
        </choose>
    </sql>

    <sql id="validAnswerAggregation">
       SUM(mdt_call_statistic.through_count) AS throughCount,
       SUM(mdt_call_statistic.valid_call_count) AS validCallCount,
       mdt_call_statistic.call_type
    </sql>

    <select id="getValidAnswerRateDTO" resultType="com.sct.tiaojie.service.call.dto.ValidAnswerRateDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="validAnswerAggregation"/>
        FROM
        mdt_call_statistic
        <include refid="caseJoin"></include>
        WHERE
        mdt_call_statistic.statistic_id IN
        <foreach item="callStatisticId" collection="callStatisticIds" open="(" separator="," close=")">
            #{callStatisticId}
        </foreach>
        AND
        STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
        GROUP BY
        groupId,
        groupName,
        mdt_call_statistic.call_type
    </select>


    <sql id="callsPerPersonAggregation">
       SUM(mdt_call_statistic.call_count) AS callCount,
       COUNT(DISTINCT agent_id) AS agentAmount,
       mdt_call_statistic.call_type
    </sql>

    <select id="getCallsPerPersonDTO" resultType="com.sct.tiaojie.service.call.dto.CallsPerPersonDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="callsPerPersonAggregation"/>
        FROM
        mdt_call_statistic
        <include refid="caseJoin"></include>
        WHERE
        mdt_call_statistic.statistic_id IN
        <foreach item="callStatisticId" collection="callStatisticIds" open="(" separator="," close=")">
            #{callStatisticId}
        </foreach>
        AND
        STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
        GROUP BY
        groupId,
        groupName,
        mdt_call_statistic.call_type
    </select>

    <sql id="avgCallTimeAggregation">
       SUM(mdt_call_statistic.call_time_total) AS callTimeTotal,
       COUNT(DISTINCT agent_id) AS agentAmount,
       mdt_call_statistic.call_type
    </sql>

    <select id="getAvgCallTimeDTO" resultType="com.sct.tiaojie.service.call.dto.AvgCallTimeDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="avgCallTimeAggregation"/>
        FROM
        mdt_call_statistic
        <include refid="caseJoin"></include>
        WHERE
        mdt_call_statistic.statistic_id IN
        <foreach item="callStatisticId" collection="callStatisticIds" open="(" separator="," close=")">
            #{callStatisticId}
        </foreach>
        AND
        STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
        GROUP BY
        groupId,
        groupName,
        mdt_call_statistic.call_type
    </select>


    <sql id="avgEffectiveCallDTOAggregation">
       SUM(mdt_call_statistic.valid_call_time_total) AS validCallTimeTotal,
       SUM(mdt_call_statistic.valid_call_count) AS validCallCount,
       mdt_call_statistic.call_type
    </sql>

    <select id="getAverageEffectiveCallDTO" resultType="com.sct.tiaojie.service.call.dto.AverageEffectiveCallDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="avgEffectiveCallDTOAggregation"/>
        FROM
        mdt_call_statistic
        <include refid="caseJoin"></include>
        WHERE
        mdt_call_statistic.statistic_id IN
        <foreach item="callStatisticId" collection="callStatisticIds" open="(" separator="," close=")">
            #{callStatisticId}
        </foreach>
        AND
        STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
        GROUP BY
        groupId,
        groupName,
        mdt_call_statistic.call_type
    </select>

    <sql id="callFailureRateDTOAggregation">
       statistic_year,
       statistic_month,
       statistic_day,
       SUM(mdt_call_statistic.call_count) AS callCount,
       SUM(mdt_call_statistic.call_missed_count) AS callMissedCount
    </sql>

    <select id="getCallFailureRateDTO" resultType="com.sct.tiaojie.service.call.dto.CallFailureRateDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="callFailureRateDTOAggregation"/>
        FROM
        mdt_call_statistic
        <include refid="caseJoin"></include>
        WHERE
        mdt_call_statistic.statistic_id IN
        <foreach item="callStatisticId" collection="callStatisticIds" open="(" separator="," close=")">
            #{callStatisticId}
        </foreach>
        AND
        STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
        GROUP BY
        groupId,
        groupName,
        statistic_year,
        statistic_month,
        statistic_day
        ORDER BY
        statistic_year,
        statistic_month,
        statistic_day
    </select>

</mapper>
