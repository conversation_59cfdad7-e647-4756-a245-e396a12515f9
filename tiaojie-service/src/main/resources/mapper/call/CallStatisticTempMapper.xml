<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.call.mapper.CallStatisticTempMapper">

    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.call.entity.CallStatisticTemp">
            <id property="statisticId" column="statistic_id" jdbcType="BIGINT"/>
            <result property="callType" column="call_type" jdbcType="TINYINT"/>
            <result property="statisticYear" column="statistic_year" jdbcType="VARCHAR"/>
            <result property="statisticMonth" column="statistic_month" jdbcType="VARCHAR"/>
            <result property="statisticDay" column="statistic_day" jdbcType="VARCHAR"/>
            <result property="statisticHour" column="statistic_hour" jdbcType="VARCHAR"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
            <result property="caseCause" column="case_cause" jdbcType="VARCHAR"/>
            <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
            <result property="mediatorId" column="mediator_id" jdbcType="BIGINT"/>
            <result property="mediatorName" column="mediator_name" jdbcType="VARCHAR"/>
            <result property="callCount" column="call_count" jdbcType="BIGINT"/>
            <result property="throughCount" column="through_count" jdbcType="BIGINT"/>
            <result property="validCallCount" column="valid_call_count" jdbcType="BIGINT"/>
            <result property="callTimeTotal" column="call_time_total" jdbcType="BIGINT"/>
            <result property="callMissedCount" column="call_missed_count" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        statistic_id,call_type,statistic_year,
        statistic_month,statistic_day,statistic_hour,
        org_id,org_name,dept_id,
        dept_name,case_cause,agent_id,
        mediator_id,mediator_name,call_count,
        through_count,valid_call_count,call_time_total,
        call_missed_count,create_time,delete_flag
    </sql>
</mapper>
