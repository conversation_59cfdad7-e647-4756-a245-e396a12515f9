<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.call.mapper.CallRecordMapper">

    <sql id="selectList">
        SELECT
            t2.case_no,
            t2.case_id,
            t1.mediator_name,
            t1.time_length,
            t1.callee_phone,
            t1.callee_name,
            t1.call_result,
            t1.start_time,
            t1.record_id,
            t1.caller_phone,
            t1.ring_length,
            t1.org_name,
            t1.dept_name AS deptName,
            t1.usage_mode,
            t3.file_path
        FROM call_record t1
                 LEFT JOIN mdt_case t2
                           ON t1.case_id = t2.case_id
                 left join call_recording t3
                           on t1.record_id = t3.record_id
    </sql>

    <select id="pageList" resultType="com.sct.tiaojie.service.call.dto.CallRecordPageDTO">
        <include refid="selectList"/>
        <include refid="whereSql"/>
    </select>
    <sql id="whereSql">
        <where>
            <if test="param != null">
                <if test="param.mediatorName != null">
                    AND t1.mediator_name LIKE CONCAT('%', #{param.mediatorName}, '%')
                </if>
                <if test="param.callerPhone != null">
                    AND t1.caller_phone LIKE CONCAT('%', #{param.callerPhone}, '%')
                </if>
                <if test="param.calleePhone != null">
                    AND t1.callee_phone LIKE CONCAT('%', #{param.calleePhone}, '%')
                </if>
                <if test="param.optDateStart != null">
                    AND t1.start_time >= #{param.optDateStart}
                </if>
                <if test="param.optDateEnd != null">
                    AND t1.start_time &lt;= #{param.optDateEnd}
                </if>
                <if test="param.usageMode != null">
                    AND t1.usage_mode = #{param.usageMode}
                </if>
                <if test="param.calleeName != null">
                    AND t1.callee_name LIKE CONCAT('%', #{param.calleeName}, '%')
                </if>
                <if test="param.mediatorId != null">
                    AND t1.mediator_id = #{param.mediatorId}
                </if>
                <if test="param.companyId != null">
                    AND t1.company_id = #{param.companyId}
                </if>
                <if test="param.companyIds != null and !param.companyIds.isEmpty">
                    AND t1.company_id IN
                    <foreach collection="param.companyIds" item="companyId" open="(" close=")" separator=",">
                        #{companyId}
                    </foreach>
                </if>
                <if test="param.mediatorIds != null and !param.mediatorIds.isEmpty">
                    AND t1.mediator_id IN
                    <foreach collection="param.mediatorIds" item="mediatorId" open="(" close=")" separator=",">
                        #{mediatorId}
                    </foreach>
                </if>
            </if>
        </where>
    </sql>
    <select id="selectAll" resultType="java.util.Map">
        SELECT
               t2.case_no,
               t1.mediator_name,
               t6.entrusts_name,
               t1.time_length,
               t1.callee_phone,
               t1.callee_name,
               t1.call_result,
               t4.file_path,
               t1.answer_time,
               t1.bye_time,
               t1.record_id,
               t1.caller_phone,
               t1.start_time AS create_time,
               t1.provider_name
        FROM call_record t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN call_recording t4
        ON t1.record_id = t4.record_id
        <where>
            <if test="recordIds != null and !recordIds.isEmpty">
                AND t1.record_id IN
                <foreach collection="recordIds" item="recordId" open="(" close=")" separator=",">
                    #{recordId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getRecordIds" resultType="java.lang.Long">
        SELECT t1.record_id
        FROM call_record t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN call_recording t4
        ON t1.record_id = t4.record_id
        <include refid="whereSql"/>
    </select>
    <select id="getSameIds" resultType="java.lang.String">
        SELECT session
        FROM call_record
        <if test="sessionIds != null and !sessionIds.isEmpty">
            <where>
                AND session IN
                <foreach collection="sessionIds" item="sessionId" open="(" close=")" separator=",">
                    #{sessionId}
                </foreach>
            </where>
        </if>
    </select>
    <select id="list" resultType="com.sct.tiaojie.service.call.dto.CallRecordPageDTO">
        SELECT
               t2.case_no,
               t1.mediator_name,
               t1.time_length,
               t1.callee_phone,
               t1.callee_name,
               t1.call_result,
               t1.start_time,
               t1.record_id,
               t1.caller_phone,
               t1.ring_length,
               t1.org_name,
               t1.usage_mode,
               t3.file_path
        FROM call_record t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        left join call_recording t3
        on t1.record_id = t3.record_id
        where t1.case_id = #{caseId}
        order by t1.start_time desc
    </select>

    <select id="getCallStatistic" resultType="com.sct.tiaojie.repository.call.entity.CallStatistic">
        SELECT
            NULL AS statisticId,
            (CASE WHEN usage_mode = '呼入' THEN 1 WHEN usage_mode = '呼出' THEN 2 WHEN usage_mode = '上传' THEN 3 ELSE NULL END) AS callType,
            YEAR(cr.create_time) AS statisticYear,
            MONTH(cr.create_time) AS statisticMonth,
            DAY(cr.create_time) AS statisticDay,
            mc.entrusts_id AS entrustsId,
            mc.org_id AS orgId,
            cr.org_name AS orgName,
            mc.dept_id AS deptId,
            cr.dept_name AS deptName,
            mc.case_nature_content AS caseCause,
            cr.agent_id AS agentId,
            cr.mediator_id AS mediatorId,
            cr.mediator_name AS mediatorName,
            COUNT(*) AS callCount,
            SUM(CASE WHEN cr.release_cause = '接通' THEN 1 ELSE 0 END) AS throughCount,
            SUM(CASE WHEN cr.call_result = '正常结束' THEN 1 ELSE 0 END) AS validCallCount,
            SUM(CASE WHEN cr.call_result = '正常结束' THEN TIMESTAMPDIFF(MINUTE, cr.answer_time, cr.bye_time) ELSE 0 END) AS validCallTimeTotal,
            SUM(TIME_TO_SEC(cr.time_length)) AS callTimeTotal,
            SUM(CASE WHEN cr.release_cause = '未接通' THEN 1 ELSE 0 END) AS callMissedCount,
            NOW() AS create_time
        FROM
            call_record cr
            LEFT JOIN
            mdt_case mc ON cr.case_id = mc.case_id
        WHERE
            DATE(cr.create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        GROUP BY
            agentId,
            statisticYear,
            statisticMonth,
            statisticDay,
            orgId,
            entrustsId,
            orgName,
            deptId,
            deptName,
            mediatorId,
            mediatorName,
            caseCause,
            callType
    </select>

    <select id="selectCallRecordList" resultType="com.sct.tiaojie.service.call.dto.CallRecordPageDTO">
        <include refid="selectList"/>
        <include refid="whereSql"/>
    </select>
</mapper>