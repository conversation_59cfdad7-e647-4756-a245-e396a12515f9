<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.task.mapper.TaskInstanceMapper">

    <!-- 任务实例分页查询结果映射 -->
    <resultMap id="TaskInstanceDTOResultMap" type="com.sct.tiaojie.repository.dto.TaskInstanceDTO">
        <result property="managerIdList" column="manager_id_list" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>


    <sql id="selectDTO">
        SELECT ti.*,
               ae_assign.employee_name AS assignAccountName,
               ae_finish.employee_name AS finishAccountName,
               (SELECT GROUP_CONCAT(ae.employee_name SEPARATOR ',')
                FROM auth_employee ae
                WHERE JSON_CONTAINS(ti.manager_id_list, CAST(ae.account_id AS CHAR))) AS managerNames
        FROM task_instance ti
                 LEFT JOIN auth_employee ae_assign ON ti.assign_account_id = ae_assign.account_id
                 LEFT JOIN auth_employee ae_finish ON ti.finish_account_id = ae_finish.account_id
            ${ew.customSqlSegment}
    </sql>

    <!-- 查询任务实例列表，关联账号表获取姓名信息 -->
    <select id="selectDTOList" resultMap="TaskInstanceDTOResultMap">
        <include refid="selectDTO"/>
    </select>

    <!-- 根据ID查询单个任务实例，关联账号表获取姓名信息 -->
    <select id="selectOneDTO" resultMap="TaskInstanceDTOResultMap">
        <include refid="selectDTO"/>
    </select>

</mapper>