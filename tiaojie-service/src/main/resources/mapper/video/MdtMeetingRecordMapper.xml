<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.video.mapper.MdtMeetingRecordMapper">

    <sql id="selectMeetingRecord">
        SELECT t1.record_id,
               t1.case_id,
               t2.case_no,
               t4.entrusts_name,
               t1.meeting_name,
               t1.mediator_name,
               t1.mediation_time,
               t6.company_name,
               GROUP_CONCAT( t7.litigant_name SEPARATOR '/' ) AS participant,
               GROUP_CONCAT( IF(t8.identity_type IS NULL,'',t8.identity_type) SEPARATOR '/' ) AS participantType,
               t1.meeting_status,
               t3.file_path
        FROM mdt_meeting_record t1
                 LEFT JOIN mdt_case t2
                           ON t1.case_id = t2.case_id
                 LEFT JOIN mdt_meeting_video t3
                           ON t1.meeting_id = t3.meeting_id
                 LEFT JOIN sys_entrusts t4
                           ON t2.entrusts_id = t4.entrusts_id
                 LEFT JOIN auth_account t5
                           ON t1.mediator_id = t5.account_id
                 LEFT JOIN auth_company t6
                           ON t5.company_id = t6.company_id
                 LEFT JOIN mdt_meeting_record_litigant t7
                           ON t1.record_id = t7.record_id
                 LEFT JOIN mdt_case_litigant t8
                           ON t8.litigant_id = t7.litigant_id
    </sql>

    <sql id="condition">
        <if test="param != null">
            <if test="param.caseNo != null">
                and t2.case_no like concat('%', #{param.caseNo}, '%')
            </if>
            <if test="param.mediatorName != null">
                and t1.mediator_name = #{param.mediatorName}
            </if>
            <if test="param.mediatorId != null">
                and t1.mediator_id = #{param.mediatorId}
            </if>
            <if test="param.participant != null">
                and t1.participant like  concat('%', #{param.participant}, '%')
            </if>
            <if test="param.meetingStatus != null">
                and t1.meeting_status = #{param.meetingStatus}
            </if>
            <if test="param.mediationStartTime != null">
                and t1.mediation_time >= #{param.mediationStartTime}
            </if>
            <if test="param.mediationEndTime != null">
                and t1.mediation_time &lt;= #{param.mediationEndTime}
            </if>
        </if>
    </sql>

    <select id="pageList" resultType="com.sct.tiaojie.service.video.dto.MdtMeetingRecordDTO">
        <include refid="selectMeetingRecord"/>
        <where>
            <include refid="condition"/>
        </where>
        group by t1.record_id, t3.video_id
    </select>

    <resultMap id="listMap" type="com.sct.tiaojie.service.video.dto.MdtMeetingDTO" autoMapping="true">
        <id column="record_id" property="recordId" />
        <collection property="smsList" autoMapping="true" javaType="java.util.ArrayList"
                    ofType="com.sct.tiaojie.service.video.dto.MdtSmsInfo"
                    columnPrefix="msi_">
            <id column="sms_send_id" property="smsSendId" />
        </collection>
    </resultMap>

    <select id="meetingList" resultMap="listMap">
        SELECT t1.record_id,
               t1.case_id,
               t1.mediator_id,
               t1.meeting_name,
               t1.video_url,
               GROUP_CONCAT( t8.litigant_name SEPARATOR '/' ) AS participant,
               GROUP_CONCAT( IF(t9.identity_type IS NULL,'',t9.identity_type) SEPARATOR '/' ) AS participantType,
               t1.meeting_status,
               t1.password,
               t1.meeting_url,
               t1.mediation_time,
               t1.text_content,
               t5.sms_send_id as msi_sms_send_id,
               t4.contact_name as msi_contact_name,
               t5.phone_number as msi_phone_number,
               t5.send_status as msi_send_status,
               t5.receive_description as msi_receive_description,
               t5.content as msi_msg_content,
               t3.file_path
        FROM mdt_meeting_record t1
        INNER JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN mdt_meeting_video t3
        ON t1.meeting_id = t3.meeting_id
        LEFT JOIN mdt_meeting_record_litigant t8
        ON t1.record_id = t8.record_id
        LEFT JOIN mdt_case_litigant t9
        ON t8.litigant_id = t9.litigant_id
        LEFT JOIN mdt_sms t4
        ON t2.case_id = t4.case_id and t1.record_id = t4.opt_id
        LEFT JOIN mdt_sms_send_details t5
        ON t4.sms_send_id = t5.sms_send_id
        WHERE t1.case_id = #{caseId}
        group by t1.record_id, t3.video_id, t4.sms_id, t5.sms_details_id
    </select>

    <select id="selectMeetingRecord" resultType="com.sct.tiaojie.service.video.dto.MdtMeetingRecordDTO">
        <include refid="selectMeetingRecord"/>
        <where>
            <include refid="condition"/>
        </where>
        group by t1.record_id, t3.video_id
    </select>
</mapper>