<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.sys.entity.SysMenu">
        <id column="menu_id" property="menuId" />
        <result column="parent_menu_id" property="parentMenuId" />
        <result column="menu_name" property="menuName" />
        <result column="menu_module" property="menuModule" />
        <result column="menu_type" property="menuType" />
        <result column="tree_level" property="treeLevel" />
        <result column="menu_icon" property="menuIcon" />
        <result column="sort_no" property="sortNo" />
        <result column="menu_href" property="menuHref" />
        <result column="menu_flag" property="menuFlag" />
        <result column="menu_route_name" property="menuRouteName" />
        <result column="enable_flag" property="enableFlag" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        menu_id, parent_menu_id, menu_name, menu_module, menu_type, tree_level, menu_icon, sort_no, menu_href, menu_flag, menu_route_name, enable_flag, create_time, creator_id, update_time, updater_id
    </sql>

    <sql id="Base_Prefix_Column_List">
      m.menu_id, m.parent_menu_id, m.menu_name, m.menu_module, m.menu_type, m.tree_level, m.menu_icon, m.sort_no, m.menu_href, m.menu_flag, m.menu_route_name, m.enable_flag, m.create_time, m.creator_id, m.update_time, m.updater_id
    </sql>

    <select id="selectByRoleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Prefix_Column_List" />
        FROM
        sys_role AS r LEFT JOIN sys_role_menu_rela AS r_m on r.role_id = r_m.role_id
        LEFT JOIN sys_menu AS m on r_m.menu_id = m.menu_id
        WHERE m.enable_flag = 1
        AND r.role_id = #{roleId}
        ORDER BY
        m.tree_level, m.sort_no
    </select>

</mapper>