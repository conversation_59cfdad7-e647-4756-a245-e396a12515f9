<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.sys.entity.SysMsg">
        <id column="msg_id" property="msgId" />
        <result column="msg_module" property="msgModule" />
        <result column="msg_type" property="msgType" />
        <result column="msg_title" property="msgTitle" />
        <result column="msg_content" property="msgContent" />
        <result column="biz_id" property="bizId" />
        <result column="oth_param" property="othParam" />
        <result column="create_tm" property="createTm" />
        <result column="creator_id" property="creatorId" />
        <result column="update_tm" property="updateTm" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        msg_id, msg_module, msg_type, msg_title, msg_content, biz_id, oth_param, create_tm, creator_id, update_tm, updater_id
    </sql>
    <sql id="sys_dto_column_list">
            t1.msg_id,
            t1.msg_module,
            t1.msg_type,
            t1.msg_title,
            t1.msg_content,
            t1.biz_id,
            t1.oth_param,
            DATE_FORMAT(t1.create_tm, '%Y-%m-%d %H:%i:%S') AS create_tm,
            t1.creator_id,
            t2.receive_user_id,
            t2.from_user_id,
            t2.readed
    </sql>

    <select id="pageList" resultType="com.sct.tiaojie.service.sys.dto.SysMsgDTO">
        select
        <include refid="sys_dto_column_list"/>
        from sys_msg t1, sys_msg_receive t2
        where t1.msg_id = t2.msg_id
        and t2.receive_user_id = #{params.userId}
        and t1.company_id = #{companyId}
        <if test="params.readed != null">
            and t2.readed = #{params.readed}
        </if>
        <if test="params.msgTypes != null and !params.msgTypes.isEmpty">
            and t1.msg_type in
            <foreach collection="params.msgTypes" item="msgType" open="(" close=")" separator=",">
                #{msgType}
            </foreach>
        </if>
        <if test="params.msgModule != null">
            and t1.msg_module = #{params.msgModule}
        </if>
        <if test="params.msgTitle != null">
            and t1.msg_title like concat('%',#{params.msgTitle},'%')
        </if>
        <if test="params.msgContent != null">
            and t1.msg_content like concat('%',#{params.msgContent},'%')
        </if>
        <if test="params.createTm != null">
            and t1.create_tm &gt;= #{params.createTm}
        </if>
    </select>

    <select id="listUnReadMsg" resultType="com.sct.tiaojie.service.sys.dto.SysMsgDTO">
        select
        <include refid="sys_dto_column_list"/>
        from sys_msg t1, sys_msg_receive t2
        where t1.msg_id = t2.msg_id
            and t2.readed = 0
            and t2.receive_user_id = #{userId}
    </select>

    <select id="getDetail" resultType="com.sct.tiaojie.service.sys.dto.SysMsgDTO">
        SELECT
            <include refid="sys_dto_column_list" />
        FROM
            sys_msg t1, sys_msg_receive t2
        WHERE t1.msg_id = t2.msg_id
            AND t1.msg_id = #{msgId}
            AND t2.receive_user_id = #{receiveUserId}
    </select>


</mapper>