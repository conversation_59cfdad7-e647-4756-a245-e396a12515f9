<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.sys.entity.SysRole">
        <id column="role_id" property="roleId" />
        <result column="role_name" property="roleName" />
        <result column="role_status" property="roleStatus" />
        <result column="role_code" property="roleCode" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        role_id, role_name, role_status, role_code, create_time, creator_id, update_time, updater_id
    </sql>

    <select id="getRoles" resultType="com.sct.tiaojie.service.sys.dto.SysRoleDTO">
        SELECT t1.role_id, t1.role_name, t1.role_status, t1.group_name
        FROM sys_role t1
        INNER JOIN sys_employee_role_rela t2
        ON t1.role_id = t2.role_id
        WHERE t2.employee_id=#{employeeId}
    </select>
    <select id="pageList" resultType="com.sct.tiaojie.service.sys.dto.SysRoleDTO">
        SELECT role_id,
               role_name,
               role_remark,
               role_status
        FROM sys_role
        <where>
            company_id = #{companyId}
            <if test="param != null">
                <if test="param.roleName != null">
                    AND role_name LIKE CONCAT('%', #{param.roleName}, '%')
                </if>
            </if>
        </where>
    </select>

</mapper>