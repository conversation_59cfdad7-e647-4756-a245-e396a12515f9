<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysVerificationCodeMapper">

    <select id="verifyCode" resultType="com.sct.tiaojie.repository.sys.entity.SysVerificationCode">
        SELECT
            *
        FROM
            sys_verification_code
        WHERE
            used = 0
            AND send_type = #{sendType}
            AND biz_type = #{bizType}
            AND target_number = #{phoneNumber}
            AND used = 0
            AND expiration_time > CURRENT_TIMESTAMP()
        ORDER BY
            create_time DESC
        LIMIT 1
    </select>
</mapper>