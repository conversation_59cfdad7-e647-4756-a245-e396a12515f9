<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysLoginLogMapper">


    <select id="pageList" resultType="com.sct.tiaojie.service.sys.dto.SysLoginLogDTO">
        SELECT
        login_log_id,
        login_name,
        account_id,
        request_ip,
        employee_name,
        org_id,
        org_name,
        creator_id,
        create_time,
        login_location
        FROM sys_login_log
        <where>
            <if test="param != null">
                <if test="param.loginName != null">
                    AND login_name LIKE CONCAT('%', #{param.loginName}, '%')
                </if>
                <if test="param.requestIp != null">
                    AND request_ip = #{param.requestIp}
                </if>
                <if test="param.accountId != null">
                    AND account_id = #{param.accountId}
                </if>
                <if test="param.orgId != null">
                    AND org_id = #{param.orgId}
                </if>
                <if test="param.creatorId != null">
                    AND creator_id = #{param.creatorId}
                </if>
                <if test="param.employeeName != null">
                    AND employee_name LIKE CONCAT('%', #{param.employeeName}, '%')
                </if>
                <if test="param.optDateStart != null">
                    AND create_time >= #{param.optDateStart}
                </if>
                <if test="param.optDateEnd != null">
                    AND create_time &lt;= #{param.optDateEnd}
                </if>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>