<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysTaskRunLogMapper">
    <select id="pageQuery" resultType="com.sct.tiaojie.service.sys.dto.SysTaskRunLogDTO">
        select
        <if test="taskType != null and taskType == 1">
            b.batch_no,
        </if>
            log.*,
            e.employee_name as creatorName
        from sys_task_run_log log
        <if test="taskType != null and taskType == 1">
            inner join mdt_case_batch b on b.batch_id = log.biz_id
            and log.task_type = #{taskType}
        </if>
        left join auth_employee e on log.creator_id = e.account_id
        <where>
            <if test="companyId != null">
                and log.company_id = #{companyId}
            </if>
            <if test="param != null">
                <if test="param.bizId != null">
                    and log.biz_id = #{param.bizId}
                </if>
                <if test="param.taskStatus">
                    and log.task_status = #{param.taskStatus}
                </if>
                <if test="param.createTimeStart != null">
                    and log.create_time &gt;= STR_TO_DATE(#{param.createTimeStart},'%Y-%m-%d')
                </if>
                <if test="param.createTimeEnd != null">
                    and log.create_time &lt;= STR_TO_DATE(#{param.createTimeEnd},'%Y-%m-%d')
                </if>
                <if test="taskType != null and taskType == 1 and param.batchNo != null">
                    and b.batch_no like concat('%', #{param.batchNo}, '%')
                </if>
            </if>
        </where>
    </select>
</mapper>