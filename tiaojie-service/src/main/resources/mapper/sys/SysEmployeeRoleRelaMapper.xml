<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysEmployeeRoleRelaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.sys.entity.SysEmployeeRoleRela">
        <id column="rela_id" property="relaId" />
        <result column="employee_id" property="employeeId" />
        <result column="role_id" property="roleId" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        rela_id, employee_id, role_id, create_time, creator_id, update_time, updater_id
    </sql>

</mapper>