<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysBusinessTypeMapper">
    <resultMap id="resultMap" type="com.sct.tiaojie.service.sys.dto.SysBusinessTypeDTO">
        <result property="processTypeList" column="process_type_list" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <select id="selectAllBusinessTypes" resultMap="resultMap">
        SELECT *
        FROM sys_business_type
        ORDER BY parent_id ASC, business_type_id ASC
    </select>
</mapper>