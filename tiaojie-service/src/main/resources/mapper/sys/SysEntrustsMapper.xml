<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysEntrustsMapper">
    <select id="pageList" resultType="com.sct.tiaojie.service.sys.dto.SysEntrustsPageDTO">
      SELECT
      a.entrusts_id,
      a.entrusts_name,
      a.contacts_name,
      a.contact_way,
      a.entrusts_type,
      a.entrusts_address,
      em.employee_name AS creatorName,
      a.create_time,
      a.time_limit_status,
      a.time_limit,
      a.entrusts_code,
      relaMdtOrg
      FROM
      sys_entrusts a
      LEFT JOIN auth_employee em ON a.creator_id = em.account_id
      LEFT JOIN (
      SELECT
      GROUP_CONCAT( DISTINCT se.org_name ) AS relaMdtOrg,
      entrusts_id
      FROM
      sys_entrusts_mdt_org_rela sr
      JOIN sys_mdt_org se ON sr.org_id = se.org_id
      <where>
        <if test="param != null">
          <if test="param.relaMdtOrgIdList != null and !param.relaMdtOrgIdList.isEmpty">
            AND se.org_id IN
            <foreach collection="param.relaMdtOrgIdList" item="orgId" open="(" close=")" separator=",">
              #{orgId}
            </foreach>
          </if>
        </if>
      </where>
      GROUP BY
      sr.entrusts_id
      ) rt ON a.entrusts_id = rt.entrusts_id
        <where>
            <if test="param != null">
                <if test="param.relaMdtOrgIdList != null and !param.relaMdtOrgIdList.isEmpty">
                    AND rt.entrusts_id is not null
                </if>
                <if test="param.entrustsName != null and param.entrustsName !=''">
                    AND a.entrusts_name LIKE CONCAT('%', #{param.entrustsName}, '%')
                </if>
                <if test="param.entrustsType != null and param.entrustsType !=''">
                    AND a.entrusts_type LIKE CONCAT('%', #{param.entrustsType}, '%')
                </if>
                <if test="param.contactsName != null and param.contactsName !=''">
                    AND a.contacts_name LIKE CONCAT('%', #{param.contactsName}, '%')
                </if>
                <if test="param.creatorName != null and param.creatorName !=''">
                    AND em.employee_name LIKE CONCAT('%', #{param.creatorName}, '%')
                </if>
                <if test="param.startTime != null and param.startTime !=''">
                  AND DATE_FORMAT(a.create_time,'%Y-%m-%d') >= #{param.startTime}
                </if>
                <if test="param.endTime != null and param.endTime !=''">
                  AND DATE_FORMAT(a.create_time,'%Y-%m-%d') &lt;= #{param.endTime}
                </if>
            </if>
        </where>
        order by a.create_time desc
    </select>
    <select id="getEntrusts" resultType="com.sct.tiaojie.service.sys.dto.SysEntrustsDTO">
        select t1.*
        from sys_entrusts t1
        <where>
            <if test="param != null">
                <if test="param.entrustsIds != null and !param.entrustsIds.isEmpty">
                    AND t1.entrusts_id IN
                    <foreach collection="param.entrustsIds" item="entrustsId" open="(" close=")" separator=",">
                        #{entrustsId}
                    </foreach>
                </if>
                <if test="param.companyIds != null and !param.companyIds.isEmpty">
                    AND t1.company_id IN
                    <foreach collection="param.companyIds" item="companyId" open="(" close=")" separator=",">
                        #{companyId}
                    </foreach>
                </if>
            </if>
        </where>
    </select>
    <!-- 在对应的 Mapper XML 文件中添加 -->
    <select id="selectEntrustIdsByOrgUserIds" resultType="long">
        SELECT DISTINCT semor.entrusts_id
        FROM auth_account aac
        LEFT JOIN sys_mdt_org smo ON smo.company_id = aac.company_id
        LEFT JOIN sys_entrusts_mdt_org_rela semor ON semor.org_id = smo.org_id
        <where>
            <if test="accountIds != null and !accountIds.isEmpty()">
                aac.account_id IN
                <foreach item="id" collection="accountIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="accountIds == null or accountIds.isEmpty()">
                AND 1 = 0 <!-- 如果传递空集合，返回空结果 -->
            </if>
        </where>
        GROUP BY semor.entrusts_id
    </select>
</mapper>