<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysLoginAppearanceMapper">


    <select id="pageList" resultType="com.sct.tiaojie.service.sys.dto.SysLoginAppearanceDTO">
        SELECT
        a.appearance_id,
        a.appearance_name,
        a.appearance_user_name,
        a.appearance_path,
        a.appearance_domain_name,
        a.appearance_url,
        a.appearance_enable_status,
        a.navigation_path,
        a.tab_path,
        a.background_path,
        a.brand_path,
        a.creator_id,
        b.employee_name as creator_name,
        a.create_time,
        a.updater_id,
        c.employee_name as updater_name,
        a.update_time,
        a.is_deleted
        FROM
        sys_login_appearance a
        inner join auth_employee b on b.account_id = a.creator_id
        left join auth_employee c on c.account_id = a.updater_id
        <where>
            <if test="param != null">
                <if test="param.appearanceName != null and param.appearanceName != ''">
                    and a.appearance_name = #{param.appearanceName}
                </if>
                <if test="param.appearanceUserName != null and param.appearanceUserName != ''">
                    and a.appearance_user_name like concat('%',#{param.appearanceUserName},'%')
                </if>
                <if test="param.appearanceEnableStatus != null">
                    and a.appearance_enable_status = #{param.appearanceEnableStatus}
                </if>
                <if test="param.appearanceDomainName != null and param.appearanceDomainName != ''">
                    and a.appearance_domain_name = #{param.appearanceDomainName}
                </if>
                <if test="param.appearancePath != null and param.appearancePath != ''">
                    and a.appearance_path like concat('%',#{param.appearancePath},'%')
                </if>
                <if test="param.updaterName != null and param.updaterName != ''">
                    and c.employee_name = #{param.updaterName}
                </if>
            </if>
            and a.is_deleted = 0
        </where>
        order by a.create_time desc
    </select>


    <!-- 更新数据 -->
    <update id="update">
        update sys_login_appearance
        <set>
            <if test="appearanceName != null and appearanceName != ''">
                appearance_name = #{appearanceName},
            </if>
            <if test="appearanceUserName != null and appearanceUserName != ''">
                appearance_user_name = #{appearanceUserName},
            </if>
            <if test="appearancePath != null and appearancePath != ''">
                appearance_path = #{appearancePath},
            </if>
            <if test="appearanceDomainName != null and appearanceDomainName != ''">
                appearance_domain_name = #{appearanceDomainName},
            </if>
            <if test="appearanceUrl != null and appearanceUrl != ''">
                appearance_url = #{appearanceUrl},
            </if>
            <if test="appearanceEnableStatus != null">
                appearance_enable_status = #{appearanceEnableStatus},
            </if>
            <if test="creatorId != null and creatorId != ''">
                creator_id = #{creatorId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updaterId != null and updaterId != ''">
                updater_id = #{updaterId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="appearanceUserName != null and appearanceUserName != ''">
                appearance_user_name = #{appearanceUserName},
            </if>
            <if test="navigationName != null and navigationName != ''">
                navigation_name = #{navigationName},
            </if>
            navigation_path = #{navigationPath},
            <if test="tabName != null and tabName != ''">
                tab_name = #{tabName},
            </if>
            tab_path = #{tabPath},
            background_path = #{backgroundPath},
            <if test="loginBoxTitle != null and loginBoxTitle != ''">
                login_box_title = #{loginBoxTitle},
            </if>
            <if test="brandTitle != null and brandTitle != ''">
                brand_title = #{brandTitle},
            </if>
            brand_path = #{brandPath},
        </set>
        where appearance_id = #{appearanceId}
    </update>
</mapper>