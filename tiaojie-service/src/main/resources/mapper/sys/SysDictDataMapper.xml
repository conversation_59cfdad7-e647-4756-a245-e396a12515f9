<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysDictDataMapper">
    <select id="getDictDateIdByDictTypeAndDictKey" resultType="java.lang.Long">
        SELECT sdd.dict_data_id from sys_dict_data sdd,sys_dict sd WHERE sdd.dict_id = sd.dict_id and sd.dict_type = #{dictType} and sdd.dict_key = #{dictKey}
    </select>
    <select id="getTopDictDataKey" resultType="java.lang.Integer">
        WITH RECURSIVE dict_path AS (
            SELECT
                dict_data_id,
                parent_id,
                dict_key
            FROM sys_dict_data
            WHERE dict_data_id = #{dictDateId}

            UNION ALL

            SELECT
                p.dict_data_id,
                p.parent_id,
                p.dict_key
            FROM sys_dict_data p
                     JOIN dict_path d ON p.dict_data_id = d.parent_id
            WHERE d.parent_id IS NOT NULL
        )
        SELECT dict_key
        FROM dict_path
        WHERE parent_id IS NULL
        UNION ALL
        SELECT dict_key
        FROM sys_dict_data
        WHERE dict_data_id = #{dictDateId} AND parent_id IS NULL
            LIMIT 1;

    </select>
</mapper>