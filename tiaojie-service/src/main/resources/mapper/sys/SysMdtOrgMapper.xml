<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysMdtOrgMapper">
    <select id="pageList" resultType="com.sct.tiaojie.service.sys.dto.SysMdtOrgPageDTO">
      SELECT
      a.org_id,
      a.org_name,
      a.org_type,
      a.mdt_scope,
      a.principal,
      a.contact_way,
      a.create_time,
      mc.handleCaseCount,
      rt.relaEntrusts,
      em.employee_name as creatorName
      FROM
      sys_mdt_org a
      LEFT JOIN  auth_employee em on a.creator_id = em.account_id
      LEFT JOIN ( SELECT count( 1 ) AS handleCaseCount, org_id FROM mdt_case WHERE mdt_case_status = '35' GROUP BY org_id ) mc ON a.org_id = mc.org_id
      LEFT JOIN (
      SELECT
      GROUP_CONCAT( se.entrusts_name ) AS relaEntrusts,
      org_id
      FROM
      sys_entrusts_mdt_org_rela sr
      JOIN sys_entrusts se ON sr.entrusts_id = se.entrusts_id
      <where>
        <if test="param != null">
          <if test="param.relaEntrustsIdList != null and !param.relaEntrustsIdList.isEmpty">
            AND se.entrusts_id IN
            <foreach collection="param.relaEntrustsIdList" item="entrustsId" open="(" close=")" separator=",">
              #{entrustsId}
            </foreach>
          </if>
        </if>
      </where>
      GROUP BY sr.org_id) rt ON a.org_id = rt.org_id
        <where>
            <if test="param != null">
                <if test="param.relaEntrustsIdList != null and !param.relaEntrustsIdList.isEmpty">
                  AND rt.org_id is not null
                </if>
                <if test="param.orgName != null and param.orgName !=''">
                    AND a.org_name LIKE CONCAT('%', #{param.orgName}, '%')
                </if>
                <if test="param.orgType != null and param.orgType !=''">
                    AND a.org_type = #{param.orgType}
                </if>
                <if test="param.principal != null and param.principal !=''">
                    AND a.principal LIKE CONCAT('%', #{param.principal}, '%')
                </if>
                <if test="param.creatorName != null and param.creatorName !=''">
                    AND em.employee_name LIKE CONCAT('%', #{param.creatorName}, '%')
                </if>
                <if test="param.startTime != null and param.startTime !=''">
                  AND DATE_FORMAT(a.create_time,'%Y-%m-%d') >= #{param.startTime}
                </if>
                <if test="param.endTime != null and param.endTime !=''">
                  AND DATE_FORMAT(a.create_time,'%Y-%m-%d') &lt;= #{param.endTime}
                </if>
            </if>
        </where>
      order by a.create_time desc
    </select>
    <select id="getOrg" resultType="com.sct.tiaojie.service.sys.dto.SysMdtOrgDTO">
        select t1.*
        from sys_mdt_org t1
        left join sys_entrusts_mdt_org_rela t2
        on t1.org_id = t2.org_id
        <where>
            <if test="param != null">
                <if test="param.companyIds != null and !param.companyIds.isEmpty">
                    AND t1.company_id IN
                    <foreach collection="param.companyIds" item="companyId" open="(" close=")" separator=",">
                        #{companyId}
                    </foreach>
                </if>
                <if test="param.entrustsId != null">
                    and t2.entrusts_id = #{param.entrustsId}
                </if>
            </if>
        </where>
        group by t1.org_id
    </select>

</mapper>