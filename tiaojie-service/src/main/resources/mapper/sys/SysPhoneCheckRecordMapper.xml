<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysPhoneCheckRecordMapper">
    <select id="getUserConcatPhone" resultType="com.sct.tiaojie.service.sys.dto.SysPhoneCheckRecordDto">
        SELECT
        a.case_id,
        a.case_no,
        a.entrusts_id,
        b.entrusts_name,
        c.litigant_id,
        c.litigant_name,
        c.identity_type,
        c.litigant_type,
        c.litigant_phone
        FROM
        mdt_case a
        INNER JOIN sys_entrusts b ON b.entrusts_id = a.entrusts_id
        INNER JOIN mdt_case_litigant c ON c.case_id = a.case_id
        <where>
            a.case_id = #{caseId} AND c.litigant_id = #{litigantId}
        </where>
    </select>

    <sql id="selectPhoneCheckLog">
        SELECT a.record_id,
               a.case_id,
               a.case_no,
               a.entrusts_name,
               a.operator_name,
               a.operator_company_id,
               a.operator_id,
               b.company_name as operator_company_name,
               c.litigant_phone,
               a.phone_status,
               a.phone_check_result,
               a.check_date,
               a.http_status,
               a.identity_type,
               c.litigant_name,
               a.litigant_type
        FROM sys_phone_check_record a
                 INNER JOIN auth_company b ON b.company_id = a.operator_company_id
                 INNER JOIN mdt_case_litigant c ON c.litigant_id = a.litigant_id
    </sql>

    <sql id="condition">
        <if test="param != null">
            <if test="param.caseNo != null">
                and a.case_no = #{param.caseNo}
            </if>
            <if test="param.litigantName != null">
                and c.litigant_name like concat('%',#{param.litigantName},'%')
            </if>
            <if test="param.checkDate != null">
                and date(a.check_date) = #{param.checkDate}
            </if>
            <if test="param.checkDateStart != null and param.checkDateStart != ''">
                and a.check_date >= #{param.checkDateStart}
            </if>
            <if test="param.checkDateEnd != null">
                and a.check_date &lt;= #{param.checkDateEnd}
            </if>
            <if test="param.phoneStatus != null">
                and a.phone_status = #{param.phoneStatus}
            </if>
            <if test="param.phoneCheckResult != null and param.phoneCheckStatus != null">
                and a.phone_check_result = #{param.phoneCheckResult}
            </if>
        </if>
    </sql>

    <select id="getPhoneCheckPageLog" resultType="com.sct.tiaojie.service.sys.dto.SysPhoneCheckRecordDto">
        <include refid="selectPhoneCheckLog"/>
        <where>
            <include refid="condition"/>
        </where>
        order by a.check_date desc
    </select>


    <select id="getPhoneCheckLogByCaseId" resultType="com.sct.tiaojie.service.sys.dto.SysPhoneCheckRecordDto">
        SELECT a.record_id,
        a.case_id,
        a.case_no,
        a.entrusts_name,
        a.operator_name,
        a.operator_company_id,
        a.operator_id,
        b.company_name,
        a.litigant_phone,
        a.phone_status,
        a.phone_check_result,
        a.check_date,
        a.http_status,
        a.identity_type,
        c.litigant_name,
        a.litigant_type
        FROM sys_phone_check_record a
        INNER JOIN auth_company b ON b.company_id = a.operator_company_id
        INNER JOIN mdt_case_litigant c ON c.litigant_id = a.litigant_id
        <where>
            and a.case_id = #{caseId}
        </where>
        order by a.check_date desc
    </select>

    <select id="selectPhoneCheckLogList" resultType="com.sct.tiaojie.service.sys.dto.SysPhoneCheckRecordDto">
        <include refid="selectPhoneCheckLog"/>
        <where>
            <include refid="condition"/>
        </where>
        order by a.check_date desc
    </select>

    <!-- 更新数据 -->
    <update id="update">
        update sys_phone_check_record
        <set>
            <if test="recordId != null and recordId != ''">
                record_id = #{recordId},
            </if>
            <if test="caseId != null and caseId != ''">
                case_id = #{caseId},
            </if>
            <if test="caseNo != null and caseNo != ''">
                case_no = #{caseNo},
            </if>
            <if test="entrustsId != null and entrustsId != ''">
                entrusts_id = #{entrustsId},
            </if>
            <if test="entrustsName != null and entrustsName != ''">
                entrusts_name = #{entrustsName},
            </if>
            <if test="operatorId != null and operatorId != ''">
                operator_id = #{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">
                operator_name = #{operatorName},
            </if>
            <if test="operatorCompanyId != null and operatorCompanyId != ''">
                operator_company_id = #{operatorCompanyId},
            </if>
            <if test="litigantId != null and litigantId != ''">
                litigant_id = #{litigantId},
            </if>
            <if test="litigantName != null and litigantName != ''">
                litigant_name = #{litigantName},
            </if>
            <if test="identityType != null and identityType != ''">
                identity_type = #{identityType},
            </if>
            <if test="litigantType != null and litigantType != ''">
                litigant_type = #{litigantType},
            </if>
            <if test="phoneStatus != null and phoneStatus != ''">
                phone_status = #{phoneStatus},
            </if>
            <if test="phoneCheckResult != null and phoneCheckResult != ''">
                phone_check_result = #{phoneCheckResult},
            </if>
            <if test="checkDate != null and checkDate != ''">
                check_date = #{checkDate},
            </if>
            <if test="httpStatus != null and httpStatus != ''">
                http_status = #{httpStatus},
            </if>
            <if test="httpResponse != null and httpResponse != ''">
                http_response = #{httpResponse},
            </if>
        </set>
        where record_id = #{recordId}
    </update>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="record_id" useGeneratedKeys="true">
        insert into
        sys_phone_check_record(record_id,case_id,case_no,entrusts_id,entrusts_name,operator_id,operator_name,operator_company_id,litigant_id,litigant_name,identity_type,litigant_type,phone_status,phone_check_result,check_date,http_status,http_response,litigant_phone)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.recordId},#{entity.caseId},#{entity.caseNo},#{entity.entrustsId},#{entity.entrustsName},
            #{entity.operatorId},#{entity.operatorName},#{entity.operatorCompanyId},#{entity.litigantId},
            #{entity.litigantName},#{entity.identityType},#{entity.litigantType},#{entity.phoneStatus},
            #{entity.phoneCheckResult},#{entity.checkDate},#{entity.httpStatus},#{entity.httpResponse},#{entity.litigantPhone})
        </foreach>
    </insert>

</mapper>