<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysEntrustsMdtOrgRelaMapper">

    <!-- 通用查询结果列 -->
    <select id="getOrgList" resultType="com.sct.tiaojie.repository.sys.entity.SysMdtOrg">
        select t2.*
        from sys_entrusts_mdt_org_rela t1
        left join sys_mdt_org t2
        on t1.org_id = t2.org_id
        where t1.entrusts_id = #{entrustsId}
    </select>

    <select id="getOrgListByDeptId" resultType="com.sct.tiaojie.repository.sys.entity.SysMdtOrg">
        select t2.*
        from sys_entrusts_mdt_org_rela t1
        left join sys_mdt_org t2
        on t1.org_id = t2.org_id
        where t1.dept_id = #{deptId}
    </select>

</mapper>