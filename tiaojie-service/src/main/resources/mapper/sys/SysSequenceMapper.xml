<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysSequenceMapper">
    <select id="getCaseNoSequence" resultType="com.sct.tiaojie.service.sys.dto.SysSequenceDTO">
        SELECT sequence_id,
               sequence_title,
               sequence_scope,
               digital_length,
               sequence_start AS lastValue,
               sequence_current
        FROM sys_sequence
        WHERE sequence_type = 'CASE_NO'
        AND company_id = #{companyId}
    </select>
</mapper>