<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysDictMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sct.tiaojie.repository.sys.entity.SysDict" id="sysDictMap">
        <result property="dictId" column="dict_id"/>
        <result property="dictType" column="dict_type"/>
        <result property="dictName" column="dict_name"/>
        <result property="dictStatus" column="dict_status"/>
        <result property="sysFlag" column="sys_flag"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="creatorId" column="creator_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="updaterId" column="updater_id"/>
    </resultMap>

    <!-- 带字典数据的查询结果 -->
    <resultMap id="SysDictWithData" type="com.sct.tiaojie.service.sys.dto.SysDictDTO">
        <id column="dict_id" property="dictId"/>
        <result column="dict_type" property="dictType"/>
        <result column="dict_name" property="dictName"/>
        <result column="dict_status" property="dictStatus"/>
        <result column="sys_flag" property="sysFlag"/>
        <result column="remark" property="remark"/>
        <!-- 字典数据集合映射 -->
        <collection property="sysDictDataList" ofType="com.sct.tiaojie.service.sys.dto.SysDictDataDTO">
            <result column="dict_id" property="dictId"/>
            <result column="dict_data_id" property="dictDataId"/>
            <result column="dict_key" property="dictKey"/>
            <result column="dict_tag" property="dictTag"/>
            <result column="sys_flag" property="sysFlag"/>
            <result column="parent_id" property="parentId"/>
        </collection>
    </resultMap>


    <select id="getAllSysDictWithData" resultMap="SysDictWithData">
           select a.dict_id,a.dict_type,a.dict_name,a.dict_status,a.remark,
                    b.sys_flag,
                    b.dict_data_id ,
                    b.dict_key ,
                    b.dict_tag ,
                    b.enable_flag,
                    b.parent_id,
                    b.business_type
            from sys_dict a join sys_dict_data b on a.dict_id = b.dict_id
           order by b.business_type
    </select>
    <select id="pageList" resultType="com.sct.tiaojie.service.sys.dto.SysDictDTO">
        SELECT *
        FROM sys_dict
        <where>
            sys_flag = 0
            <if test="param != null">
                <if test="param.dictName != null">
                    AND dict_name LIKE CONCAT('%', #{param.dictName}, '%')
                </if>
                <if test="param.remark != null">
                    AND remark LIKE CONCAT('%', #{param.remark}, '%')
                </if>
            </if>
        </where>
    </select>
</mapper>