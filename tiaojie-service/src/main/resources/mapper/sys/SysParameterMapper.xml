<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysParameterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.sys.entity.SysParameter">
        <id column="module_code" property="moduleCode" />
        <result column="param_code" property="paramCode" />
        <result column="param_name" property="paramName" />
        <result column="param_value" property="paramValue" />
        <result column="param_cmnt" property="paramCmnt" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 查询映射结果 DTO -->
    <resultMap id="BaseResultMapDTO" type="com.sct.tiaojie.service.sys.dto.SysParameterDTO">
        <id column="module_code" property="moduleCode" />
        <result column="param_code" property="paramCode" />
        <result column="param_name" property="paramName" />
        <result column="param_value" property="paramValue" />
        <result column="param_cmnt" property="paramCmnt" />
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        module_code, param_code, param_name, param_value, param_cmnt, create_tm, creator_id, update_tm, updater_id
    </sql>

    <!-- DTO查询结果列 -->
    <sql id="DTO_Column_List">
        module_code, param_code, param_name, param_value, param_cmnt
    </sql>

    <select id="getSysParameterByModule" resultMap="BaseResultMapDTO">
        select
        <include refid="DTO_Column_List"/>
        from sys_parameter
        where
        module_code = #{moduleCode}
    </select>

    <select id="getSysParameterByModuleAndParam" resultMap="BaseResultMapDTO">
        select
        <include refid="DTO_Column_List"/>
        from sys_parameter
        where
        module_code = #{moduleCode}
        and
        param_code = #{paramCode}
    </select>

    <select id="getAllSysParameter" resultMap="BaseResultMapDTO">
        select
        <include refid="DTO_Column_List"/>
        from sys_parameter
    </select>
    <select id="getValueSysParameterByModuleAndParam" resultType="java.lang.String">
        select
        param_value
        from sys_parameter
        where
        module_code = #{moduleCode}
        and
        param_code = #{paramCode}
    </select>
</mapper>