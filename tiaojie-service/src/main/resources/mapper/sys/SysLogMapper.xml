<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.sys.mapper.SysLogMapper">

    <select id="pageList" resultType="com.sct.tiaojie.service.auth.dto.OperateLogPageDTO">
        SELECT
               t1.log_id,
               t1.company_id,
               t1.account_id,
               t1.account_name,
               t1.log_title,
               t1.request_ip,
               t1.request_url,
               t1.creator_id,
               t1.create_time,
               t1.end_time,
               t1.account_org_id,
               t1.account_org_name,
               t1.account_dept_id,
               t1.account_dept_name,
               t1.operate_type,
			   GROUP_CONCAT(DISTINCT t2.case_id SEPARATOR ',') as caseId,
			   GROUP_CONCAT(DISTINCT t2.case_no SEPARATOR ',') as caseNo,
			   GROUP_CONCAT(DISTINCT t2.entrusts_id SEPARATOR ',') as entrustsId,
			   GROUP_CONCAT(DISTINCT t2.entrusts_name SEPARATOR ',') as entrustsName,
		       GROUP_CONCAT(DISTINCT t2.entrusts_dept_id SEPARATOR ',') as entrustsDeptId,
			   GROUP_CONCAT(DISTINCT t2.entrusts_dept_name SEPARATOR ',') as entrustsDeptName
        FROM sys_log AS t1
				LEFT JOIN sys_log_case AS t2
				ON t1.log_id = t2.log_id
                <include refid="whereSql"/>
				GROUP BY t1.log_id
				ORDER BY create_time DESC
    </select>

    <sql id="whereSql">
        <where>
            <if test="param != null">
                <if test="param.caseId != null">
                    AND t2.case_id  = #{param.caseId}
                </if>
                <if test="param.logTitle != null">
                    AND t1.log_title LIKE CONCAT('%', #{param.logTitle}, '%')
                </if>
                <if test="param.requestIp != null">
                    AND t1.request_ip = #{param.requestIp}
                </if>
                <if test="param.accountId != null">
                    AND t1.account_id = #{param.accountId}
                </if>
                <if test="param.accountName != null">
                    AND t1.account_name LIKE CONCAT('%', #{param.accountName}, '%')
                </if>
                <if test="param.operateType != null">
                    AND t1.operate_type = #{param.operateType}
                </if>
                <if test="param.optDateStart != null">
                    AND t1.create_time >= #{param.optDateStart}
                </if>
                <if test="param.optDateEnd != null">
                    AND t1.create_time &lt;= #{param.optDateEnd}
                </if>
                <if test="param.accountIdList != null and !param.accountIdList.isEmpty">
                    AND t1.account_id IN
                    <foreach collection="param.accountIdList" item="accountId" open="(" close=")" separator=",">
                        #{accountId}
                    </foreach>
                </if>
                <if test="param.accountOrgIdList != null and !param.accountOrgIdList.isEmpty">
                    AND t1.account_org_id IN
                    <foreach collection="param.accountOrgIdList" item="accountOrgId" open="(" close=")" separator=",">
                        #{accountOrgId}
                    </foreach>
                </if>
                <if test="param.accountDeptIdList != null and !param.accountDeptIdList.isEmpty">
                    AND t1.account_dept_id IN
                    <foreach collection="param.accountDeptIdList" item="accountDeptId" open="(" close=")" separator=",">
                        #{accountDeptId}
                    </foreach>
                </if>
                <if test="param.entrustsIdList != null and !param.entrustsIdList.isEmpty">
                    AND t2.entrusts_id IN
                    <foreach collection="param.entrustsIdList" item="entrustsId" open="(" close=")" separator=",">
                        #{entrustsId}
                    </foreach>
                </if>
                <if test="param.caseNo != null">
                    AND t2.case_no LIKE CONCAT('%', #{param.caseNo}, '%')
                </if>
                <if test="param.entrustsDeptIdList != null and !param.entrustsDeptIdList.isEmpty">
                    AND t2.entrusts_dept_id IN
                    <foreach collection="param.entrustsDeptIdList" item="entrustsDeptId" open="(" close=")" separator=",">
                        #{entrustsDeptId}
                    </foreach>
                </if>
            </if>
        </where>
    </sql>

</mapper>