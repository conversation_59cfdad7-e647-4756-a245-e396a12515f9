<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthEmployeeDeptRelaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.auth.entity.AuthEmployeeDeptRela">
        <id column="rela_id" property="relaId" />
        <result column="employee_id" property="employeeId" />
        <result column="company_id" property="companyId" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        rela_id, employee_id, company_id, create_time, creator_id, update_time, updater_id
    </sql>
    <select id="selectDeptNameByEmployeeId" resultType="java.lang.String">
        SELECT GROUP_CONCAT(d.dept_name SEPARATOR ', ') AS dept_names
        FROM auth_dept d
                 JOIN auth_employee_dept_rela ed ON ed.dept_id = d.dept_id
        WHERE ed.employee_id = #{employeeId}
    </select>

</mapper>