<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthOperationStatisticsMapper">

    <select id="getMediateWorkSituation" resultType="com.sct.tiaojie.repository.auth.entity.AuthOperationStatistics">
        SELECT
        statistic_year,
        statistic_month,
        statistic_day,
        login_name,
        employee_name,
        dept_id,
        dept_name,
        org_id,
        org_name,
        operation_duration,
        operation_gap
        FROM
        auth_operation_statistics
        WHERE
        <if test="orgIdList != null and !orgIdList.isEmpty">
            org_id IN
            <foreach item="orgId" collection="orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
            AND
        </if>
        STR_TO_DATE(CONCAT(statistic_year, '-', statistic_month, '-', statistic_day),'%Y-%m-%d') BETWEEN #{dateStart} AND #{dateEnd}
    </select>


</mapper>