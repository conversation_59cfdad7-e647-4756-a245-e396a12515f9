<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.auth.entity.AuthCompany">
        <id column="company_id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="parent_company_id" property="parentCompanyId" />
        <result column="company_status" property="companyStatus" />
        <result column="company_remark" property="companyRemark" />
        <result column="company_icon" property="companyIcon" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        comp_id, comp_name, parent_comp_id, comp_status, comp_remark, comp_icon, create_time, creator_id, update_time, updater_id
    </sql>
    <select id="getCompany" resultType="com.sct.tiaojie.service.auth.dto.AuthCompanyDTO">
        select t1.*
        from auth_company t1
        <where>
            <if test="param != null">
                <if test="param.companyStatus != null">
                    and t1.company_status = #{param.companyStatus}
                </if>
            </if>
        </where>
    </select>

</mapper>