<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.auth.entity.AuthDept">
        <id column="dept_id" property="deptId" />
        <result column="dept_name" property="deptName" />
        <result column="parent_dept_id" property="parentDeptId" />
        <result column="tree_level" property="treeLevel" />
        <result column="company_id" property="companyId" />
        <result column="dept_status" property="deptStatus" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        dept_id, dept_name, parent_dept_id, tree_level, company_id, dept_status, create_time, creator_id, update_time, updater_id
    </sql>
    <select id="pageList" resultType="com.sct.tiaojie.service.auth.dto.DeptDTO">
        SELECT t1.dept_id,
               t1.dept_name,
               t1.parent_dept_id,
               t1.remark,
               t2.company_name
        FROM auth_dept t1
        LEFT JOIN auth_company t2
        ON t1.company_id = t2.company_id
        <where>
            <if test="companyId != null">
                t1.company_id = #{companyId}
            </if>
            <if test="param != null">
                <if test="param.deptName != null">
                    AND t1.dept_name LIKE CONCAT('%', #{param.deptName}, '%')
                </if>
            </if>
        </where>
    </select>
    <select id="getDept" resultType="com.sct.tiaojie.service.auth.dto.DeptDTO">
        select t1.*
        from auth_dept t1
        left join auth_employee_dept_rela t2
        on t1.dept_id = t2.dept_id
        left join sys_mdt_org t3
        on t1.company_id = t3.company_id
        <where>
            <if test="param != null">
                <if test="param.companyIds != null and !param.companyIds.isEmpty">
                    AND t1.company_id IN
                    <foreach collection="param.companyIds" item="companyId" open="(" close=")" separator=",">
                        #{companyId}
                    </foreach>
                </if>
                <if test="param.deptStatus != null">
                    and t1.dept_status = #{param.deptStatus}
                </if>
                <if test="param.employeeId != null">
                    and t2.employee_id = #{param.employeeId}
                </if>
                <if test="param.orgIds != null and !param.orgIds.isEmpty">
                    AND t3.org_id IN
                    <foreach collection="param.orgIds" item="orgId" open="(" close=")" separator=",">
                        #{orgId}
                    </foreach>
                </if>
            </if>
        </where>
        group by t1.dept_id
    </select>

    <select id="findMaxSortByCompanyId" resultType="java.lang.Integer">
      select COALESCE(max(ad.sort), 0) from  auth_dept ad where ad.company_id = #{companyId}
    </select>

    <select id="getAllDept" resultType="com.sct.tiaojie.service.auth.dto.DeptSmsDTO">
      select
      t1.dept_id,
      t1.dept_name,
      t1.tree_level,
      t1.remark,
      t1.sort,
      t1.company_id,
      t2.company_name,
      t2.company_type
      FROM auth_dept t1
      LEFT JOIN auth_company t2
      ON t1.company_id = t2.company_id
    </select>

</mapper>