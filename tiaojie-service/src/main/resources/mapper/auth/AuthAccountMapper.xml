<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.auth.entity.AuthAccount">
        <id column="account_id" property="accountId" />
        <result column="login_name" property="loginName" />
        <result column="login_pwd" property="loginPwd" />
        <result column="account_mobile" property="accountMobile" />
        <result column="account_email" property="accountEmail" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        account_id, login_name, login_pwd, account_mobile, account_email, create_time, creator_id, update_time, updater_id
    </sql>

</mapper>