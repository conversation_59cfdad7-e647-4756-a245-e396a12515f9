<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthRealNameOrderMapper">

    <select id="pageList" resultType="com.sct.tiaojie.service.auth.dto.RealNamePageDTO">
        SELECT t1.`name`,
               t1.id_card_no,
               t1.result,
               t1.create_time,
               t2.case_no,
               t1.order_id
        FROM auth_realname_order t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN mdt_case_batch t3
        ON t2.case_batch_id = t3.batch_id
        <include refid="whereSql"/>
    </select>
    <select id="getFaceImg" resultType="com.sct.tiaojie.service.mdt.dto.FaceImgDTO">
        SELECT t1.photo,
               t1.name,
               t1.expire_time
        FROM auth_realname_order t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN mdt_case_batch t3
        ON t2.case_batch_id = t3.batch_id
        <include refid="whereSql"/>
        <if test="orderIds != null and !orderIds.isEmpty">
            <where>
                AND t1.order_id IN
                <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </where>
        </if>
    </select>
    <sql id="whereSql">
        <where>
            <if test="companyId != null">
                AND t2.company_id = #{companyId}
            </if>
            <if test="param != null">
                <if test="param.name != null">
                    AND t1.`name` LIKE CONCAT('%', #{param.name}, '%')
                </if>
                <if test="param.result != null">
                    <if test="param.result">
                        AND t1.result = '认证成功'
                    </if>
                    <if test="!param.result">
                        AND (t1.result != '认证成功' OR t1.result IS NULL)
                    </if>
                </if>
                <if test="param.startTime != null">
                    AND t1.create_time >= #{param.startTime}
                </if>
                <if test="param.endTime != null">
                    AND t1.create_time &lt;= #{param.endTime}
                </if>
                <if test="param.caseNo != null">
                    AND t2.case_no = #{param.caseNo}
                </if>
                <if test="param.batchNo != null">
                    AND t3.batch_no = #{param.batchNo}
                </if>
            </if>
        </where>
    </sql>
</mapper>