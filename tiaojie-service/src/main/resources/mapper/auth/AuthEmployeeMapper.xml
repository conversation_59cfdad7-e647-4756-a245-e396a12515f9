<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.auth.mapper.AuthEmployeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sct.tiaojie.repository.auth.entity.AuthEmployee">
        <id column="employee_id" property="employeeId"/>
        <result column="employee_name" property="employeeName"/>
        <result column="employee_mobile" property="employeeMobile"/>
        <result column="employee_email" property="employeeEmail"/>
        <result column="create_time" property="createTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="update_time" property="updateTime"/>
        <result column="updater_id" property="updaterId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        employee_id
        , employee_name, employee_mobile, employee_email, create_time, creator_id, update_time, updater_id
    </sql>
    <update id="updateRelevanceAccountStatus">
        UPDATE auth_account aa
            JOIN auth_employee ae ON aa.account_id = ae.account_id
            SET aa.account_status = 0
        WHERE ae.employee_number = #{employeeNumber}
    </update>
    <select id="pageList" resultType="com.sct.tiaojie.service.auth.dto.AuthEmployeePageDTO">
        SELECT
        t1.employee_id,
        t1.employee_number,
        t1.employee_name,
        t1.account_id,
        t6.account_mobile AS employeeMobile,
        t3.dept_name,
        t6.account_status,
        t7.roleNames
        FROM
        auth_employee t1
        JOIN auth_account t6 ON t1.account_id = t6.account_id
        LEFT JOIN auth_employee_dept_rela t2 ON t1.employee_id = t2.employee_id
        LEFT JOIN auth_dept t3 ON t2.dept_id = t3.dept_id
        LEFT JOIN(SELECT
        t4.employee_id,
        GROUP_CONCAT( t5.role_name SEPARATOR ',' ) as roleNames
        FROM
        sys_employee_role_rela t4
        JOIN sys_role t5 ON t4.role_id = t5.role_id
        <if test="param.roleIdList != null and !param.roleIdList.isEmpty">
            AND t5.role_id IN
            <foreach collection="param.roleIdList" item="roleId" open="(" close=")" separator=",">
                #{roleId}
            </foreach>
        </if>
        GROUP BY t4.employee_id
        )t7 on t1.employee_id = t7.employee_id

        <where>
            <if test="param != null">
                <if test="param.companyId != null">
                    t1.company_id = #{param.companyId}
                </if>
                <if test="param.employeeName != null">
                    AND t1.employee_name LIKE CONCAT('%', #{param.employeeName}, '%')
                </if>
                <if test="param.employeeNumber != null">
                    AND t1.employee_number LIKE CONCAT('%', #{param.employeeNumber}, '%')
                </if>
                <if test="param.employeeMobile != null">
                    AND t6.account_mobile LIKE CONCAT('%', #{param.employeeMobile}, '%')
                </if>
                <if test="param.deptId != null">
                    AND t3.dept_id = #{param.deptId}
                </if>
                <if test="param.accountStatus != null">
                    AND t6.account_status = #{param.accountStatus}
                </if>
                <if test="param.roleIdList != null and !param.roleIdList.isEmpty">
                    AND t7.employee_id is not null
                </if>
            </if>
        </where>
    </select>
    <select id="getUserInfo" resultType="com.sct.tiaojie.service.auth.dto.UserInfoDTO">
        SELECT t1.employee_id,
               t1.account_id,
               t1.employee_name,
               t4.account_mobile AS employeeMobile,
               t1.employee_email,
               t3.dept_name,
               t6.company_name,
               t4.login_name,
               t4.is_first_login AS isFirstLogin,
               t4.pwd_update_time AS pwdUpdateTime,
               t1.company_id,
               t6.company_type,
               t1.employee_mobile AS landLineNumber,
               (SELECT GROUP_CONCAT(t5.role_name SEPARATOR ',')
                FROM auth_employee t7
                         LEFT JOIN sys_employee_role_rela t4
                                   ON t7.employee_id = t4.employee_id
                         LEFT JOIN sys_role t5
                                   ON t4.role_id = t5.role_id
                WHERE t7.employee_id = t1.employee_id) AS roleNames
        FROM auth_employee t1
                 LEFT JOIN auth_employee_dept_rela t2
                           ON t1.employee_id = t2.employee_id
                 LEFT JOIN auth_dept t3
                           ON t2.dept_id = t3.dept_id
                 LEFT JOIN auth_company t6
                           ON t3.company_id = t6.company_id
                 LEFT JOIN auth_account t4
                           ON t1.account_id = t4.account_id
        WHERE t1.employee_id = #{employeeId}
          AND t1.company_id = #{companyId}
    </select>
    <select id="getMediator" resultType="com.sct.tiaojie.service.auth.dto.MediatorDTO">
        SELECT ae.employee_id,
               ae.account_id,
               ae.employee_name,
               ae.employee_gender,
               count(mc.case_id) AS caseNumber
        FROM auth_employee ae
                 LEFT JOIN mdt_case mc
                           ON ae.account_id = mc.current_mediator_id
        WHERE mediator_flag = 1
          AND ae.company_id = #{companyId}
        GROUP BY ae.employee_id
    </select>
    <select id="getUserByDept" resultType="com.sct.tiaojie.service.auth.dto.AuthEmployeeDTO">
        SELECT t1.employee_id,
               t1.employee_name,
               t1.account_id,
               t1.employee_status
        FROM auth_employee t1
                 LEFT JOIN auth_employee_dept_rela t2
                           ON t1.employee_id = t2.employee_id
        WHERE t2.dept_id = #{deptId}
          AND t1.employee_status = 1
          AND t1.company_id = #{companyId}
    </select>
    <select id="getNameByIds" resultType="java.util.Map">
        SELECT employee_name AS employeeName,
        account_id AS accountId
        FROM auth_employee
        <where>
            <if test="mediatorIds != null and !mediatorIds.isEmpty">
                AND account_id IN
                <foreach collection="mediatorIds" item="mediatorId" open="(" close=")" separator=",">
                    #{mediatorId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getEmployee" resultType="com.sct.tiaojie.service.auth.dto.AuthEmployeeDTO">
        SELECT t.*
        from auth_employee t
        left join auth_employee_dept_rela t1
        on t.employee_id = t1.employee_id
        LEFT JOIN sys_mdt_org t2 ON t2.company_id = t.company_id
        <where>
            <if test="param != null">
                <if test="param.orgIds != null and !param.orgIds.isEmpty">
                    AND t2.org_id IN
                    <foreach collection="param.orgIds" item="orgId" open="(" close=")" separator=",">
                        #{orgId}
                    </foreach>
                </if>
                <if test="param.deptIds != null and !param.deptIds.isEmpty">
                    AND t1.dept_id IN
                    <foreach collection="param.deptIds" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="param.employeeStatus != null">
                    and t.employee_status = #{param.employeeStatus}
                </if>
                <if test="param.employeeId != null">
                    and t.employee_id = #{param.employeeId}
                </if>
            </if>
        </where>
        group by t.employee_id
    </select>
    <select id="getMediatorByDept" resultType="com.sct.tiaojie.service.auth.dto.AuthEmployeeDTO">
        SELECT t1.employee_id,
               t1.employee_name,
               t1.account_id,
               t1.employee_status
        FROM auth_employee t1
                 LEFT JOIN auth_employee_dept_rela t2
                           ON t1.employee_id = t2.employee_id
        WHERE t2.dept_id = #{deptId}
          AND t1.employee_status = 1
          AND t1.mediator_flag = 1
          AND t1.company_id = #{companyId}
    </select>

    <select id="getMediatorByDeptIdList" resultType="com.sct.tiaojie.service.auth.dto.AuthEmployeeDTO">
        SELECT t1.employee_id,
               t1.employee_name,
               t1.account_id,
               t1.employee_status
        FROM auth_employee t1
                 LEFT JOIN auth_employee_dept_rela t2
                           ON t1.employee_id = t2.employee_id
        WHERE t2.dept_id in
        <foreach item="deptId" collection="deptIdList" open="(" separator="," close=")">
            #{deptId}
        </foreach>
          AND t1.employee_status = 1
          AND t1.mediator_flag = 1
          AND t1.company_id = #{companyId}
    </select>

    <select id="batchListEmployees" resultType="com.sct.tiaojie.service.auth.dto.AuthEmployeeDTO">
        SELECT
        ae.account_id,
        ae.employee_id,
        ae.employee_name,
        ae.employee_status,
        aedr.dept_id,
        ad.dept_name
        FROM auth_employee ae
        LEFT JOIN auth_employee_dept_rela aedr
        ON aedr.employee_id = ae.employee_id
        LEFT JOIN auth_dept ad
        ON ad.dept_id = aedr.dept_id
        LEFT JOIN auth_account acc ON acc.account_id = ae.account_id
        <where>
            <if test="param != null">
                <if test="param.deptIds != null and !param.deptIds.isEmpty">
                    AND aedr.dept_id IN
                    <foreach collection="param.deptIds" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="param.employeeStatus != null">
                    and ae.employee_status = #{param.employeeStatus}
                </if>
                <if test="param.employeeId != null">
                    and ae.employee_id = #{param.employeeId}
                </if>
                <if test="param.accountStatus != null">
                    and acc.account_status = #{param.accountStatus}
                </if>
            </if>
        </where>
    </select>
    <select id="getRelevanceUserInfoList" resultType="com.sct.tiaojie.service.auth.dto.UserInfoDTO">
        SELECT
            e.employee_id,
            e.employee_name,
            e.employee_gender,
            e.account_id,
            c.company_id,
            c.company_type,
            c.company_name,
            c.company_remark
        FROM
            auth_employee e
                INNER JOIN auth_company c ON e.company_id = c.company_id
                INNER JOIN auth_account a ON a.account_id = e.account_id
        WHERE
            e.employee_number = #{employeeNumber}
          AND a.account_status = 1
    </select>
    <select id="checkRelevanceAccount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            auth_employee e
                INNER JOIN auth_account a ON a.account_id = e.account_id
        WHERE
            e.employee_number = #{employeeNumber}
          AND a.account_status = 1
    </select>

</mapper>