<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtContactRepairRecordMapper">

    <resultMap type="com.sct.tiaojie.repository.mdt.entity.MdtContactRepairRecord" id="MdtContactRepairRecordMap">
        <result property="recordId" column="record_id" jdbcType="INTEGER"/>
        <result property="repairId" column="repair_id" jdbcType="INTEGER"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="caseId" column="case_id" jdbcType="INTEGER"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="idCardSha" column="id_card_sha" jdbcType="VARCHAR"/>
        <result property="idCardMd5" column="id_card_md5" jdbcType="VARCHAR"/>
        <result property="repairerName" column="repairer_name" jdbcType="VARCHAR"/>
        <result property="repairerNumber" column="repairer_number" jdbcType="VARCHAR"/>
        <result property="litigantType" column="litigant_type" jdbcType="VARCHAR"/>
        <result property="identityType" column="identity_type" jdbcType="VARCHAR"/>
        <result property="phoneStatus" column="phone_status" jdbcType="VARCHAR"/>
        <result property="resultId" column="result_id" jdbcType="VARCHAR"/>
        <result property="maskModel" column="mask_model" jdbcType="INTEGER"/>
        <result property="phoneList" column="phone_list" jdbcType="VARCHAR"/>
        <result property="matchRepairResult" column="match_repair_result" jdbcType="VARCHAR"/>
        <result property="matchFailMessage" column="match_fail_message" jdbcType="VARCHAR"/>
        <result property="callNo" column="call_no" jdbcType="VARCHAR"/>
        <result property="displayNo" column="display_no" jdbcType="VARCHAR"/>
        <result property="phoneSeqNo" column="phone_seq_no" jdbcType="VARCHAR"/>
        <result property="phoneAreaCode" column="phone_area_code" jdbcType="VARCHAR"/>
        <result property="dataSource" column="data_source" jdbcType="VARCHAR"/>
        <result property="virtualNo" column="virtual_no" jdbcType="VARCHAR"/>
        <result property="bindId" column="bind_id" jdbcType="VARCHAR"/>
        <result property="numberRepairResult" column="number_repair_result" jdbcType="VARCHAR"/>
        <result property="numberFailMessage" column="number_fail_message" jdbcType="VARCHAR"/>
        <result property="litigantId" column="litigant_id" jdbcType="INTEGER"/>
        <result property="recordRepairStatus" column="record_repair_status" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="companyId" column="company_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="MdtContactRepairRecordMap">
        select record_id,
               repair_id,
               batch_no,
               case_id,
               id_card,
               id_card_sha,
               id_card_md5,
               repairer_name,
               repairer_number,
               litigant_type,
               identity_type,
               phone_status,
               result_id,
               mask_model,
               phone_list,
               match_repair_result,
               match_fail_message,
               call_no,
               display_no,
               phone_seq_no,
               phone_area_code,
               data_source,
               virtual_no,
               bind_id,
               number_repair_result,
               number_fail_message
        from mdt_contact_repair_record
        where record_id = #{recordId}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from mdt_contact_repair_record
        <where>
            <if test="recordId != null">
                and record_id = #{recordId}
            </if>
            <if test="repairId != null">
                and repair_id = #{repairId}
            </if>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="caseId != null">
                and case_id = #{caseId}
            </if>
            <if test="idCard != null and idCard != ''">
                and id_card = #{idCard}
            </if>
            <if test="idCardSha != null and idCardSha != ''">
                and id_card_sha = #{idCardSha}
            </if>
            <if test="idCardMd5 != null and idCardMd5 != ''">
                and id_card_md5 = #{idCardMd5}
            </if>
            <if test="repairerName != null and repairerName != ''">
                and repairer_name = #{repairerName}
            </if>
            <if test="repairerNumber != null and repairerNumber != ''">
                and repairer_number = #{repairerNumber}
            </if>
            <if test="litigantType != null and litigantType != ''">
                and litigant_type = #{litigantType}
            </if>
            <if test="identityType != null and identityType != ''">
                and identity_type = #{identityType}
            </if>
            <if test="phoneStatus != null and phoneStatus != ''">
                and phone_status = #{phoneStatus}
            </if>
            <if test="resultId != null and resultId != ''">
                and result_id = #{resultId}
            </if>
            <if test="maskModel != null">
                and mask_model = #{maskModel}
            </if>
            <if test="phoneList != null and phoneList != ''">
                and phone_list = #{phoneList}
            </if>
            <if test="matchRepairResult != null and matchRepairResult != ''">
                and match_repair_result = #{matchRepairResult}
            </if>
            <if test="matchFailMessage != null and matchFailMessage != ''">
                and match_fail_message = #{matchFailMessage}
            </if>
            <if test="callNo != null and callNo != ''">
                and call_no = #{callNo}
            </if>
            <if test="displayNo != null and displayNo != ''">
                and display_no = #{displayNo}
            </if>
            <if test="phoneSeqNo != null and phoneSeqNo != ''">
                and phone_seq_no = #{phoneSeqNo}
            </if>
            <if test="phoneAreaCode != null and phoneAreaCode != ''">
                and phone_area_code = #{phoneAreaCode}
            </if>
            <if test="dataSource != null and dataSource != ''">
                and data_source = #{dataSource}
            </if>
            <if test="virtualNo != null and virtualNo != ''">
                and virtual_no = #{virtualNo}
            </if>
            <if test="bindId != null and bindId != ''">
                and bind_id = #{bindId}
            </if>
            <if test="numberRepairResult != null and numberRepairResult != ''">
                and number_repair_result = #{numberRepairResult}
            </if>
            <if test="numberFailMessage != null and numberFailMessage != ''">
                and number_fail_message = #{numberFailMessage}
            </if>
        </where>
    </select>

    <select id="getLeastRecord" resultType="com.sct.tiaojie.service.mdt.dto.MdtContactRepairRecordDto">
        SELECT
        a.record_id,
        a.batch_no,
        a.case_id,
        mc.case_no,
        a.id_card,
        b.batch_status,
        a.repairer_name,
        a.repairer_number,
        a.phone_status,
        a.match_fail_message,
        b.user_name,
        b.repair_time,
        b.batch_expire_date,
        a.virtual_no,
        a.repair_virtual_no_time,
        a.match_repair_result,
        a.number_repair_result
        FROM
        mdt_contact_repair_record a
        INNER JOIN (
        SELECT
        ROW_NUMBER() OVER ( PARTITION BY b.id_card,b.case_id ORDER BY a.operation_time DESC ) AS rn,
        a.repair_id,
        b.record_id,
        a.batch_status,
        a.user_name,
        a.repair_time,
        a.batch_expire_date
        FROM mdt_contact_repair a
        INNER JOIN mdt_contact_repair_record b ON a.repair_id = b.repair_id
        ) b ON b.record_id = a.record_id
        AND b.repair_id = a.repair_id
        inner join mdt_case mc on a.case_id = mc.case_id
        <where>
            b.rn = 1 and a.case_id = #{caseId}
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="recordId" useGeneratedKeys="true">
        insert into mdt_contact_repair_record(repair_id, batch_no, case_id, id_card, id_card_sha, id_card_md5,
                                              repairer_name, repairer_number, litigant_type, identity_type,
                                              phone_status, result_id, mask_model, phone_list, match_repair_result,
                                              match_fail_message, call_no, display_no, phone_seq_no, phone_area_code,
                                              data_source, virtual_no, bind_id, number_repair_result,
                                              number_fail_message,litigant_id,record_repair_status,user_id,company_id)
        values (#{repairId}, #{batchNo}, #{caseId}, #{idCard}, #{idCardSha}, #{idCardMd5}, #{repairerName},
                #{repairerNumber}, #{litigantType}, #{identityType}, #{phoneStatus}, #{resultId}, #{maskModel},
                #{phoneList}, #{matchRepairResult}, #{matchFailMessage}, #{callNo}, #{displayNo}, #{phoneSeqNo},
                #{phoneAreaCode}, #{dataSource}, #{virtualNo}, #{bindId}, #{numberRepairResult}, #{numberFailMessage}, #{entity.litigantId},#{entity.recordRepairStatus},#{entity.userId},#{entity.companyId})
    </insert>

    <insert id="insertBatch" keyProperty="recordId" useGeneratedKeys="true">
        insert into mdt_contact_repair_record(record_id,repair_id, batch_no, case_id, id_card, id_card_sha, id_card_md5,
        repairer_name, repairer_number, litigant_type, identity_type, phone_status, result_id, mask_model, phone_list,
        match_repair_result, match_fail_message, call_no, display_no, phone_seq_no, phone_area_code, data_source,
        virtual_no, bind_id, number_repair_result, number_fail_message, litigant_id, record_repair_status, user_id, company_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.recordId},#{entity.repairId}, #{entity.batchNo}, #{entity.caseId}, #{entity.idCard},
            #{entity.idCardSha},
            #{entity.idCardMd5}, #{entity.repairerName}, #{entity.repairerNumber}, #{entity.litigantType},
            #{entity.identityType}, #{entity.phoneStatus}, #{entity.resultId}, #{entity.maskModel}, #{entity.phoneList},
            #{entity.matchRepairResult}, #{entity.matchFailMessage}, #{entity.callNo}, #{entity.displayNo},
            #{entity.phoneSeqNo}, #{entity.phoneAreaCode}, #{entity.dataSource}, #{entity.virtualNo}, #{entity.bindId},
            #{entity.numberRepairResult}, #{entity.numberFailMessage}, #{entity.litigantId},
            #{entity.recordRepairStatus}, #{entity.userId}, #{entity.companyId})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="recordId" useGeneratedKeys="true">
        insert into mdt_contact_repair_record(repair_id, batch_no, case_id, id_card, id_card_sha, id_card_md5,
        repairer_name, repairer_number, litigant_type, identity_type, phone_status, result_id, mask_model, phone_list,
        match_repair_result, match_fail_message, call_no, display_no, phone_seq_no, phone_area_code, data_source,
        virtual_no, bind_id, number_repair_result, number_fail_message, litigant_id, record_repair_status,user_id, company_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.repairId}, #{entity.batchNo}, #{entity.caseId}, #{entity.idCard}, #{entity.idCardSha},
            #{entity.idCardMd5}, #{entity.repairerName}, #{entity.repairerNumber}, #{entity.litigantType},
            #{entity.identityType}, #{entity.phoneStatus}, #{entity.resultId}, #{entity.maskModel}, #{entity.phoneList},
            #{entity.matchRepairResult}, #{entity.matchFailMessage}, #{entity.callNo}, #{entity.displayNo},
            #{entity.phoneSeqNo}, #{entity.phoneAreaCode}, #{entity.dataSource}, #{entity.virtualNo}, #{entity.bindId},
            #{entity.numberRepairResult}, #{entity.numberFailMessage},#{entity.litigantId},
            #{entity.recordRepairStatus}, #{entity.userId}, #{entity.companyId})
        </foreach>
        on duplicate key update
        repair_id = values(repair_id),
        batch_no = values(batch_no),
        case_id = values(case_id),
        id_card = values(id_card),
        id_card_sha = values(id_card_sha),
        id_card_md5 = values(id_card_md5),
        repairer_name = values(repairer_name),
        repairer_number = values(repairer_number),
        litigant_type = values(litigant_type),
        identity_type = values(identity_type),
        phone_status = values(phone_status),
        result_id = values(result_id),
        mask_model = values(mask_model),
        phone_list = values(phone_list),
        match_repair_result = values(match_repair_result),
        match_fail_message = values(match_fail_message),
        call_no = values(call_no),
        display_no = values(display_no),
        phone_seq_no = values(phone_seq_no),
        phone_area_code = values(phone_area_code),
        data_source = values(data_source),
        virtual_no = values(virtual_no),
        bind_id = values(bind_id),
        number_repair_result = values(number_repair_result),
        number_fail_message = values(number_fail_message),
        litigant_id = values(litigant_id),
        record_repair_status = values(record_repair_status)
        user_id = values(user_id)
        company_id = values(company_id)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mdt_contact_repair_record
        <set>
            <if test="repairId != null">
                repair_id = #{repairId},
            </if>
            <if test="batchNo != null and batchNo != ''">
                batch_no = #{batchNo},
            </if>
            <if test="caseId != null">
                case_id = #{caseId},
            </if>
            <if test="idCard != null and idCard != ''">
                id_card = #{idCard},
            </if>
            <if test="idCardSha != null and idCardSha != ''">
                id_card_sha = #{idCardSha},
            </if>
            <if test="idCardMd5 != null and idCardMd5 != ''">
                id_card_md5 = #{idCardMd5},
            </if>
            <if test="repairerName != null and repairerName != ''">
                repairer_name = #{repairerName},
            </if>
            <if test="repairerNumber != null and repairerNumber != ''">
                repairer_number = #{repairerNumber},
            </if>
            <if test="litigantType != null and litigantType != ''">
                litigant_type = #{litigantType},
            </if>
            <if test="identityType != null and identityType != ''">
                identity_type = #{identityType},
            </if>
            <if test="phoneStatus != null and phoneStatus != ''">
                phone_status = #{phoneStatus},
            </if>
            <if test="resultId != null and resultId != ''">
                result_id = #{resultId},
            </if>
            <if test="maskModel != null">
                mask_model = #{maskModel},
            </if>
            <if test="phoneList != null and phoneList != ''">
                phone_list = #{phoneList},
            </if>
            <if test="matchRepairResult != null and matchRepairResult != ''">
                match_repair_result = #{matchRepairResult},
            </if>
            <if test="matchFailMessage != null and matchFailMessage != ''">
                match_fail_message = #{matchFailMessage},
            </if>
            <if test="callNo != null and callNo != ''">
                call_no = #{callNo},
            </if>
            <if test="displayNo != null and displayNo != ''">
                display_no = #{displayNo},
            </if>
            <if test="phoneSeqNo != null and phoneSeqNo != ''">
                phone_seq_no = #{phoneSeqNo},
            </if>
            <if test="phoneAreaCode != null and phoneAreaCode != ''">
                phone_area_code = #{phoneAreaCode},
            </if>
            <if test="dataSource != null and dataSource != ''">
                data_source = #{dataSource},
            </if>
            <if test="virtualNo != null and virtualNo != ''">
                virtual_no = #{virtualNo},
            </if>
            <if test="bindId != null and bindId != ''">
                bind_id = #{bindId},
            </if>
            <if test="numberRepairResult != null and numberRepairResult != ''">
                number_repair_result = #{numberRepairResult},
            </if>
            <if test="numberFailMessage != null and numberFailMessage != ''">
                number_fail_message = #{numberFailMessage},
            </if>
            <if test="litigantId != null and litigantId != ''">
                litigant_id = #{litigantId},
            </if>
            <if test="recordRepairStatus != null and recordRepairStatus != ''">
                record_repair_status = #{recordRepairStatus},
            </if>
            <if test="repairVirtualNoTime != null">
                repair_virtual_no_time = #{repairVirtualNoTime},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="companyId != null">
                company_id = #{companyId}
            </if>
        </set>
        where record_id = #{recordId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from mdt_contact_repair_record
        where record_id = #{recordId}
    </delete>

</mapper>

