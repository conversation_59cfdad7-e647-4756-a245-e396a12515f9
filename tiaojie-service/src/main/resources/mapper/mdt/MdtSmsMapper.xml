<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtSmsMapper">

    <select id="pageList" resultType="com.sct.tiaojie.service.mdt.dto.MdtSmsPageDTO">
        select
        t1.sms_id,
        t1.case_id,
        t1.sms_send_id,
        t1.contact_name,
        t1.contact_relation,
        t2.case_batch_id,
        t2.case_no,
        t2.case_type_id,
        t2.current_mediator_name,
        t2.current_mediator_id,
        t3.entrusts_id,
        t4.entrusts_name,
        t5.send_time,
        t6.phone_number,
        t6.receive_time,
        IF(t6.receive_status = 'SUCCESS', '成功', '失败') as receiveStatus,
        IF(t6.receive_description IS NULL, t6.send_message, t6.receive_description) AS receiveDescription
        FROM mdt_sms t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN mdt_case_batch t3
        ON t2.case_batch_id = t3.batch_id
        LEFT JOIN sys_entrusts t4
        ON t3.entrusts_id = t4.entrusts_id
        LEFT JOIN mdt_sms_send t5
        ON t1.sms_send_id = t5.sms_send_id
        LEFT JOIN mdt_sms_send_details t6
        ON t1.sms_send_id = t6.sms_send_id
        <include refid="whereSql"/>
    </select>
    <sql id="whereSql">
        <where>
            <if test="companyId != null">
                t2.company_id = #{companyId}
            </if>
            <if test="param != null">
                <if test="param.sendTimeStart != null">
                    AND t5.send_time >= #{param.sendTimeStart}
                </if>
                <if test="param.sendTimeEnd != null">
                    AND t5.send_time &lt;= #{param.sendTimeEnd}
                </if>
                <if test="param.batchNo != null">
                    AND t3.batch_no = #{param.batchNo}
                </if>
                <if test="param.entrustsId != null">
                    AND t3.entrusts_id = #{param.entrustsId}
                </if>
                <if test="param.caseTypeId != null">
                    AND t2.case_type_id = #{param.caseTypeId}
                </if>
                <if test="param.currentMediatorId != null">
                    AND t2.current_mediator_id = #{param.currentMediatorId}
                </if>
                <if test="param.createTimeStart != null">
                    AND t2.create_time >= #{param.createTimeStart}
                </if>
                <if test="param.createTimeEnd != null">
                    AND t2.create_time &lt;= #{param.createTimeEnd}
                </if>
                <if test="param.caseNo != null">
                    AND t2.case_no = #{param.caseNo}
                </if>
                <if test="param.caseStatus != null">
                    AND t2.case_status = #{param.caseStatus}
                </if>
                <if test="param.loanTotalStart != null">
                    AND t2.loan_total >= #{param.loanTotalStart}
                </if>
                <if test="param.loanTotalEnd != null">
                    AND t2.loan_total &lt;= #{param.loanTotalEnd}
                </if>
                <if test="param.receiveStatus != null">
                    AND t6.receive_status = #{param.receiveStatus}
                </if>
                <if test="param.mediatorIds != null and !param.mediatorIds.isEmpty">
                    AND t2.current_mediator_id IN
                    <foreach collection="param.mediatorIds" item="mediatorId" open="(" close=")" separator=",">
                        #{mediatorId}
                    </foreach>
                </if>
            </if>
        </where>
    </sql>
    <select id="export" resultType="com.sct.tiaojie.service.mdt.dto.MdtSmsPageDTO">
        select
        t1.sms_id,
        t1.case_id,
        t1.sms_send_id,
        t1.contact_name,
        t1.contact_relation,
        t2.case_batch_id,
        t2.case_no,
        t2.case_type_id,
        t2.current_mediator_name,
        t2.current_mediator_id,
        t3.entrusts_id,
        t4.entrusts_name,
        t5.send_time,
        t6.phone_number,
        t6.receive_time,
        IF(t6.receive_status = 'SUCCESS', '成功', '失败') as receiveStatus,
        IF(t6.receive_description IS NULL, t6.send_message, t6.receive_description) AS receiveDescription
        FROM mdt_sms t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN mdt_case_batch t3
        ON t2.case_batch_id = t3.batch_id
        LEFT JOIN sys_entrusts t4
        ON t3.entrusts_id = t4.entrusts_id
        LEFT JOIN mdt_sms_send t5
        ON t1.sms_send_id = t5.sms_send_id
        LEFT JOIN mdt_sms_send_details t6
        ON t1.sms_send_id = t6.sms_send_id
        <if test="smsIds != null and !smsIds.isEmpty">
            WHERE
            t1.sms_id
            IN
            <foreach collection="smsIds" item="smsId" open="(" close=")" separator=",">
                #{smsId}
            </foreach>
        </if>
        <include refid="whereSql"/>
    </select>
</mapper>