<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtContactRepairMapper">

    <resultMap type="com.sct.tiaojie.repository.mdt.entity.MdtContactRepair" id="MdtContactRepairMap">
        <result property="repairId" column="repair_id" jdbcType="INTEGER"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
        <result property="uploadRequestBody" column="upload_request_body" jdbcType="VARCHAR"/>
        <result property="operationTime" column="operation_time" jdbcType="TIMESTAMP"/>
        <result property="batchCount" column="batch_count" jdbcType="INTEGER"/>
        <result property="batchSuccessCount" column="batch_success_count" jdbcType="INTEGER"/>
        <result property="batchSuccessTime" column="batch_success_time" jdbcType="TIMESTAMP"/>
        <result property="batchExpireDate" column="batch_expire_date" jdbcType="TIMESTAMP"/>
        <result property="batchStatus" column="batch_status" jdbcType="VARCHAR"/>
        <result property="failCode" column="fail_code" jdbcType="VARCHAR"/>
        <result property="failMsg" column="fail_msg" jdbcType="VARCHAR"/>
        <result property="matchResult" column="match_result" jdbcType="VARCHAR"/>
        <result property="repairTime" column="repair_time" jdbcType="TIMESTAMP"/>
        <result property="companyId" column="company_id" jdbcType="INTEGER" />
        <result property="repairNetStatus" column="repair_net_status" jdbcType="INTEGER" />
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="MdtContactRepairMap">
        select repair_id,
               batch_no,
               user_id,
               user_name,
               company_name,
               department_name,
               upload_request_body,
               operation_time,
               batch_count,
               batch_success_count,
               batch_success_time,
               batch_expire_date,
               batch_status,
               fail_code,
               fail_msg,
               match_result,
               repair_time
        from mdt_contact_repair
        where repair_id = #{repairId}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from mdt_contact_repair
        <where>
            <if test="repairId != null">
                and repair_id = #{repairId}
            </if>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="companyName != null and companyName != ''">
                and company_name = #{companyName}
            </if>
            <if test="departmentName != null and departmentName != ''">
                and department_name = #{departmentName}
            </if>
            <if test="uploadRequestBody != null and uploadRequestBody != ''">
                and upload_request_body = #{uploadRequestBody}
            </if>
            <if test="operationTime != null">
                and operation_time = #{operationTime}
            </if>
            <if test="batchCount != null">
                and batch_count = #{batchCount}
            </if>
            <if test="batchSuccessCount != null">
                and batch_success_count = #{batchSuccessCount}
            </if>
            <if test="batchSuccessTime != null">
                and batch_success_time = #{batchSuccessTime}
            </if>
            <if test="batchExpireDate != null">
                and batch_expire_date = #{batchExpireDate}
            </if>
            <if test="batchStatus != null and batchStatus != ''">
                and batch_status = #{batchStatus}
            </if>
            <if test="failCode != null and failCode != ''">
                and fail_code = #{failCode}
            </if>
            <if test="failMsg != null and failMsg != ''">
                and fail_msg = #{failMsg}
            </if>
            <if test="matchResult != null and matchResult != ''">
                and match_result = #{matchResult}
            </if>
            <if test="repairTime != null">
                and repair_time = #{repairTime}
            </if>
        </where>
    </select>

    <sql id="selectRepairLog">
        SELECT
            c.case_no,
            a.batch_no,
            b.repairer_name,
            b.id_card,
            a.batch_status,
            b.repairer_number,
            b.phone_status,
            CASE
                WHEN b.match_fail_message IS NOT NULL THEN
                    b.match_fail_message
                ELSE b.number_fail_message END AS fail_message,
            CONCAT(a.company_name, '-', a.department_name) AS company_name,
            a.user_name,
            a.repair_time,
            b.record_id,
            b.case_id,
            a.batch_expire_date,
            b.virtual_no
        FROM mdt_contact_repair a
                 INNER JOIN mdt_contact_repair_record b ON a.repair_id = b.repair_id
                 INNER JOIN mdt_case c ON c.case_id = b.case_id
    </sql>

    <sql id="conditionRepairLog">
        <if test="param != null">
            <if test="param.caseNo != null and param.caseNo != ''">
                and c.case_no like concat('%',#{param.caseNo},'%')
            </if>
            <if test="param.litigantName != null and param.litigantName != ''">
                and b.repairer_name like concat('%', #{param.litigantName} ,'%')
            </if>
            <if test="param.company != null">
                and c.current_mediator_id = #{param.company}
            </if>
            <if test="param.litigantPhone != null and param.litigantPhone != ''">
                and b.repairer_number = #{param.litigantPhone}
            </if>
            <if test="param.matchRepairResult != null and param.matchRepairResult == '修复成功'">
                and b.match_repair_result = #{param.matchRepairResult}
            </if>
            <if test="param.matchRepairResult != null and param.matchRepairResult == '修复失败'">
                and b.match_repair_result is null
            </if>
            <if test="param.repairTimeStart != null and param.repairTimeStart != ''">
                and a.repair_time >= #{param.repairTimeStart}
            </if>
            <if test="param.repairTimeEnd != null and param.repairTimeEnd != ''">
                and a.repair_time &lt;= #{param.repairTimeEnd}
            </if>
        </if>
    </sql>



    <select id="pageRepairLog" resultType="com.sct.tiaojie.service.mdt.dto.MdtContactRepairRecordDto">
        <include refid="selectRepairLog"/>
        <where>
            <include refid="conditionRepairLog"/>
        </where>
        order by a.operation_time desc
    </select>

    <select id="repairLogList" resultType="com.sct.tiaojie.service.mdt.dto.MdtContactRepairRecordDto">
        SELECT
        c.case_no,
        a.batch_no,
        b.repairer_name,
        b.id_card,
        b.record_repair_status AS batch_status,
        b.repairer_number,
        b.phone_status,
        CASE
        WHEN b.number_fail_message IS NOT NULL THEN
        b.number_fail_message
        ELSE b.match_repair_result END AS fail_message,
        CONCAT(a.company_name, '-', a.department_name) AS company_name,
        a.user_name,
        a.repair_time,
        b.record_id,
        b.case_id
        FROM mdt_contact_repair a
        INNER JOIN mdt_contact_repair_record b ON a.repair_id = b.repair_id
        INNER JOIN mdt_case c ON c.case_id = b.case_id
        <where>
            c.case_id = #{caseId}
        </where>
        order by a.operation_time desc
    </select>

    <select id="selectRepairLogListByBo" resultType="com.sct.tiaojie.service.mdt.dto.MdtContactRepairRecordDto">
        <include refid="selectRepairLog"/>
        <where>
            <include refid="conditionRepairLog"/>
        </where>
        order by a.operation_time desc
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="repairId" useGeneratedKeys="true">
        insert into mdt_contact_repair(repair_id, batch_no, user_id, user_name, company_name, department_name,
                                       upload_request_body, operation_time, batch_count, batch_success_count,
                                       batch_success_time, batch_expire_date, batch_status, fail_code, fail_msg,
                                       match_result, repair_time, repair_net_status, company_id)
        values (#{repairId}, #{batchNo}, #{userId}, #{userName}, #{companyName}, #{departmentName},
                #{uploadRequestBody}, #{operationTime}, #{batchCount}, #{batchSuccessCount}, #{batchSuccessTime},
                #{batchExpireDate}, #{batchStatus}, #{failCode}, #{failMsg}, #{matchResult}, #{repairTime}, #{repairNetStatus}, #{companyId})
    </insert>

    <insert id="insertBatch" keyProperty="repairId" useGeneratedKeys="true">
        insert into mdt_contact_repair(batch_no, user_id, user_name, company_name, department_name, upload_request_body,
        operation_time, batch_count, batch_success_count, batch_success_time, batch_expire_date, batch_status,
        fail_code, fail_msg, match_result, repair_time, repair_net_status, company_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.batchNo}, #{entity.userId}, #{entity.userName}, #{entity.companyName}, #{entity.departmentName},
            #{entity.uploadRequestBody}, #{entity.operationTime}, #{entity.batchCount}, #{entity.batchSuccessCount},
            #{entity.batchSuccessTime}, #{entity.batchExpireDate}, #{entity.batchStatus}, #{entity.failCode},
            #{entity.failMsg}, #{entity.matchResult}, #{entity.repairTime}, #{entity.repairNetStatus}, #{entity.companyId})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="repairId" useGeneratedKeys="true">
        insert into mdt_contact_repair(batch_no, user_id, user_name, company_name, department_name, upload_request_body,
        operation_time, batch_count, batch_success_count, batch_success_time, batch_expire_date, batch_status,
        fail_code, fail_msg, match_result, repair_time, repair_net_status, company_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.batchNo}, #{entity.userId}, #{entity.userName}, #{entity.companyName}, #{entity.departmentName},
            #{entity.uploadRequestBody}, #{entity.operationTime}, #{entity.batchCount}, #{entity.batchSuccessCount},
            #{entity.batchSuccessTime}, #{entity.batchExpireDate}, #{entity.batchStatus}, #{entity.failCode},
            #{entity.failMsg}, #{entity.matchResult}, #{entity.repairTime}, #{entity.repairNetStatus}, #{entity.companyId})
        </foreach>
        on duplicate key update
        batch_no = values(batch_no),
        user_id = values(user_id),
        user_name = values(user_name),
        company_name = values(company_name),
        department_name = values(department_name),
        upload_request_body = values(upload_request_body),
        operation_time = values(operation_time),
        batch_count = values(batch_count),
        batch_success_count = values(batch_success_count),
        batch_success_time = values(batch_success_time),
        batch_expire_date = values(batch_expire_date),
        batch_status = values(batch_status),
        fail_code = values(fail_code),
        fail_msg = values(fail_msg),
        match_result = values(match_result),
        repair_time = values(repair_time),
        repair_net_status = values(repair_net_status),
        company_id = values(company_id)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mdt_contact_repair
        <set>
            <if test="batchNo != null and batchNo != ''">
                batch_no = #{batchNo},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="companyName != null and companyName != ''">
                company_name = #{companyName},
            </if>
            <if test="departmentName != null and departmentName != ''">
                department_name = #{departmentName},
            </if>
            <if test="uploadRequestBody != null and uploadRequestBody != ''">
                upload_request_body = #{uploadRequestBody},
            </if>
            <if test="operationTime != null">
                operation_time = #{operationTime},
            </if>
            <if test="batchCount != null">
                batch_count = #{batchCount},
            </if>
            <if test="batchSuccessCount != null">
                batch_success_count = #{batchSuccessCount},
            </if>
            <if test="batchSuccessTime != null">
                batch_success_time = #{batchSuccessTime},
            </if>
            <if test="batchExpireDate != null">
                batch_expire_date = #{batchExpireDate},
            </if>
            <if test="batchStatus != null and batchStatus != ''">
                batch_status = #{batchStatus},
            </if>
            <if test="failCode != null and failCode != ''">
                fail_code = #{failCode},
            </if>
            <if test="failMsg != null and failMsg != ''">
                fail_msg = #{failMsg},
            </if>
            <if test="matchResult != null and matchResult != ''">
                match_result = #{matchResult},
            </if>
            <if test="repairTime != null">
                repair_time = #{repairTime},
            </if>
            <if test="repairNetStatus != null">
                repair_net_status = #{repairNetStatus},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
        </set>
        where repair_id = #{repairId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from mdt_contact_repair
        where repair_id = #{repairId}
    </delete>

</mapper>

