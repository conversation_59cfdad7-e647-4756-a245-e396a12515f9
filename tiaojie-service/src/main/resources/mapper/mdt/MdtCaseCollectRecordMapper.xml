<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtCaseCollectRecordMapper">

    <select id="pageList" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseRecordPageDTO">
        SELECT t1.record_id,
               t2.case_no,
               t2.respond_name,
               t2.respond_id_cert_no,
               t1.contactor_user,
               t1.contact_phone,
               t1.phone_type,
               t1.contact_rela,
               t1.mdt_result,
               t1.reducte_amount,
               t1.reducte_type,
               t1.next_do_time,
               t4.entrusts_name,
               t1.account,
               t1.account_name,
               t1.create_tm AS createTime,
               t1.contact_result,
               t1.call_record
        FROM mdt_case_collect_record t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN mdt_case_batch t3
        ON t2.case_batch_id = t3.batch_id
        LEFT JOIN sys_entrusts t4
        ON t3.entrusts_id = t4.entrusts_id
        <include refid="whereSql"/>
    </select>
    <sql id="whereSql">
        <where>
            <if test="companyId != null">
                t2.company_id = #{companyId}
            </if>
            <if test="param != null">
                <if test="param.batchNo != null">
                    AND t3.batch_no = #{param.batchNo}
                </if>
                <if test="param.entrustsId != null">
                    AND t4.entrusts_id = #{param.entrustsId}
                </if>
                <if test="param.entrustCaseDateStart != null">
                    AND t3.entrust_case_date &gt;= STR_TO_DATE(#{param.entrustCaseDateStart},'%Y-%m-%d')
                </if>
                <if test="param.entrustCaseDateEnd != null">
                    AND t3.entrust_case_date &lt;= STR_TO_DATE(#{param.entrustCaseDateEnd},'%Y-%m-%d')
                </if>
                <if test="param.caseTypeId != null">
                    AND t3.case_type_id = #{param.caseTypeId}
                </if>
                <if test="param.mdtResult != null">
                    AND t1.mdt_result = #{param.mdtResult}
                </if>
                <if test="param.loanTotalStart != null">
                    AND t2.loan_total >= #{param.loanTotalStart}
                </if>
                <if test="param.loanTotalEnd != null">
                    AND t2.loan_total &lt;= #{param.loanTotalEnd}
                </if>
                <if test="param.optDateStart != null">
                    AND t1.create_time >= #{param.optDateStart}
                </if>
                <if test="param.optDateEnd != null">
                    AND t1.create_time &lt;= #{param.optDateEnd}
                </if>
                <if test="param.finOrgId != null">
                    AND t3.fin_org_id = #{param.finOrgId}
                </if>
                <if test="param.mediatorName != null">
                    AND t1.contactor_user = #{param.mediatorName}
                </if>
            </if>
        </where>
    </sql>
    <select id="list" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseRecordPageDTO">
        SELECT t1.record_id,
        t2.case_no,
        t2.respond_name,
        t2.respond_id_cert_no,
        t1.contactor_user,
        t1.contact_phone,
        t1.phone_type,
        t1.contact_rela,
        t1.mdt_result,
        t1.reducte_amount,
        t1.reducte_type,
        t1.next_do_time,
        t4.entrusts_name,
        t1.create_tm AS createTime,
        t1.account,
        t1.account_name,
        t1.call_record,
        t1.contact_result
        FROM mdt_case_collect_record t1
        LEFT JOIN mdt_case t2
        ON t1.case_id = t2.case_id
        LEFT JOIN mdt_case_batch t3
        ON t2.case_batch_id = t3.batch_id
        LEFT JOIN sys_entrusts t4
        ON t3.entrusts_id = t4.entrusts_id
        <if test="recordIds != null">
            <if test="!recordIds.isEmpty">
                WHERE record_id IN
                <foreach collection="recordIds" item="recordId" open="(" close=")" separator=",">
                    #{recordId}
                </foreach>
            </if>
        </if>
        <include refid="whereSql"/>
    </select>
    <select id="getCaseToday" resultType="com.sct.tiaojie.service.mdt.dto.MdtCasePageDTO">
        SELECT t1.case_id,
               t1.case_batch_id,
               t1.case_status,
               t1.case_no,
               t1.close_time,
               t2.batch_no,
               t1.respond_mobile,
               t1.respond_address,
               t1.case_nature_content,
               t1.claimant_name,
               t1.respond_name,
               t1.respond_id_cert_no,
               t1.loan_total,
               t1.overdue_principal,
               t1.overdue_interest,
               t1.penalty_interest,
               t1.arb_mdt_amount,
               t2.entrust_case_date AS createTime,
               t1.current_mediator_name,
               t1.mediation_result,
               t1.process_flag
        FROM mdt_case t1
        LEFT JOIN mdt_case_batch t2
        ON t1.case_batch_id = t2.batch_id
        LEFT JOIN mdt_case_collect_record t3
        ON t1.case_id = t3.case_id
        LEFT JOIN auth_employee t4
        ON t3.mediator_employ_id = t4.employee_id
        WHERE DATE_FORMAT(NOW(),'%Y-%m-%d') = DATE_FORMAT(t3.next_do_time,'%Y-%m-%d')
        AND t4.account_id = #{accountId}
        AND t1.company_id = #{companyId}
    </select>

    <select id="selectMaxTimeRecords" resultType="com.sct.tiaojie.repository.mdt.entity.MdtCaseCollectRecord">
        SELECT a.* FROM (SELECT a.*, row_number() OVER (PARTITION BY a.case_id ORDER BY a.mediate_time DESC,a.create_tm DESC) AS row_num FROM mdt_case_collect_record a) a WHERE a.row_num = 1
    </select>
    <select id="getCasePushToday" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseMessagePushDTO">
        SELECT
            t1.case_id,
            t1.case_no,
            t1.expiration_time,
            t2.next_do_time,
            t1.current_mediator_id,
            t1.company_id
        FROM
            mdt_case t1
                LEFT JOIN mdt_case_collect_record t2 ON t1.case_id = t2.case_id
        WHERE
                DATE_FORMAT( NOW(), '%Y-%m-%d' ) = DATE_FORMAT( t2.next_do_time, '%Y-%m-%d' )
        AND t1.current_mediator_id IS NOT NULL
    </select>
</mapper>