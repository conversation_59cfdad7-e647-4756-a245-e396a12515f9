<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtSmsTemplateMapper">
    <select id="pageList" resultType="com.sct.tiaojie.service.mdt.dto.MdtSmsTempPageDTO">
        SELECT
        t1.sms_template_id,
        t1.tx_template_id,
        t1.template_content,
        t1.template_params,
        t1.sms_sign,
        t1.scene,
        t1.channel,
        t1.dept_id_list,
        t1.role_name_list,
        t1.template_name,
        t1.template_status,
        t1.template_type,
        t1.applyto,
        t1.description,
        t1.update_time,
        t2.employee_name AS updaterName
        FROM mdt_sms_template t1
        left join auth_employee t2 on t1.updater_id = t2.account_id
        <where>
            <if test="param != null">
                <if test="param.templateName != null">
                    AND t1.template_name LIKE CONCAT('%', #{param.templateName}, '%')
                </if>
                <if test="param.txTemplateId != null">
                    AND t1.tx_template_id LIKE CONCAT('%', #{param.txTemplateId}, '%')
                </if>
                <if test="param.updaterName != null">
                    AND t2.employee_name LIKE CONCAT('%', #{param.updaterName}, '%')
                </if>
                <if test="param.templateStatus != null">
                    AND t1.template_status = #{param.templateStatus}
                </if>
                <if test="param.templateTypes != null and !param.templateTypes.isEmpty">
                    AND t1.template_type IN
                    <foreach collection="param.templateTypes" item="templateType" open="(" close=")" separator=",">
                        #{templateType}
                    </foreach>
                </if>
                <if test="param.deptIdList != null and !param.deptIdList.isEmpty">
                    <foreach collection="param.deptIdList" item="deptId" open="(" close=")" separator="OR">
                        JSON_CONTAINS(t1.dept_id_list,JSON_QUOTE(#{deptId}),'$')
                    </foreach>
                </if>
            </if>
        </where>
    </select>

    <sql id="logListConditions">
        <if test="param != null">
            <if test="param.caseNo != null">
                and b.case_no = #{param.caseNo}
            </if>
            <if test="param.phoneNumber != null">
                and c.phone_number like concat('%',#{param.phoneNumber},'%')
            </if>
            <if test="param.contactName != null">
                and a.contact_name = #{param.contactName}
            </if>
            <if test="param.companyId != null">
                and f.company_id = #{param.companyId}
            </if>
            <if test="param.sendMessage != null">
                and c.send_message = #{param.sendMessage}
            </if>
            <if test="param.sendTimeBegin != null and param.sendTimeBegin != ''">
                and f.send_time >= #{param.sendTimeBegin}
            </if>
            <if test="param.sendTimeEnd != null and param.sendTimeEnd != ''">
                and f.send_time &lt;= #{param.sendTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="selectLogList">
        SELECT
            a.sms_id,
            b.case_no,
            b.case_id,
            c.sms_send_id,
            a.contact_name,
            c.phone_number,
            f.is_batch AS send_type,
            c.content AS send_context,
            c.receive_status,
            c.send_status,
            c.send_message,
            f.send_time,
            e.company_id,
            f.sender_id,
            e.employee_name AS sender_name,
            CASE
                WHEN h.dept_id IS NOT NULL THEN
                    CONCAT( g.company_name, '-', i.dept_name ) ELSE g.company_name
                END AS company_name
        FROM
            mdt_sms a
                INNER JOIN mdt_case b ON b.case_id = a.case_id
                INNER JOIN mdt_sms_send_details c ON c.sms_send_id = a.sms_send_id
                LEFT JOIN mdt_case_litigant d ON d.case_id = b.case_id
                AND d.litigant_phone = c.phone_number
                INNER JOIN mdt_sms_send f ON f.sms_send_id = c.sms_send_id
                INNER JOIN auth_employee e ON e.account_id = f.sender_id
                INNER JOIN auth_company g ON g.company_id = e.company_id
                LEFT JOIN auth_employee_dept_rela h ON h.employee_id = e.employee_id
                LEFT JOIN auth_dept i ON i.dept_id = h.dept_id
    </sql>

    <select id="logPageList" resultType="com.sct.tiaojie.service.mdt.dto.MdtSmsLogDTO">
        <include refid="selectLogList"/>
        <where>
            <include refid="logListConditions"/>
        </where>
        order by a.create_time desc
    </select>

    <select id="logListByCaseId" resultType="com.sct.tiaojie.service.mdt.dto.MdtSmsLogDTO">
        SELECT
            a.sms_id,
            b.case_no,
            b.case_id,
            c.sms_send_id,
            a.contact_name,
            c.phone_number,
            f.is_batch AS send_type,
            c.content AS send_context,
            c.receive_status,
            c.send_status,
            c.send_message,
            f.send_time,
            e.company_id,
            f.sender_id,
            e.employee_name AS sender_name,
            CASE
                WHEN h.dept_id IS NOT NULL THEN
                    CONCAT( g.company_name, '-', i.dept_name ) ELSE g.company_name
                END AS company_name
        FROM
            mdt_sms a
                INNER JOIN mdt_case b ON b.case_id = a.case_id
                INNER JOIN mdt_sms_send_details c ON c.sms_send_id = a.sms_send_id
                LEFT JOIN mdt_case_litigant d ON d.case_id = b.case_id
                AND d.litigant_phone = c.phone_number
                INNER JOIN mdt_sms_send f ON f.sms_send_id = c.sms_send_id
                INNER JOIN auth_employee e ON e.account_id = f.sender_id
                INNER JOIN auth_company g ON g.company_id = e.company_id
                LEFT JOIN auth_employee_dept_rela h ON h.employee_id = e.employee_id
                LEFT JOIN auth_dept i ON i.dept_id = h.dept_id
        <where>
            b.case_id = #{caseId}
        </where>
        order by a.create_time desc
    </select>

    <select id="selectSmsListByBo" resultType="com.sct.tiaojie.service.mdt.dto.MdtSmsLogDTO">
        <include refid="selectLogList"/>
        <where>
            <include refid="logListConditions"/>
        </where>
        order by a.create_time desc
    </select>

</mapper>