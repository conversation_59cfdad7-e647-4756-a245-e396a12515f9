<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtCaseLitigantMapper">
    <select id="getByPhoneLike" resultType="com.sct.tiaojie.repository.mdt.entity.MdtCaseLitigant">
        select *
        from mdt_case_litigant
        where #{phone} like CONCAT('%',litigant_phone,'%')
    </select>

    <select id="getCaseStatisticsByCaseId" resultType="com.sct.tiaojie.service.mdt.dto.CaseSurgeWarningDTO">
        SELECT
        litigant.litigant_name AS litigantName,
        COUNT(litigant.case_id) AS caseAmount,
        SUM(case_info.case_subject_matter) AS caseSubjectMatterSum
        FROM
        mdt_case_litigant litigant
        LEFT JOIN
        mdt_case case_info ON litigant.case_id = case_info.case_id
        WHERE
        litigant.litigant_type = 1
        <if test="caseIds != null">
            AND litigant.case_id IN
            <foreach item="caseId" collection="caseIds" open="(" separator="," close=")">
                #{caseId}
            </foreach>
        </if>
        GROUP BY
        litigant.litigant_name
    </select>

    <select id="getNeedRepairContactListByIdList" resultType="com.sct.tiaojie.service.mdt.bo.NeedRepairContactBO">
        SELECT
        a.litigant_id,
        a.case_id,
        a.litigant_name,
        a.litigant_phone,
        a.create_time,
        a.creator_id,
        a.update_time,
        a.updater_id,
        a.litigant_type,
        a.identity_type,
        a.phone_status,
        a.id_type,
        a.id_no,
        a.sex,
        a.post,
        a.nation,
        a.birth,
        a.id_address,
        a.contact_address,
        a.is_private_enterprise,
        a.register_address,
        a.business_address,
        a.litigant_desc,
        a.legal_agent_name,
        c.batch_expire_date,
        c.batch_status,
        c.repair_id,
        c.repair_net_status,
        c.user_id,
        c.company_id,
        c.operation_time,
        b.record_repair_status,
        b.record_id,
        b.id_card_md5,
        b.call_no,
        b.match_repair_result,
        b.number_fail_message
        FROM
        mdt_case_litigant a
        LEFT JOIN mdt_contact_repair_record b ON b.case_id = a.case_id
        AND b.litigant_id = a.litigant_id
        LEFT JOIN mdt_contact_repair c ON c.repair_id = b.repair_id AND c.repair_net_status = ${mdtContactRepairBo.oldRepairNetStatus}
        <where>
            <if test="mdtContactRepairBo.caseIdList != null and mdtContactRepairBo.caseIdList.size() > 0">
                and a.case_id in
                <foreach collection="mdtContactRepairBo.caseIdList" item="caseId" open="(" close=")" separator=",">
                    #{caseId}
                </foreach>
            </if>
            <if test="mdtContactRepairBo.caseId != null and mdtContactRepairBo.caseId != ''">
                and a.case_id = #{mdtContactRepairBo.caseId}
            </if>
            <if test="mdtContactRepairBo.identityTypeList != null and mdtContactRepairBo.identityTypeList.size() > 0">
                and a.identity_type in
                <foreach collection="mdtContactRepairBo.identityTypeList" item="identityType" open="(" close=")" separator=",">
                    #{identityType}
                </foreach>
            </if>
            <if test="mdtContactRepairBo.litigantTypeList != null and mdtContactRepairBo.litigantTypeList.size() > 0">
                and a.litigant_type in
                <foreach collection="mdtContactRepairBo.litigantTypeList" item="litigantType" open="(" close=")" separator=",">
                    #{litigantType}
                </foreach>
            </if>
            <if test="mdtContactRepairBo.litigantId != null and mdtContactRepairBo.litigantId != ''">
                and a.litigant_id = #{mdtContactRepairBo.litigantId}
            </if>
            <if test="mdtContactRepairBo.litigantPhoneStatusesList != null and mdtContactRepairBo.litigantPhoneStatusesList.size() > 0">
                and a.phone_status in
                <foreach collection="mdtContactRepairBo.litigantPhoneStatusesList" item="litigantPhoneStatuses" open="(" close=")" separator=",">
                    #{litigantPhoneStatuses}
                </foreach>
            </if>
            <if test="mdtContactRepairBo.litigantIdList != null and mdtContactRepairBo.litigantIdList != ''">
                and a.litigant_id in
                <foreach collection="mdtContactRepairBo.litigantIdList" item="litigantId" open="(" close=")" separator=",">
                    #{litigantId}
                </foreach>
            </if>
            <if test="mdtContactRepairBo.repairNetStatus != null and mdtContactRepairBo.repairNetStatus != 1">
                and c.user_id is not null
            </if>
        </where>
    </select>

    <select id="getNeedRepairLitigant" resultType="com.sct.tiaojie.service.mdt.bo.NeedRepairContactBO">
        SELECT
        a.litigant_id,
        a.case_id,
        a.litigant_name,
        a.litigant_phone,
        a.create_time,
        a.creator_id,
        a.update_time,
        a.updater_id,
        a.litigant_type,
        a.identity_type,
        a.phone_status,
        a.id_type,
        a.id_no,
        a.sex,
        a.post,
        a.nation,
        a.birth,
        a.id_address,
        a.contact_address,
        a.is_private_enterprise,
        a.register_address,
        a.business_address,
        a.litigant_desc,
        a.legal_agent_name,
        c.batch_expire_date,
        c.batch_status,
        c.repair_id,
        c.repair_net_status,
        c.user_id,
        c.company_id,
        c.operation_time,
        b.record_repair_status,
        b.record_id,
        b.id_card_md5,
        b.call_no,
        b.match_repair_result,
        b.number_fail_message
        FROM
        mdt_case_litigant a
        LEFT JOIN mdt_contact_repair_record b ON b.case_id = a.case_id
        AND b.litigant_id = a.litigant_id
        LEFT JOIN mdt_contact_repair c ON c.repair_id = b.repair_id
        <where>
            <if test="mdtContactRepairBo.caseId != null and mdtContactRepairBo.caseId != ''">
                and a.case_id = #{mdtContactRepairBo.caseId}
            </if>
            <if test="mdtContactRepairBo.litigantId != null and mdtContactRepairBo.litigantId != ''">
                and a.litigant_id = #{mdtContactRepairBo.litigantId}
            </if>
            <if test="mdtContactRepairBo.repairNetStatus != null and mdtContactRepairBo.repairNetStatus != ''">
                and c.repair_net_status = ${mdtContactRepairBo.oldRepairNetStatus}
            </if>
        </where>
    </select>
</mapper>