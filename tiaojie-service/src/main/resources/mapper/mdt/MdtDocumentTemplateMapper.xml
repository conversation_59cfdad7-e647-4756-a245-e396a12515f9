<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtDocumentTemplateMapper">
    <select id="getKeyWord" resultType="java.lang.String">
        SELECT t1.keyword
        FROM mdt_document_template t1
        LEFT JOIN mdt_document t2
        ON t1.document_id = t2.document_id
        WHERE t1.intelligent_case_type_id = 'normal'
        AND t2.document_name = #{title}
        AND t1.company_id = #{companyId}
    </select>
</mapper>