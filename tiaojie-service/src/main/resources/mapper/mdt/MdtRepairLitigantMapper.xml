<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtRepairLitigantMapper">


    <resultMap id="repairLogNew" type="com.sct.tiaojie.service.mdt.dto.MdtRepairRecordDTO" autoMapping="true">
        <id property="repairLitigantId" column="repair_litigant_id"/>
        <collection property="mdtNetRepairRecordDTOList" columnPrefix="nrr_" ofType="com.sct.tiaojie.service.mdt.dto.MdtNetRepairRecordDTO"
                    javaType="java.util.ArrayList" autoMapping="true">
            <id property="repairRecordId" column="repair_record_Id"/>
        </collection>
    </resultMap>



    <select id="pageRepairLogNew" resultMap="repairLogNew">
        select
            t1.repair_litigant_id,
            t1.case_id,
            t1.litigant_id,
            t1.litigant_name,
            t1.repair_status,
            t1.repair_result,
            t1.repair_success_net,
            t1.repair_success_time,
            t1.account_id,
            t1.account_name,
            t1.company_id,
            t1.id_card,
            t1.company_name,
            t2.case_no,
            t3.operation_time,
            t4.repair_record_id as nrr_repair_record_id,
            t4.repair_litigant_id as nrr_repair_litigant_id,
            t4.case_id as nrr_case_id,
            t4.litigant_id as nrr_litigant_id,
            t4.task_no as nrr_task_no,
            t4.repair_net as nrr_repair_net,
            t4.repair_status as nrr_repair_status,
            t4.repair_result as nrr_repair_result,
            t4.repair_fail_code as nrr_repair_fail_code,
            t4.repair_fail_msg as nrr_repair_fail_msg,
            t4.phone_number as nrr_phone_number,
            t4.repair_complete_time as nrr_repair_complete_time,
            t4.expire_time as nrr_expire_time
        FROM mdt_repair_litigant t1
        left join mdt_case t2
        on t1.case_id = t2.case_id
        left join mdt_repair t3
        on t1.repair_id = t3.repair_id
        left join mdt_repair_record t4
        on t1.repair_litigant_id = t4.repair_litigant_id
        <where>
            <if test="param != null">
                <if test="param.caseNo != null and param.caseNo != ''">
                    and t2.case_no like concat('%',#{param.caseNo},'%')
                </if>
                <if test="param.litigantName != null and param.litigantName != ''">
                    and t1.litigant_name like concat('%', #{param.litigantName} ,'%')
                </if>
                <if test="param.company != null">
                    and t1.account_id = #{param.company}
                </if>
                <if test="param.matchRepairResult != null">
                    and t1.repair_result = #{param.matchRepairResult}
                </if>
                <if test="param.repairTimeStart != null and param.repairTimeStart != ''">
                    and t3.operation_time >= #{param.repairTimeStart}
                </if>
                <if test="param.repairTimeEnd != null and param.repairTimeEnd != ''">
                    and t3.operation_time &lt;= #{param.repairTimeEnd}
                </if>
            </if>
        </where>
    </select>

</mapper>