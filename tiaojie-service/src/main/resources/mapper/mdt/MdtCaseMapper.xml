<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtCaseMapper">
    <resultMap id="page" type="com.sct.tiaojie.service.mdt.dto.MdtPublicCasePageDTO">
        <result property="respondName" column="respond_name"/>
        <result property="respondIdCertNo" column="respond_id_cert_no"/>
        <collection property="caseDetails" ofType="com.sct.tiaojie.service.mdt.dto.MdtCasePageDTO">
            <result property="arbMdtAmount" column="arb_mdt_amount"/>
            <result property="batchNo" column="batch_no"/>
            <result property="caseBatchId" column="case_batch_id"/>
            <result property="caseId" column="case_id"/>
            <result property="caseNo" column="case_no"/>
            <result property="caseNatureContent" column="case_nature_content"/>
            <result property="caseStatus" column="case_status"/>
            <result property="claimantName" column="claimant_name"/>
            <result property="currentMediatorName" column="current_mediator_name"/>
            <result property="overduePrincipal" column="overdue_principal"/>
            <result property="loanTotal" column="loan_total"/>
            <result property="penaltyInterest" column="penalty_interest"/>
            <result property="respondAddress" column="respond_address"/>
            <result property="respondMobile" column="respond_mobile"/>
            <result property="overdueInterest" column="overdue_interest"/>
            <result property="createTime" column="create_time"/>
        </collection>
    </resultMap>

    <sql id="selectCaseCollectionRecordReviewLogList">
        SELECT
            concat(a.collection_id,jt.case_id) as record_id,
            a.collection_id,
            jt.case_id,
            jt.case_no,
            a.collection_type,
            a.collection_result,
            a.creator_name,
            a.company_name,
            a.entrusts_name,
            a.tmpl_name,
            a.file_path,
            a.accept_time
        FROM
            mdt_case_collection_record a
                JOIN JSON_TABLE (
                    a.case_info,
                    '$[*]' COLUMNS ( case_id VARCHAR ( 200 ) PATH '$.caseId', case_no VARCHAR ( 200 ) PATH '$.caseNo' )) jt
                INNER JOIN mdt_case b ON b.case_id = jt.case_id
    </sql>

    <sql id="condition">
        <if test="param != null">
            <if test="param.caseNo != null">
                and jt.case_no = #{param.caseNo}
            </if>
            <if test="param.collectionType != null">
                and a.collection_type = #{param.collectionType}
            </if>
            <if test="param.creatorName != null">
                and a.creator_name like concat('%',#{param.creatorName},'%')
            </if>
            <if test="param.entrustsName != null">
                and a.entrusts_name like concat('%',#{param.entrustsName},'%')
            </if>
            <if test="param.collectionResult != null">
                and a.collection_result = #{param.collectionResult}
            </if>
            <if test="param.acceptTime != null">
                and date(a.accept_time) = #{param.acceptTime}
            </if>
            <if test="param.acceptTimeStart != null">
                and a.accept_time >= #{param.acceptTimeStart}
            </if>
            <if test="param.acceptTimeEnd != null">
                and a.accept_time &lt;= #{param.acceptTimeEnd}
            </if>
            <if test="param.entrustsId != null">
                and b.entrusts_id = #{param.entrustsId}
            </if>
        </if>
    </sql>

    <select id="getCaseCollectionRecordReviewLogPageList"
            resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseCollectionRecordReceiveLogDto">
        <include refid="selectCaseCollectionRecordReviewLogList"/>
        <where>
            <include refid="condition"/>
        </where>
        order by a.accept_time desc
    </select>

    <sql id="selectColumnPage">
        select t1.case_id,
        t1.mdt_case_status,
        t1.case_subject_matter,
        t1.case_no,
        t2.entrusts_name,
        t1.entrusts_dept_id,
        t1.mediate_result,
        t1.mediate_status,
        t1.case_nature_content,
        t1.case_apply_time,
        t1.close_reason,
        t1.success_reason,
        t1.case_manage_close_time,
        t1.suspend_reason,
        t1.case_manage_close_reason,
        t4.org_name,
        t5.dept_name,
        t1.current_mediator_name,
        t1.create_time,
        (SELECT business_type_name FROM sys_business_type where business_type_id = t1.business_type) as businessTypeName,
        CASE WHEN t1.mediate_begin_time > NOW() THEN DATEDIFF(t1.expiration_time , t1.mediate_begin_time)
             WHEN t1.mdt_case_status = 40 THEN DATEDIFF(t1.expiration_time , t10.end_time)
             ELSE DATEDIFF(t1.expiration_time , NOW())
             END as remainingDays,
        t1.close_time,
        t1.mediate_begin_time,
        t6.employee_name as creatorName,
        t1.expiration_time,
--         GROUP_CONCAT(DISTINCT t8.litigant_name SEPARATOR ', ') AS defend_ant_litigant_names,
--         GROUP_CONCAT(DISTINCT t9.litigant_name SEPARATOR ', ') AS plain_tiff_litigant_names,
--      t8.defend_ant_litigant_names,
--      t9.plain_tiff_litigant_names,
        t1.mediate_close_time as mediateCloseTime,
        t1.mediate_done_time,
        t1.business_type,
        t1.case_mediate_type
    </sql>

    <sql id="joinColumnPage">
        FROM mdt_case t1
        left join sys_entrusts t2
        on t1.entrusts_id = t2.entrusts_id
        left join mdt_case_litigant t3
        on t3.case_id = t1.case_id
        left join sys_mdt_org t4
        on t1.org_id = t4.org_id
        left join auth_dept t5
        on t1.dept_id = t5.dept_id
        left join auth_employee t6 on t6.account_id = t1.creator_id
--         LEFT JOIN mdt_case_litigant t8 on t8.case_id = t1.case_id  and t8.identity_type in (1,3)
        -- left join (SELECT a.case_id,GROUP_CONCAT(litigant_name SEPARATOR ',') AS defend_ant_litigant_names FROM mdt_case_litigant a
        -- WHERE a.identity_type = 1 OR a.identity_type = 3 GROUP BY a.case_id) t8 on t8.case_id = t1.case_id
--         LEFT JOIN mdt_case_litigant t9 on t9.case_id = t1.case_id  and t9.identity_type in (2,4)
        -- left join (SELECT a.case_id,GROUP_CONCAT(litigant_name SEPARATOR ',') AS plain_tiff_litigant_names FROM mdt_case_litigant a
        -- WHERE a.identity_type = 2 OR a.identity_type = 4 GROUP BY a.case_id) t9 on t9.case_id = t1.case_id
        left join (SELECT a.case_id,MAX(a.end_time) AS end_time FROM mdt_case_approval a GROUP BY a.case_id) t10 on t10.case_id = t1.case_id
    </sql>

    <sql id="conditionPage">
        <if test="param != null">
            <if test="param.caseNo != null">
                and t1.case_no like concat('%', #{param.caseNo}, '%')
            </if>
            <if test="param.caseNoList != null and !param.caseNoList.isEmpty">
                AND t1.case_no IN
                <foreach collection="param.caseNoList" item="caseNoItem" open="(" close=")" separator=",">
                    #{caseNoItem}
                </foreach>
            </if>
            <if test="param.mdtCaseStatuses != null and !param.mdtCaseStatuses.isEmpty">
                AND t1.mdt_case_status IN
                <foreach collection="param.mdtCaseStatuses" item="mdtCaseStatus" open="(" close=")" separator=",">
                    #{mdtCaseStatus}
                </foreach>
            </if>
            <if test="param.mediateStatuses != null and !param.mediateStatuses.isEmpty">
                AND t1.mediate_status IN
                <foreach collection="param.mediateStatuses" item="mediateStatus" open="(" close=")" separator=",">
                    #{mediateStatus}
                </foreach>
            </if>
            <if test="param.mediateResult != null">
                and t1.mediate_result = #{param.mediateResult}
            </if>
            <if test="param.entrustsIds != null and !param.entrustsIds.isEmpty">
                AND t1.entrusts_id IN
                <foreach collection="param.entrustsIds" item="entrustsId" open="(" close=")" separator=",">
                    #{entrustsId}
                </foreach>
            </if>

            <if test="(param.entrustsIds != null and !param.entrustsIds.isEmpty) or (param.entrustsDeptIds != null and !param.entrustsDeptIds.isEmpty) or (param.entrustsJudgeIds != null and !param.entrustsJudgeIds.isEmpty)">
                AND (
            </if>
            <if test="param.entrustsIds != null and !param.entrustsIds.isEmpty">
                t1.entrusts_id IN
                <foreach collection="param.entrustsIds" item="entrustsId" open="(" close=")" separator=",">
                    #{entrustsId}
                </foreach>
                <if test="param.entrustsDeptIds != null and !param.entrustsDeptIds.isEmpty">
                    OR
                    (
                    t1.entrusts_dept_id IN
                    <foreach collection="param.entrustsDeptIds" item="entrustsDeptId" open="(" close=")" separator=",">
                        #{entrustsDeptId}
                    </foreach>
                    <if test="param.entrustsJudgeIds != null and !param.entrustsJudgeIds.isEmpty">
                        OR t1.creator_id IN
                        <foreach collection="param.entrustsJudgeIds" item="entrustsJudgeId" open="(" close=")" separator=",">
                            #{entrustsJudgeId}
                        </foreach>
                    </if>
                    )
                </if>
                <if test="param.entrustsJudgeIds != null and !param.entrustsJudgeIds.isEmpty">
                    OR
                    (
                    t1.creator_id IN
                    <foreach collection="param.entrustsJudgeIds" item="entrustsJudgeId" open="(" close=")" separator=",">
                        #{entrustsJudgeId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="param.entrustsIds == null or param.entrustsIds.isEmpty">
                <if test="param.entrustsDeptIds != null and !param.entrustsDeptIds.isEmpty">
                    t1.entrusts_dept_id IN
                    <foreach collection="param.entrustsDeptIds" item="entrustsDeptId" open="(" close=")" separator=",">
                        #{entrustsDeptId}
                    </foreach>
                    <if test="param.entrustsJudgeIds != null and !param.entrustsJudgeIds.isEmpty">
                        OR t1.creator_id IN
                        <foreach collection="param.entrustsJudgeIds" item="entrustsJudgeId" open="(" close=")" separator=",">
                            #{entrustsJudgeId}
                        </foreach>
                    </if>
                </if>
                <if test="param.entrustsJudgeIds != null and !param.entrustsJudgeIds.isEmpty and (param.entrustsDeptIds == null or param.entrustsDeptIds.isEmpty)">
                    t1.creator_id IN
                    <foreach collection="param.entrustsJudgeIds" item="entrustsJudgeId" open="(" close=")" separator=",">
                        #{entrustsJudgeId}
                    </foreach>
                </if>
            </if>
            <if test="param.entrustsIds != null and !param.entrustsIds.isEmpty or (param.entrustsDeptIds != null and !param.entrustsDeptIds.isEmpty) or (param.entrustsJudgeIds != null and !param.entrustsJudgeIds.isEmpty)">
                )
            </if>


            <if test="param.entrustsDeptId != null and param.entrustsDeptId != ''">
                AND t1.entrusts_dept_id = #{param.entrustsDeptId}
            </if>
            <if test="param.creatorId != null and param.creatorId != ''">
                AND t1.creator_id = #{param.creatorId}
            </if>
            <if test="param.createStartTime != null">
                and t1.create_time >= #{param.createStartTime}
            </if>
            <if test="param.createEndTime != null">
                and t1.create_time &lt;= #{param.createEndTime}
            </if>
            <if test="param.orgIds != null and !param.orgIds.isEmpty">
                AND t1.org_id IN
                <foreach collection="param.orgIds" item="orgId" open="(" close=")" separator=",">
                    #{orgId}
                </foreach>
            </if>
            <if test="param.deptIds != null and !param.deptIds.isEmpty">
                AND t1.dept_id IN
                <foreach collection="param.deptIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="param.currentMediatorIds != null and !param.currentMediatorIds.isEmpty">
                AND t1.current_mediator_id IN
                <foreach collection="param.currentMediatorIds" item="currentMediatorId" open="(" close=")"
                         separator=",">
                    #{currentMediatorId}
                </foreach>
            </if>
            <if test="param.closeStartTime != null">
                and t1.close_time >= #{param.closeStartTime}
            </if>
            <if test="param.closeEndTime != null">
                and t1.close_time &lt;= #{param.closeEndTime}
            </if>

            <if test="param.caseManageCloseStartTime != null">
                and t1.case_manage_close_time >= #{param.caseManageCloseStartTime}
            </if>
            <if test="param.caseManageCloseEndTime != null">
                and t1.case_manage_close_time &lt;= #{param.caseManageCloseEndTime}
            </if>


            <if test="param.businessTypes != null and !param.businessTypes.isEmpty">
                AND t1.business_type IN
                <foreach collection="param.businessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="param.caseMediateTypes != null and !param.caseMediateTypes.isEmpty">
                AND t1.case_mediate_type IN
                <foreach collection="param.caseMediateTypes" item="caseMediateType" open="(" close=")" separator=",">
                    #{caseMediateType}
                </foreach>
            </if>
            <if test="param.closeReasons != null and !param.closeReasons.isEmpty">
                AND t1.close_reason IN
                <foreach collection="param.closeReasons" item="closeReason" open="(" close=")" separator=",">
                    #{closeReason}
                </foreach>
            </if>
            <if test="param.successReasons != null and !param.successReasons.isEmpty">
                AND t1.success_reason IN
                <foreach collection="param.successReasons" item="successReason" open="(" close=")" separator=",">
                    #{successReason}
                </foreach>
            </if>
            <if test="param.suspendReasons != null and !param.suspendReasons.isEmpty">
                AND t1.suspend_reason IN
                <foreach collection="param.suspendReasons" item="suspendReason" open="(" close=")" separator=",">
                    #{suspendReason}
                </foreach>
            </if>

            <if test="param.caseManageCloseReasons != null and !param.caseManageCloseReasons.isEmpty">
                AND t1.case_manage_close_reason IN
                <foreach collection="param.caseManageCloseReasons" item="caseManageCloseReason" open="(" close=")" separator=",">
                    #{caseManageCloseReason}
                </foreach>
            </if>
            <if test="param.litigantName != null">
                and t3.litigant_name like concat('%', #{param.litigantName}, '%') or t3.legal_agent_name like concat('%', #{param.litigantName}, '%')
            </if>
            <if test="param.litigantPhone != null">
                and t3.litigant_phone = #{param.litigantPhone}
            </if>
            <if test="param.litigantTypes != null and !param.litigantTypes.isEmpty">
                AND t3.litigant_type IN
                <foreach collection="param.litigantTypes" item="litigantType" open="(" close=")" separator=",">
                    #{litigantType}
                </foreach>
            </if>
            <if test="param.litigantIdentityTypes != null and !param.litigantIdentityTypes.isEmpty">
                AND t3.identity_type IN
                <foreach collection="param.litigantIdentityTypes" item="litigantIdentityType" open="(" close=")"
                         separator=",">
                    #{litigantIdentityType}
                </foreach>
            </if>
            <if test="param.litigantPhoneStatuses != null and !param.litigantPhoneStatuses.isEmpty">
                AND t3.phone_status IN
                <foreach collection="param.litigantPhoneStatuses" item="litigantPhoneStatus" open="(" close=")"
                         separator=",">
                    #{litigantPhoneStatus}
                </foreach>
            </if>
            <if test="param.expirationTimeStart != null or param.expirationTimeEnd != null">
                AND (CASE WHEN t1.mediate_begin_time > NOW() THEN DATEDIFF(t1.expiration_time , t1.mediate_begin_time)
                WHEN t1.mdt_case_status = 40 THEN DATEDIFF(t1.expiration_time , t10.end_time)
                ELSE DATEDIFF(t1.expiration_time , NOW())
                END ) >= #{param.expirationTimeStart}
                AND (CASE WHEN t1.mediate_begin_time > NOW() THEN DATEDIFF(t1.expiration_time , t1.mediate_begin_time)
                WHEN t1.mdt_case_status = 40 THEN DATEDIFF(t1.expiration_time , t10.end_time)
                ELSE DATEDIFF(t1.expiration_time , NOW())
                END ) &lt;= #{param.expirationTimeEnd}
            </if>
            <if test="param.caseApplyBegin != null">
                AND t1.case_apply_time >= #{param.caseApplyBegin}
            </if>
            <if test="param.caseApplyEnd != null">
                AND t1.case_apply_time &lt;= #{param.caseApplyEnd}
            </if>
            <if test="param.caseStartBegin != null">
                AND t1.mediate_begin_time >= #{param.caseStartBegin}
            </if>
            <if test="param.caseStartEnd != null">
                AND t1.mediate_begin_time &lt;= #{param.caseStartEnd}
            </if>
            <if test="param.caseExpirationBegin != null">
                AND t1.expiration_time >= #{param.caseExpirationBegin}
            </if>
            <if test="param.caseExpirationEnd != null">
                AND t1.expiration_time &lt;= #{param.caseExpirationEnd}
            </if>
            <if test="param.mediateCloseTimeStart != null">
                AND t1.mediate_close_time >= #{param.mediateCloseTimeStart}
            </if>
            <if test="param.mediateCloseTimeEnd != null">
                AND t1.mediate_close_time &lt;= #{param.mediateCloseTimeEnd}
            </if>
            <if test="param.caseNatureIds != null and !param.caseNatureIds.isEmpty">
                AND t1.case_nature_content IN (
                WITH RECURSIVE dict_tree AS(
                SELECT dict_data_id,parent_id,dict_tag FROM sys_dict_data WHERE dict_data_id IN
                <foreach collection="param.caseNatureIds" item="caseNatureId" open="(" close=")"
                         separator=",">
                    #{caseNatureId}
                </foreach>
                UNION ALL
                SELECT d.dict_data_id,d.parent_id,d.dict_tag FROM sys_dict_data d INNER JOIN dict_tree dt ON dt.dict_data_id = d.parent_id
                )
                SELECT dict_tag FROM dict_tree
                )
            </if>


            <if test="param.taskInstance != null">

                <if test="param.taskInstance.isExist !=null and param.taskInstance.isExist == 0">
                    AND NOT
                </if>
                <if test="param.taskInstance.isExist ==null or param.taskInstance.isExist == 1">
                    AND
                </if>
                   EXISTS (
                SELECT 1
                FROM task_instance inner_task_instance
                <where>
                    inner_task_instance.case_id = t1.case_id
                    <!-- 任务发起人列表 -->
                    <if test="param.taskInstance.assignAccountIdList != null and !param.taskInstance.assignAccountIdList.isEmpty">
                        AND inner_task_instance.assign_account_id IN
                        <foreach collection="param.taskInstance.assignAccountIdList" item="assignAccountId" open="(" close=")" separator=",">
                            #{assignAccountId}
                        </foreach>
                    </if>

                    <!-- 任务类型 -->
                    <if test="param.taskInstance.taskTypes != null and !param.taskInstance.taskTypes.isEmpty">
                        AND inner_task_instance.task_type IN
                        <foreach collection="param.taskInstance.taskTypes" item="taskType" open="(" close=")" separator=",">
                            #{taskType}
                        </foreach>
                    </if>

                    <!-- 分派登记时间-开始 -->
                    <if test="param.taskInstance.assignTimeStart != null">
                        AND inner_task_instance.assign_time >= #{param.taskInstance.assignTimeStart}
                    </if>

                    <!-- 分派登记时间-结束 -->
                    <if test="param.taskInstance.assignTimeEnd != null">
                        AND inner_task_instance.assign_time &lt;= #{param.taskInstance.assignTimeEnd}
                    </if>

                    <!-- 截止时间-开始 -->
                    <if test="param.taskInstance.deadlineStart != null">
                        AND inner_task_instance.deadline >= #{param.taskInstance.deadlineStart}
                    </if>

                    <!-- 截止时间-结束 -->
                    <if test="param.taskInstance.deadlineEnd != null">
                        AND inner_task_instance.deadline &lt;= #{param.taskInstance.deadlineEnd}
                    </if>

                    <!-- 任务负责人列表 -->
                    <if test="param.taskInstance.managerIdList != null and !param.taskInstance.managerIdList.isEmpty">
                        AND JSON_OVERLAPS(inner_task_instance.manager_id_list, #{param.taskInstance.managerIdList, typeHandler=com.sct.tiaojie.config.mybatis.JsonTypeHandler})
                    </if>

                    <!-- 任务状态 -->
                    <if test="param.taskInstance.taskStatuses != null and !param.taskInstance.taskStatuses.isEmpty">
                        AND inner_task_instance.task_status IN
                        <foreach collection="param.taskInstance.taskStatuses" item="taskStatus" open="(" close=")" separator=",">
                            #{taskStatus}
                        </foreach>
                    </if>

                    <!-- 任务完成人列表 -->
                    <if test="param.taskInstance.finishAccountIdList != null and !param.taskInstance.finishAccountIdList.isEmpty">
                        AND inner_task_instance.finish_account_id IN
                        <foreach collection="param.taskInstance.finishAccountIdList" item="finishAccountId" open="(" close=")" separator=",">
                            #{finishAccountId}
                        </foreach>
                    </if>

                    <!-- 完成时间-开始 -->
                    <if test="param.taskInstance.finishTimeStart != null">
                        AND inner_task_instance.finish_time >= #{param.taskInstance.finishTimeStart}
                    </if>

                    <!-- 完成时间-结束 -->
                    <if test="param.taskInstance.finishTimeEnd != null">
                        AND inner_task_instance.finish_time &lt;= #{param.taskInstance.finishTimeEnd}
                    </if>

                    <!-- 备注 -->
                    <if test="param.taskInstance.remark != null and param.taskInstance.remark != ''">
                        AND inner_task_instance.remark LIKE CONCAT('%', #{param.taskInstance.remark}, '%')
                    </if>
                </where>)

            </if>



            <if test="param.tmplCaseId != null">
                AND t1.tmpl_case_id  = #{param.tmplCaseId}
            </if>
            <if test="param.customModules != null and !param.customModules.isEmpty">
                <foreach collection="param.customModules" item="customModule">
                    <if test="customModule.fields != null and !customModule.fields.isEmpty">
                        <foreach collection="customModule.fields" item="field">
                            AND (EXISTS (SELECT 1 FROM mdt_case_custom_data t11 WHERE t11.case_id = t1.case_id and t11.tmpl_module_id = #{field.tmplModuleId}
                                <choose>
                                    <when test="field.componentRelation != null and field.componentRelation == 'ASelect'">
                                        <if test="field.isAbsent">
                                            and IFNULL(JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))),'') = ''
                                        </if>
                                        <if test="!field.isAbsent and (field.fieldValue != null and field.fieldValue != '')">
                                            and IFNULL(JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))),'') = #{field.fieldValue}
                                        </if>
                                    </when>
                                    <when test="field.componentRelation != null and field.componentRelation == 'ASelectMultiple'">
                                        <if test="field.isAbsent">
                                            and IFNULL(JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))),'') = ''
                                        </if>
                                        <if test="!field.isAbsent and (field.fieldValueList != null and !field.fieldValueList.isEmpty)">
                                            <foreach collection="field.fieldValueList" item="fieldValueListItem">
                                                and FIND_IN_SET(#{fieldValueListItem},IFNULL(JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))),''))
                                            </foreach>
                                            <bind name="valueListSize" value="field.fieldValueList.size()"/>
                                            AND LENGTH(IFNULL(JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))),''))-
                                            LENGTH(REPLACE(IFNULL(JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))),''),',',''))+1 = #{valueListSize}
                                        </if>
                                    </when>

                                    <when test="field.componentRelation != null and (field.componentRelation == 'ADatePicker' or  field.componentRelation == 'ADateTimePicker') and (field.fieldValueList != null and !field.fieldValueList.isEmpty and field.fieldValueList.size()==2)">
                                        and JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))) between  #{field.fieldValueList[0]} and DATE_FORMAT(#{field.fieldValueList[1]},'%Y-%m-%d 23:59:59')
                                    </when>


                                    <when test="field.componentRelation != null and field.componentRelation == 'AInputNumber'">
                                        <if test="field.fieldValue != null">
                                            and JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"')))  >= #{field.fieldValue}
                                        </if>
                                        <if test="field.fieldValueSecond != null">
                                            and JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"')))  &lt;= #{field.fieldValueSecond}
                                        </if>
                                    </when>

                                    <otherwise>
                                        and JSON_UNQUOTE(JSON_EXTRACT(t11.field_value, CONCAT('$."',#{field.fieldTitle},'"'))) like concat('%', #{field.fieldValue}, '%')
                                    </otherwise>
                                </choose>
                            LIMIT 1
                            )
                            <if test="field.isAbsent">
                                OR NOT EXISTS (
                                SELECT
                                1
                                FROM
                                mdt_case_custom_data t11
                                WHERE
                                t11.case_id = t1.case_id
                                AND t11.tmpl_module_id = #{field.tmplModuleId}
                                LIMIT 1
                                )
                            </if>
                            )
                        </foreach>
                    </if>
                </foreach>
            </if>
        </if>
    </sql>

    <select id="newPageList" resultType="com.sct.tiaojie.service.mdt.dto.MediateCasePageDTO">
        <include refid="selectColumnPage"/>
        <include refid="joinColumnPage"/>
        <where>
            <include refid="conditionPage"/>
        </where>
        group by t1.case_id , t10.end_time
        order by
        <if test="orderParams != null and !orderParams.isEmpty">
            <foreach collection="orderParams" item="orderParam">
                <if test="orderParam.field == 'expirationTime'">
                    t1.expiration_time
                </if>
                <choose>
                    <when test="orderParam.orderKeyWord == 'ASC'">ASC</when>
                    <when test="orderParam.orderKeyWord == 'DESC'">DESC</when>
                    <otherwise>ASC</otherwise>
                </choose>
                ,
            </foreach>
        </if>
        <if test="param.orderType == 'desc'">
            remainingDays desc,
        </if>
        <if test="param.orderType == 'asc'">
            remainingDays asc,
        </if>
         t1.create_time desc,t1.case_id desc
    </select>

    <sql id="whereSql">
        <where>
            t1.delete_flag = 0
            <if test="companyId != null">
                and t1.company_id = #{companyId}
            </if>
            <if test="params != null">
                <if test="params.batchNo != null">
                    AND t2.batch_no = #{params.batchNo}
                </if>
                <if test="params.entrustsId != null">
                    AND t2.entrusts_id = #{params.entrustsId}
                </if>
                <if test="params.caseTypeId != null">
                    AND t1.case_type_id = #{params.caseTypeId}
                </if>
                <if test="params.currentMediatorId != null">
                    AND t1.current_mediator_id = #{params.currentMediatorId}
                </if>
                <if test="params.distributeStatus != null">
                    <choose>
                        <when test="params.distributeStatus == 1">
                            AND t1.current_mediator_id IS NOT NULL
                        </when>
                        <when test="params.distributeStatus == 0">
                            AND t1.current_mediator_id IS NULL
                        </when>
                    </choose>
                </if>
                <if test="params.createTimeStart != null">
                    AND t2.entrust_case_date >= #{params.createTimeStart}
                </if>
                <if test="params.createTimeEnd != null">
                    AND t2.entrust_case_date &lt;= #{params.createTimeEnd}
                </if>
                <if test="params.loanTotalStart != null">
                    AND t1.loan_total >= #{params.loanTotalStart}
                </if>
                <if test="params.loanTotalEnd != null">
                    AND t1.loan_total &lt;= #{params.loanTotalEnd}
                </if>

                <if test="params.respondMobile != null">
                    AND t1.respond_mobile = #{params.respondMobile}
                </if>
                <if test="params.respondIdCertNo != null">
                    AND t1.respond_id_cert_no = #{params.respondIdCertNo}
                </if>
                <if test="params.caseNo != null">
                    AND t1.case_no LIKE CONCAT('%', #{params.caseNo}, '%')
                </if>
                <if test="params.caseStatus != null">
                    AND t1.case_status = #{params.caseStatus}
                </if>
                <if test="params.respondName != null">
                    AND t1.respond_name LIKE CONCAT('%', #{params.respondName}, '%')
                </if>
                <if test="params.mediationResult != null">
                    <if test="params.mediationResult.equals('false')">
                        AND t1.mediation_result IS NULL
                    </if>
                    <if test="!params.mediationResult.equals('false')">
                        AND t1.mediation_result = #{params.mediationResult}
                    </if>
                </if>
                <if test="params.processFlag != null">
                    <choose>
                        <when test="params.processFlag == 1">
                            AND t1.process_flag = 1
                        </when>
                        <when test="params.processFlag == 0">
                            AND (t1.process_flag = 0 OR t1.process_flag IS NULL)
                        </when>
                        <otherwise/>
                    </choose>
                </if>
                <if test="params.fileFlag != null">
                    <if test="params.fileType != null">
                        <if test="params.fileFlag">
                            AND t1.${params.fileType} IS NOT NULL
                            AND t1.${params.fileType} != ''
                        </if>
                        <if test="!params.fileFlag">
                            AND (t1.${params.fileType} IS NULL
                            OR t1.${params.fileType} = '')
                        </if>
                    </if>
                </if>
                <if test="params.signStatus != null and params.fileType != null">
                    <choose>
                        <when test="params.signStatus == 1">
                            AND t3.file_path IS NULL
                        </when>
                        <when test="params.signStatus == 2">
                            AND t3.result = '已盖章'
                        </when>
                        <when test="params.signStatus == 3">
                            AND t3.result = '盖章中'
                        </when>
                        <when test="params.signStatus == 4">
                            AND t3.result IS NOT NULL
                            AND t3.result != '已盖章'
                            AND t3.result != '盖章中'
                        </when>
                        <otherwise/>
                    </choose>
                </if>
                <if test="params.signFlag != null">
                    <if test="params.signFlag">
                        AND t1.mediation_notice IN (SELECT file_path FROM mdt_file_sign_info WHERE status = '签名完成')
                    </if>
                    <if test="!params.signFlag">
                        AND (t1.mediation_notice IS NULL OR t1.mediation_notice NOT IN (SELECT file_path FROM
                        mdt_file_sign_info WHERE status = '签名完成'))
                    </if>
                </if>
            </if>
        </where>
    </sql>

    <resultMap id="frontCase" type="com.sct.tiaojie.service.mdt.dto.MdtCaseFrontDTO">
        <result property="caseNo" column="case_no"/>
        <result property="caseId" column="case_id"/>
        <result property="mediatorName" column="current_mediator_name"/>
        <result property="claimantName" column="claimant_name"/>
        <result property="respondName" column="respond_name"/>
        <result property="arbMdtAmount" column="arb_mdt_amount"/>
        <result property="mortgageCarRemark" column="mortgage_car_remark"/>
        <result property="mediatorPhone" column="account_mobile"/>
        <result property="mediationAgreement" column="mediation_agreement"/>
        <result property="mediationNotice" column="mediation_notice"/>
        <result property="noticeNeedSignFlag" column="notice_need_sign_flag"/>
        <result property="noticeLookFlag" column="notice_look_flag"/>
        <result property="mediationNotification" column="mediation_notification"/>
        <result property="mediationAgreement" column="mediation_agreement"/>
        <result property="agreementFlag" column="agreement_flag"/>
        <result property="noticeFlag" column="notice_flag"/>
        <result property="statusMsg" column="mediating_case_status"
                typeHandler="com.sct.tiaojie.service.mdt.handler.CaseStatusTypeHandler"/>
        <result property="mediationAgreement" column="mediation_agreement"/>
        <result property="details" column="details"/>
    </resultMap>
    <select id="frontList" resultMap="frontCase">
        SELECT t1.case_id,
        t1.current_mediator_name,
        t1.claimant_name,
        t1.respond_name,
        t1.arb_mdt_amount,
        t1.mortgage_car_remark,
        t1.case_no,
        t2.account_mobile,
        IF(t1.mediation_agreement NOT LIKE '%docx', t1.mediation_agreement, NULL) AS mediationAgreement,
        t1.mediation_notice,
        t1.notice_need_sign_flag,
        t1.notice_look_flag,
        t1.mediating_case_status,
        t1.mediation_notification,
        t1.mediation_agreement,
        t1.details
        FROM mdt_case t1
        LEFT JOIN auth_account t2
        ON t1.current_mediator_id = t2.account_id
        WHERE t1.respond_name = #{loginName}
        AND t1.case_status != 25
        <if test="mobile != null">
            AND t1.respond_mobile = #{mobile}
        </if>
        <if test="idCardNo != null">
            AND t1.respond_id_cert_no = #{idCardNo}
        </if>
    </select>

    <select id="getOneFront" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseFrontDTO">
        SELECT t1.case_id,
               t1.current_mediator_name AS mediatorName,
               t1.claimant_name,
               t1.respond_name,
               t1.arb_mdt_amount,
               t1.mortgage_car_remark,
               t1.case_no,
               t2.account_mobile        AS mediatorPhone,
               t1.mediation_agreement,
               t1.mediation_notice,
               t1.notice_need_sign_flag,
               t1.mediation_notification,
               t1.notice_look_flag
        FROM mdt_case t1
                 LEFT JOIN auth_account t2
                           ON t1.current_mediator_id = t2.account_id
        WHERE t1.case_id = #{caseId}
    </select>

    <select id="getSignParam" resultType="com.sct.tiaojie.service.mdt.bo.MdtBatchSignParam">
        SELECT t1.respond_id_cert_no,
               t1.respond_name,
               t1.case_id,
               t1.arb_mdt_amount,
               t1.respond_mobile,
               t3.entrusts_name,
               t4.fin_org_name,
               t1.case_no,
               t2.batch_no     AS caseBatchNo,
               t1.current_mediator_name,
               t1.case_status,
               t1.overdue_principal,
               t1.overdue_interest,
               t1.penalty_interest,
               t1.case_type_id AS case_type,
               t2.entrust_case_date,
               t1.loan_total
        FROM mdt_case t1
                 LEFT JOIN mdt_case_batch t2
                           ON t1.case_batch_id = t2.batch_id
                 LEFT JOIN sys_entrusts t3
                           ON t2.entrusts_id = t3.entrusts_id
                 LEFT JOIN sys_fin_org t4
                           ON t2.fin_org_id = t4.fin_org_id
        WHERE t1.case_id = #{caseId}
    </select>
    <select id="getCaseIds" resultType="java.lang.Long">
        SELECT t1.case_id
        FROM mdt_case t1
        LEFT JOIN mdt_case_batch t2
        ON t1.case_batch_id = t2.batch_id
        LEFT JOIN sys_entrusts t3
        ON t1.current_mediator_name = t3.entrusts_name
        <include refid="whereSql"/>
    </select>

    <select id="selectFilePath" resultType="java.util.Map">
        SELECT t1.mediation_notice as filePath,
        t1.respond_name as respondName
        FROM mdt_case t1
        LEFT JOIN mdt_case_batch t2
        ON t1.case_batch_id = t2.batch_id
        <if test="caseIds != null and !caseIds.isEmpty">
            <where>
                AND t1.case_id IN
                <foreach collection="caseIds" item="caseId" open="(" close=")" separator=",">
                    #{caseId}
                </foreach>
                AND t1.mediation_notice IN (SELECT file_path FROM mdt_file_sign_info WHERE status = '签名完成')
            </where>
        </if>
        <include refid="whereSql"/>
    </select>

    <select id="getSysTempCase" resultType="java.util.Map">
        SELECT t1.*,
               (select GROUP_CONCAT(ml1.litigant_name SEPARATOR ',' ) from mdt_case_litigant ml1 where ml1.case_id = t1.case_id and (ml1.identity_type = '申请人' or ml1.identity_type = '1')) as claimant_name,
               (select GROUP_CONCAT(ml2.litigant_name SEPARATOR ',' ) from mdt_case_litigant ml2 where ml2.case_id = t1.case_id and (ml2.identity_type = '被申请人' or ml2.identity_type = '2')) as response_name,
               t3.entrusts_name,
               t4.agent_phone,
               t5.mediation_time,
               t5.password,
               t5.mediator_name
        FROM mdt_case t1
        LEFT JOIN mdt_meeting_record t5
        ON t1.case_id = t5.case_id AND t5.record_id = #{meetingRecordId}
        LEFT JOIN mdt_case_litigant t2
        ON t1.case_id = t2.case_id
        LEFT JOIN sys_entrusts t3
        ON t1.entrusts_id = t3.entrusts_id
        LEFT JOIN auth_ten_agent t4
        ON t5.mediator_id = t4.account_id
        WHERE t1.case_id = #{caseId}
		group by t1.case_id, t4.id
    </select>
    <select id="selectTmplCaseIds" resultType="java.lang.Integer">
        select DISTINCT tmpl_case_id
        from mdt_case
        where case_id in
        <foreach collection="caseIds" item="caseId" open="(" close=")" separator=",">
            #{caseId}
        </foreach>
    </select>
    <resultMap id="caseDetail" type="com.sct.tiaojie.service.mdt.dto.CaseDetail" autoMapping="true">
        <id property="caseId" column="case_id"/>
        <collection property="litigants" columnPrefix="lit_" ofType="com.sct.tiaojie.service.mdt.dto.MdtCaseLitigantDTO"
                    javaType="java.util.ArrayList" autoMapping="true">
            <id property="litigantId" column="litigant_id"/>
        </collection>
        <collection property="collectRecords" columnPrefix="cr_"
                    ofType="com.sct.tiaojie.service.mdt.dto.MdtCaseCollectRecordDTO"
                    javaType="java.util.ArrayList" autoMapping="true">
            <id property="recordId" column="record_id"/>
        </collection>
        <collection property="taskInstances" columnPrefix="tcr_"
                    ofType="com.sct.tiaojie.repository.dto.TaskInstanceDTO"
                    javaType="java.util.ArrayList" autoMapping="true">
            <id property="instanceId" column="instance_id"/>
        </collection>
        <collection property="customData" columnPrefix="cus_"
                    ofType="com.sct.tiaojie.service.mdt.dto.MdtCaseCustomModuleDTO"
                    javaType="java.util.ArrayList" autoMapping="true">
            <id property="dataId" column="data_id"/>
            <result property="data" column="field_value"
                    typeHandler="com.sct.tiaojie.service.mdt.handler.MdtCustomCaseDataHandler"/>
            <collection property="fields" columnPrefix="fd_" resultMap="moduleField"
                        javaType="java.util.ArrayList"/>
        </collection>
    </resultMap>
    <resultMap id="moduleField" type="com.sct.tiaojie.service.tmpl.dto.TmplModuleFieldDTO" autoMapping="true">
        <id property="tmplModuleFieldId" column="tmpl_module_field_id"/>
        <result property="readOnlyRoles" column="read_only_roles" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>
    <select id="selectDetailCase" resultMap="caseDetail">
        select
        t1.tmpl_case_id,
        t1.case_subject_matter,
        t1.case_id,
        t1.org_id,
        t1.dept_id,
        t1.company_id,
        t1.creator_id,
        t1.updater_id,
        t1.mdt_case_status,
        t1.case_no,
        t1.close_time,
        t1.mediate_begin_time,
        t1.expiration_time,
        t1.case_apply_time,
        t1.case_nature_content,
        t1.entrusts_id,
        t1.entrusts_dept_id,
        t6.entrusts_name,
        t1.mediate_result,
        t1.mediate_status,
        t1.close_reason,
        t1.success_reason,
        t1.case_name,
        t7.org_name,
        t8.dept_name,
        t1.current_mediator_name,
        t1.create_time,
        t1.mediate_done_time,
        t1.mediate_close_time,
        t1.business_type,
        t1.case_manage_close_time,
        t1.case_manage_close_reason,
        t1.suspend_reason,
        CASE WHEN t1.mediate_begin_time > NOW() THEN DATEDIFF(t1.expiration_time , t1.mediate_begin_time)
             WHEN t1.mdt_case_status = 40 THEN DATEDIFF(t1.expiration_time , t10.end_time)
             ELSE DATEDIFF(t1.expiration_time , NOW())
             END as remainingDays,
        t2.litigant_id as lit_litigant_id,
        t2.litigant_name as lit_litigant_name,
        t2.litigant_phone as lit_litigant_phone,
        t2.litigant_type as lit_litigant_type,
        t2.identity_type as lit_identity_type,
        t2.phone_status as lit_phone_status,
        t2.id_type as lit_id_type,
        t2.id_no as lit_id_no,
        t2.sex as lit_sex,
        t2.post as lit_post,
        t2.nation as lit_nation,
        t2.birth as lit_birth,
        t2.id_address as lit_id_address,
        t2.contact_address as lit_contact_address,
        t2.is_private_enterprise as lit_is_private_enterprise,
        t2.register_address as lit_register_address,
        t2.business_address as lit_business_address,
        t2.litigant_desc as lit_litigant_desc,
        t2.legal_agent_name as lit_legal_agent_name,
        t2.mbti as lit_mbti,
        t3.record_id as cr_record_id,
        t3.contact_phone as cr_contact_phone,
        t3.mediate_result as cr_mediate_result,
        t3.contactor_user as cr_contactor_user,
        t3.litigant_id as cr_litigant_id,
        t3.mediate_status as cr_mediate_status,
        t3.identity_type as cr_identity_type,
        t3.mediate_time as cr_mediate_time,
        t3.mediate_type as cr_mediate_type,
        t3.is_answer as cr_is_answer,
        t3.intention_amount as cr_intention_amount,
        t3.other_demand as cr_other_demand,
        t3.need_help_content as cr_need_help_content,
        t3.mediator_name as cr_mediator_name,
        t3.next_do_time as cr_next_do_time,
        t3.create_tm as cr_create_tm,
        t3.expected_success_date as cr_expected_success_date,


        t9.instance_id as tcr_instance_id,
        t9.case_id as tcr_case_id,
        t9.workflow_id as tcr_workflow_id,
        t9.config_id as tcr_config_id,
        t9.task_type as tcr_task_type,
        t9.time_limit as tcr_time_limit,
        t9.deadline as tcr_deadline,
        t9.manager_id_list as tcr_manager_id_list,
        t9.task_status as tcr_task_status,
        t9.remark as tcr_remark,
        t9.assign_account_id as tcr_assign_account_id,
        t9.assign_time as tcr_assign_time,
        t9.finish_account_id as tcr_finish_account_id,
        t9.finish_time as tcr_finish_time,
        t9.creator_id as tcr_creator_id,
        t9.updater_id as tcr_updater_id,
        t9.create_time as tcr_create_time,
        t9.update_time as tcr_update_time,



        t4.data_id as cus_data_id,
        t4.field_value as cus_field_value,
        t5.tmpl_module_id as cus_tmpl_module_id,
        t5.module_type as cus_module_type,
        t5.module_title as cus_module_title,
        t5.module_sn as cus_module_sn,
        t5.allow_multiple_record as cus_allow_multiple_record,
        tmf.tmpl_module_field_id as cus_fd_tmpl_module_field_id,
        tmf.field_title as cus_fd_field_title,
        tmf.field_ui as cus_fd_field_ui,
        tmf.field_values as cus_fd_field_values,
        tmf.field_sn as cus_fd_field_sn,
        tmf.original_name as cus_fd_original_name,
        tmf.component_relation as cus_fd_component_relation,
        tmf.read_only_roles as cus_fd_read_only_roles
        from mdt_case t1
        left join mdt_case_litigant t2
        on t1.case_id = t2.case_id
        left join mdt_case_collect_record t3
        on t1.case_id = t3.case_id
        left join tmpl_case tc
        on tc.tmpl_id = t1.tmpl_case_id
        left join tmpl_case_module t5
        on tc.tmpl_id = t5.tmpl_id and t5.module_type = 3
        left join mdt_case_custom_data t4
        on t1.case_id = t4.case_id and t4.tmpl_module_id = t5.tmpl_module_id
        left join tmpl_case_module_field tmf
        on t5.tmpl_module_id = tmf.tmpl_module_id
        left join sys_entrusts t6
        on t1.entrusts_id = t6.entrusts_id
        left join sys_mdt_org t7
        on t1.org_id = t7.org_id
        left join auth_dept t8
        on t7.company_id = t8.company_id and t1.dept_id = t8.dept_id
        left join task_instance t9
        on t1.case_id = t9.case_id
        left join (SELECT a.case_id,MAX(a.end_time) AS end_time FROM mdt_case_approval a GROUP BY a.case_id) t10 on t10.case_id = t1.case_id
        <where>
            t1.case_id = #{caseId}
        </where>
        order by t2.create_time,t3.create_tm desc
    </select>

    <select id="selectDetailCaseList" resultMap="caseDetail">
        select
        t1.tmpl_case_id,
        t1.case_id,
        t1.org_id,
        t1.dept_id,
        t1.company_id,
        t1.creator_id,
        t1.updater_id,
        t1.mdt_case_status,
        t1.case_no,
        t1.close_time,
        t1.mediate_begin_time,
        t1.expiration_time,
        t1.case_apply_time,
        t1.case_nature_content,
        t1.entrusts_id,
        t1.entrusts_dept_id,
        t6.entrusts_name,
        t1.mediate_result,
        t1.mediate_status,
        t1.close_reason,
        t1.success_reason,
        t1.case_name,
        t7.org_name,
        t8.dept_name,
        t1.current_mediator_name,
        t1.create_time,
        t1.mediate_close_time,
        t2.litigant_id as lit_litigant_id,
        t2.litigant_name as lit_litigant_name,
        t2.litigant_phone as lit_litigant_phone,
        t2.litigant_type as lit_litigant_type,
        t2.identity_type as lit_identity_type,
        t2.phone_status as lit_phone_status,
        t2.id_type as lit_id_type,
        t2.id_no as lit_id_no,
        t2.sex as lit_sex,
        t2.post as lit_post,
        t2.nation as lit_nation,
        t2.birth as lit_birth,
        t2.id_address as lit_id_address,
        t2.contact_address as lit_contact_address,
        t2.is_private_enterprise as lit_is_private_enterprise,
        t2.register_address as lit_register_address,
        t2.business_address as lit_business_address,
        t2.litigant_desc as lit_litigant_desc,
        t2.legal_agent_name as lit_legal_agent_name,
        t3.record_id as cr_record_id,
        t3.contact_phone as cr_contact_phone,
        t3.mediate_result as cr_mediate_result,
        t3.contactor_user as cr_contactor_user,
        t3.mediate_status as cr_mediate_status,
        t3.identity_type as cr_identity_type,
        t3.mediate_time as cr_mediate_time,
        t3.mediate_type as cr_mediate_type,
        t3.is_answer as cr_is_answer,
        t3.intention_amount as cr_intention_amount,
        t3.other_demand as cr_other_demand,
        t3.need_help_content as cr_need_help_content,
        t3.mediator_name as cr_mediator_name,
        t3.next_do_time as cr_next_do_time,
        t3.expected_success_date as cr_expected_success_date,

        t9.instance_id as tcr_instance_id,
        t9.case_id as tcr_case_id,
        t9.workflow_id as tcr_workflow_id,
        t9.config_id as tcr_config_id,
        t9.task_type as tcr_task_type,
        t9.time_limit as tcr_time_limit,
        t9.deadline as tcr_deadline,
        t9.manager_id_list as tcr_manager_id_list,
        t9.task_status as tcr_task_status,
        t9.remark as tcr_remark,
        t9.assign_account_id as tcr_assign_account_id,
        t9.assign_time as tcr_assign_time,
        t9.finish_account_id as tcr_finish_account_id,
        t9.finish_time as tcr_finish_time,
        t9.creator_id as tcr_creator_id,
        t9.updater_id as tcr_updater_id,
        t9.create_time as tcr_create_time,
        t9.update_time as tcr_update_time,



        t4.data_id as cus_data_id,
        t4.field_value as cus_field_value,
        t5.tmpl_module_id as cus_tmpl_module_id,
        t5.module_type as cus_module_type,
        t5.module_title as cus_module_title,
        t5.module_sn as cus_module_sn,
        tmf.tmpl_module_field_id as cus_fd_tmpl_module_field_id,
        tmf.field_title as cus_fd_field_title,
        tmf.field_ui as cus_fd_field_ui,
        tmf.field_values as cus_fd_field_values,
        tmf.field_sn as cus_fd_field_sn
        from mdt_case t1
        left join mdt_case_litigant t2
        on t1.case_id = t2.case_id
        left join mdt_case_collect_record t3
        on t1.case_id = t3.case_id
        left join tmpl_case tc
        on tc.tmpl_id = t1.tmpl_case_id
        left join tmpl_case_module t5
        on tc.tmpl_id = t5.tmpl_id and t5.module_type = 3
        left join mdt_case_custom_data t4
        on t1.case_id = t4.case_id and t4.tmpl_module_id = t5.tmpl_module_id
        left join tmpl_case_module_field tmf
        on t5.tmpl_module_id = tmf.tmpl_module_id
        left join sys_entrusts t6
        on t1.entrusts_id = t6.entrusts_id
        left join sys_mdt_org t7
        on t1.org_id = t7.org_id
        left join auth_dept t8
        on t7.company_id = t8.company_id and t1.dept_id = t8.dept_id
        left join task_instance t9
        on t1.case_id = t9.case_id
        left join (SELECT a.case_id,MAX(a.end_time) AS end_time FROM mdt_case_approval a GROUP BY a.case_id) t10
        on t10.case_id = t1.case_id
        <where>
            t1.case_id in
            <foreach collection="caseIdList" separator="," open="(" close=")" item="caseId">
                #{caseId}
            </foreach>
            and t1.tmpl_case_id = #{tmplId}
        </where>
    </select>

    <select id="selectCaseDetail" resultType="com.sct.tiaojie.service.mdt.dto.CaseDetail">
        select
        t1.tmpl_case_id,
        t1.case_subject_matter,
        t1.case_id,
        t1.org_id,
        t1.dept_id,
        t1.company_id,
        t1.creator_id,
        t1.updater_id,
        t1.mdt_case_status,
        t1.case_no,
        t1.close_time,
        t1.mediate_begin_time,
        t1.expiration_time,
        t1.case_apply_time,
        t1.case_nature_content,
        t1.entrusts_id,
        t1.entrusts_dept_id,
        t6.entrusts_name,
        t1.mediate_result,
        t1.mediate_status,
        t1.close_reason,
        t1.success_reason,
        t1.case_name,
        t7.org_name,
        t8.dept_name,
        t1.current_mediator_name,
        t1.create_time,
        t1.mediate_close_time,
        t1.mediate_done_time,
        t1.case_mediate_type
        from mdt_case t1
        left join sys_entrusts t6
        on t1.entrusts_id = t6.entrusts_id
        left join sys_mdt_org t7
        on t1.org_id = t7.org_id
        left join auth_dept t8
        on t7.company_id = t8.company_id and t1.dept_id = t8.dept_id
        <where>
            t1.case_id in
            <foreach collection="caseIdList" separator="," open="(" close=")" item="caseId">
                #{caseId}
            </foreach>
        </where>
        order by t1.create_time desc
    </select>

    <select id="selectLitigantDetail" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseLitigantDTO">
        select
        case_id,
        litigant_id,
        litigant_name,
        litigant_phone,
        litigant_type,
        identity_type,
        phone_status,
        id_type,
        id_no,
        sex,
        post,
        nation,
        birth,
        id_address,
        contact_address,
        is_private_enterprise,
        register_address,
        business_address,
        litigant_desc,
        legal_agent_name,
        contact_status
        from mdt_case_litigant
        <where>
            case_id in
            <foreach collection="caseIdList" separator="," open="(" close=")" item="caseId">
                #{caseId}
            </foreach>
        </where>
    </select>

    <select id="selectRecordDetail" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseCollectRecordDTO">
        select
        case_id,
        record_id,
        contact_phone,
        mediate_result,
        contactor_user,
        mediate_status,
        identity_type,
        mediate_time,
        mediate_type,
        is_answer,
        intention_amount,
        other_demand,
        need_help_content,
        mediator_name,
        next_do_time
        from mdt_case_collect_record
        <where>
            case_id in
            <foreach collection="caseIdList" separator="," open="(" close=")" item="caseId">
                #{caseId}
            </foreach>
        </where>
    </select>

    <resultMap id="customDetail" type="com.sct.tiaojie.service.mdt.dto.MdtCaseCustomModuleDTO">
        <id property="dataId" column="data_id"/>
        <result property="data" column="field_value"
                typeHandler="com.sct.tiaojie.service.mdt.handler.MdtCustomCaseDataHandler"/>
        <result property="caseId" column="case_id" />
        <result property="tmplModuleId" column="tmpl_module_id" />
        <result property="moduleType" column="module_type" />
        <result property="moduleSn" column="module_sn" />
        <result property="moduleTitle" column="module_title" />
        <collection property="fields" columnPrefix="fd_"
                    ofType="com.sct.tiaojie.service.tmpl.dto.TmplModuleFieldDTO"
                    javaType="java.util.ArrayList" autoMapping="true">
            <id property="tmplModuleFieldId" column="tmpl_module_field_id"/>
        </collection>
    </resultMap>

    <select id="selectCustomDetail" resultMap="customDetail">
        select
        t1.data_id,
        t1.case_id,
        t1.field_value,
        t1.tmpl_module_id,
        t2.module_type as module_type,
        t2.module_title as module_title,
        t2.module_sn as module_sn,
        tmf.tmpl_module_field_id as fd_tmpl_module_field_id,
        tmf.field_title as fd_field_title,
        tmf.field_ui as fd_field_ui,
        tmf.field_values as fd_field_values,
        tmf.field_sn as fd_field_sn
        from mdt_case_custom_data t1
        left join tmpl_case_module t2
        on t1.tmpl_module_id = t2.tmpl_module_id and t2.module_type = 3
        left join tmpl_case_module_field tmf
        on t1.tmpl_module_id = tmf.tmpl_module_id
        <where>
            case_id in
            <foreach collection="caseIdList" separator="," open="(" close=")" item="caseId">
                #{caseId}
            </foreach>
        </where>
    </select>



    <select id="selectCaseIdList" resultType="java.lang.Long">
        select
        t1.case_id
        from mdt_case t1
        <where>
            t1.case_id in
            <foreach collection="caseIdList" separator="," open="(" close=")" item="caseId">
                #{caseId}
            </foreach>
            and t1.tmpl_case_id = #{tmplId}
        </where>
    </select>

    <select id="getCaseCollectionRecordReceiveLogListByCaseId"
            resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseCollectionRecordReceiveLogDto">
        SELECT
        a.collection_id,
        jt.case_id,
        jt.case_no,
        a.collection_type,
        a.collection_result,
        a.creator_name,
        a.company_name,
        a.entrusts_name,
        a.tmpl_name,
        a.file_path,
        a.accept_time
        FROM
        mdt_case_collection_record a
        JOIN JSON_TABLE (
        a.case_info,
        '$[*]' COLUMNS ( case_id VARCHAR ( 200 ) PATH '$.caseId', case_no VARCHAR ( 200 ) PATH '$.caseNo' )) jt
        <where>
            and jt.case_id = #{caseId}
        </where>
        order by a.accept_time desc
    </select>

    <select id="getUserConcatPhoneList" resultType="com.sct.tiaojie.service.sys.dto.SysPhoneCheckRecordDto">
        SELECT
        a.case_id,
        a.case_no,
        a.entrusts_id,
        b.entrusts_name,
        c.litigant_id,
        c.litigant_name,
        c.identity_type,
        c.litigant_type,
        c.litigant_phone
        FROM
        mdt_case a
        INNER JOIN sys_entrusts b ON b.entrusts_id = a.entrusts_id
        INNER JOIN mdt_case_litigant c ON c.case_id = a.case_id
        <where>
            a.case_id IN
            <foreach collection="caseIdList" item="caseId" open="(" close=")" separator=",">
                #{caseId}
            </foreach>
            <if test="identityTypeList != null and identityTypeList.size() > 0">
                and c.identity_type in
                <foreach collection="identityTypeList" item="identityType" open="(" close=")" separator=",">
                    #{identityType}
                </foreach>
            </if>
            <if test="litigantTypeList != null and litigantTypeList.size() > 0">
                and c.litigant_type in
                <foreach collection="litigantTypeList" item="litigantType" open="(" close=")" separator=",">
                    #{litigantType}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCaseConcatPhone" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseBatchSmsDto">
        SELECT
        a.case_id,
        a.case_no,
        b.litigant_id,
        b.litigant_name,
        b.litigant_phone,
        CASE WHEN b.identity_type = '2' OR b.identity_type = '4' THEN c.litigant_id ELSE b.litigant_id END AS
        claimant_id,
        CASE WHEN b.identity_type = '2' OR b.identity_type = '4' THEN c.litigant_name ELSE b.litigant_name END AS
        claimant_name,
        CASE WHEN b.identity_type = '2' OR b.identity_type = '4' THEN b.litigant_name ELSE c.litigant_name END AS
        respond_name,
        CASE WHEN b.identity_type = '2' OR b.identity_type = '4' THEN b.litigant_id ELSE c.litigant_id END AS respond_id
        FROM
        mdt_case a
        INNER JOIN mdt_case_litigant b ON b.case_id = a.case_id
        INNER JOIN mdt_case_litigant c ON c.case_id = b.case_id
        AND c.litigant_id != b.litigant_id
        AND c.identity_type != b.identity_type
        <where>
            <if test="param != null">
                and a.case_id in
                <foreach collection="param.caseIdList" item="caseId" open="(" close=")" separator=",">
                    #{caseId}
                </foreach>
                <if test="param.litigantType != null">
                    and b.litigant_type = #{param.litigantType}
                </if>
                <if test="param.identityType != null">
                    and b.identity_type = #{param.identityType}
                </if>
            </if>
        </where>
    </select>

    <select id="listByCaseIds" resultType="com.sct.tiaojie.repository.mdt.entity.MdtCase">
        select *
        from mdt_case
        where case_id in
        <foreach collection="caseIds" item="caseId" open="(" close=")" separator=",">
            #{caseId}
        </foreach>
    </select>


    <sql id="groupSelect">
        <choose>
            <when test="groupCondition == 1">
                mdt_case.entrusts_id AS groupId, sys_entrusts.entrusts_name AS groupName
            </when>
            <when test="groupCondition == 2">
                mdt_case.org_id AS groupId, sys_mdt_org.org_name AS groupName
            </when>
            <when test="groupCondition == 3">
                mdt_case.dept_id AS groupId, auth_dept.dept_name AS groupName
            </when>
            <when test="groupCondition == 4">
                mdt_case.current_mediator_id AS groupId, auth_employee.employee_name AS groupName
            </when>
        </choose>
    </sql>

    <sql id="caseJoin">
        <choose>
            <when test="groupCondition == 1">
                LEFT JOIN sys_entrusts ON mdt_case.entrusts_id = sys_entrusts.entrusts_id
            </when>
            <when test="groupCondition == 2">
                LEFT JOIN sys_mdt_org ON mdt_case.org_id = sys_mdt_org.org_id
            </when>
            <when test="groupCondition == 3">
                LEFT JOIN auth_dept ON mdt_case.dept_id = auth_dept.dept_id
            </when>
            <when test="groupCondition == 4">
                LEFT JOIN auth_employee ON mdt_case.current_mediator_id = auth_employee.account_id
            </when>
        </choose>
    </sql>
    <sql id="caseAggregation">
       SUM(CASE WHEN mdt_case.mediate_result = 1 THEN 1 ELSE 0 END) AS mediateSuccessAmount,
       COUNT(mdt_case.case_id) AS mediateAmount,
       SUM(CASE WHEN mdt_case.success_reason = 1 THEN 1 ELSE 0 END) AS noProsecutionAmount,
       SUM(CASE WHEN mdt_case.success_reason = 2 THEN 1 ELSE 0 END) AS withdrawnAmount,
       SUM(CASE WHEN mdt_case.success_reason = 3 THEN 1 ELSE 0 END) AS outOfCourtAgreementAmount,
       SUM(CASE WHEN mdt_case.success_reason = 4 THEN 1 ELSE 0 END) AS confirmationRulingAmount,
       SUM(CASE WHEN mdt_case.success_reason = 5 THEN 1 ELSE 0 END) AS transferredMediationDecreeAmount
    </sql>

    <!-- 动态查询 -->
    <select id="getMediationSuccessRate" resultType="com.sct.tiaojie.service.mdt.dto.MediationSuccessRateDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="caseAggregation"/>
        FROM
        mdt_case
        <include refid="caseJoin"></include>
        WHERE
        mdt_case.case_id IN
        <foreach item="caseId" collection="caseIds" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        GROUP BY
        groupId
    </select>

    <sql id="diversionCaseAggregation">
       SUM(CASE WHEN mdt_case.mediate_result = 1 THEN 1 ELSE 0 END) AS mediateSuccessAmount,
       SUM(CASE WHEN mdt_case.mediate_result = 2 THEN 1 ELSE 0 END) AS mediateFailAmount,
       SUM(CASE WHEN mdt_case.success_reason = 1 THEN 1 ELSE 0 END) AS noProsecutionAmount,
       SUM(CASE WHEN mdt_case.success_reason = 2 THEN 1 ELSE 0 END) AS withdrawnAmount,
       SUM(CASE WHEN mdt_case.success_reason = 3 THEN 1 ELSE 0 END) AS outOfCourtAgreementAmount,
       SUM(CASE WHEN mdt_case.success_reason = 4 THEN 1 ELSE 0 END) AS confirmationRulingAmount,
       SUM(CASE WHEN mdt_case.success_reason = 5 THEN 1 ELSE 0 END) AS transferredMediationDecreeAmount
    </sql>

    <select id="getDiversionRate" resultType="com.sct.tiaojie.service.mdt.dto.DiversionRateDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="diversionCaseAggregation"/>
        FROM
        mdt_case
        <include refid="caseJoin"></include>
        WHERE
        mdt_case.case_id IN
        <foreach item="caseId" collection="caseIds" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        GROUP BY
        groupId
    </select>

    <sql id="caseCloseAggregation">
       SUM(CASE WHEN mdt_case.mediate_result = 1 THEN 1 ELSE 0 END) AS mediateSuccessAmount,
       SUM(CASE WHEN mdt_case.mediate_result = 2 THEN 1 ELSE 0 END) AS mediateFailAmount,
       COUNT(mdt_case.case_id) AS mediateAmount
    </sql>

    <select id="getCaseCloseRate" resultType="com.sct.tiaojie.service.mdt.dto.CaseCloseRateDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="caseCloseAggregation"/>
        FROM
        mdt_case
        <include refid="caseJoin"></include>
        WHERE
        mdt_case.case_id IN
        <foreach item="caseId" collection="caseIds" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        GROUP BY
        groupId
    </select>

    <sql id="caseReachAggregation">
       EXTRACT(YEAR FROM mdt_case.close_time) AS year,
       EXTRACT(MONTH FROM mdt_case.close_time) AS month,
       EXTRACT(DAY FROM mdt_case.close_time) AS day,
       COUNT(*) AS closeCaseAmount,
       SUM(CASE WHEN mdt_case.case_touched = 1 THEN 1 ELSE 0 END) AS caseTouchedAmount
    </sql>

    <select id="getCaseReachRate" resultType="com.sct.tiaojie.service.mdt.dto.CaseReachRateDTO">
        SELECT
        <include refid="groupSelect"></include>,
        <include refid="caseReachAggregation"/>
        FROM
        mdt_case
        <include refid="caseJoin"></include>
        WHERE
        mdt_case.close_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY
        groupId,
        year,
        month,
        day
        ORDER BY
        year,
        month,
        day
    </select>

    <select id="getFieldDataByFieldValue" resultType="java.lang.String">
        select ${valueFieldName} from ${tableName} where ${keyFieldName} = #{value}
    </select>

    <select id="getFieldDataByFieldValueList" resultType="java.lang.String">
        select ${valueFieldName} from ${tableName} where ${keyFieldName} in
        <foreach collection="values" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </select>

    <select id="selectCollectionReceiveLog"
            resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseCollectionRecordReceiveLogDto">
        <include refid="selectCaseCollectionRecordReviewLogList"/>
        <where>
            <include refid="condition"/>
        </where>
        order by a.accept_time desc
    </select>

    <select id="getAllListId" resultType="java.lang.Long">
        select t1.case_id
        <include refid="joinColumnPage"/>
        <where>
            <include refid="conditionPage"/>
        </where>
        group by t1.case_id
        order by t1.create_time desc, t1.case_id desc
    </select>

    <select id="getAllMdtCase" resultType="com.sct.tiaojie.repository.mdt.entity.MdtCase">
        select t1.*
        <include refid="joinColumnPage"/>
        <where>
            <include refid="conditionPage"/>
        </where>
        group by t1.case_id
    </select>

    <select id="getCreatorIdAndEntrustsDeptIds" resultType="com.sct.tiaojie.repository.mdt.entity.MdtCase">
        select DISTINCT t1.creator_id, t1.entrusts_dept_id
        from mdt_case t1
        <where>

            <include refid="conditionPage"/>
        </where>
    </select>

    <select id="selectCaseIdListByParam" resultType="java.lang.Long">
        select t1.case_id
        FROM mdt_case t1
        left join sys_entrusts t2
        on t1.entrusts_id = t2.entrusts_id
        left join mdt_case_litigant t3
        on t3.case_id = t1.case_id
        left join sys_mdt_org t4
        on t1.org_id = t4.org_id
        left join auth_dept t5
        on t1.dept_id = t5.dept_id
        left join auth_employee t6 on t6.account_id = t1.creator_id
        left join (SELECT a.case_id,MAX(a.end_time) AS end_time FROM mdt_case_approval a GROUP BY a.case_id) t10 on t10.case_id = t1.case_id
        <where>
            <include refid="conditionPage"/>
            <if test="tmplId != null">
                and t1.tmpl_case_id = #{tmplId}
            </if>
        </where>
        group by t1.case_id
        order by t1.create_time desc, t1.case_id desc
    </select>

    <select id="tmplList" resultType="com.sct.tiaojie.service.tmpl.dto.TmplDTO">
        select
        t99.tmpl_id,
        t99.tmpl_title,
        '1' as tmpl_type,
        t99.entrusts_name,
        t99.nature_title,
        t99.tmpl_status,
        t99.update_time,
        t99.tmpl_desc,
        t99.is_delete
        <include refid="joinColumnPage"/>
        left join tmpl_case t99 on t99.tmpl_id = t1.tmpl_case_id
        <where>
            <include refid="conditionPage"/>
        </where>
        group by t99.tmpl_id
        order by t99.create_time desc, t99.tmpl_id desc
    </select>

    <select id="tmplList2" resultType="com.sct.tiaojie.service.tmpl.dto.TmplDTO">
        select
        t99.tmpl_id,
        t99.tmpl_title,
        '1' as tmpl_type,
        t99.entrusts_name,
        t99.nature_title,
        t99.tmpl_status,
        t99.update_time,
        t99.tmpl_desc,
        t99.is_delete
        from mdt_case t1
        left join tmpl_case t99 on t99.tmpl_id = t1.tmpl_case_id
        <where>
            <include refid="conditionPage"/>
        </where>
        group by t99.tmpl_id
        order by t99.create_time desc, t99.tmpl_id desc
    </select>
    <select id="selectCaseLitigantInfo" resultType="com.sct.tiaojie.service.mdt.dto.MediateCasePageDTO">
        SELECT case_id,
               GROUP_CONCAT(DISTINCT CASE WHEN identity_type IN (1,3) THEN litigant_name END) AS defend_ant_litigant_names,
               GROUP_CONCAT(DISTINCT CASE WHEN identity_type IN (2,4) THEN litigant_name END) AS plain_tiff_litigant_names
        from mdt_case_litigant WHERE case_id in
        <foreach item="caseId" collection="caseIdList" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        GROUP BY case_id
    </select>

</mapper>