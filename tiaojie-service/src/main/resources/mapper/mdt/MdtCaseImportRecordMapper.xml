<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtCaseImportRecordMapper">
    <select id="pageList" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseImportRecordDTO">
    SELECT
    t1.case_import_id,
	t1.create_time,
	t1.import_file_name,
	t1.import_file_path,
	t1.error_mark_file_path,
	t1.total_mark_file_path,
	t1.imp_flag,
	t1.total_cases,
	t1.failed_cases,
	t1.success_cases,
	t1.import_status,
	t2.employee_name,
	t3.tmpl_title,
	t4.entrusts_name,
	t5.dept_name
FROM
	mdt_case_import_record t1
	LEFT JOIN auth_employee t2 ON t2.account_id = t1.import_account_id
	LEFT JOIN deputy_tmpl t3 ON t3.deputy_tmpl_id = t1.deputy_tmpl_id
	LEFT JOIN sys_entrusts t4 ON t4.entrusts_id = t1.entrusts_id
	LEFT JOIN auth_dept t5 ON t1.entrusts_dept_id = t5.dept_id
<where>
	<if test="param.importAccountId != null">
		AND t1.import_account_id = #{param.importAccountId}
	</if>
	<if test="param.importStatus != null">
		AND t1.import_status = #{param.importStatus}
	</if>
	<if test="param.impFlag != null">
		AND t1.imp_flag = #{param.impFlag}
	</if>
</where>
ORDER BY t1.create_time DESC
    </select>

</mapper>