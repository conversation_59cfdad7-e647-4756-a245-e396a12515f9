<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtCaseApprovalMapper">
    <select id="pageList" resultType="com.sct.tiaojie.service.mdt.dto.MdtCaseApprovalPageDTO">
      SELECT
      t1.approval_id,
      t1.case_id,
      t2.case_no,
      t1.approval_type,
      t1.mdt_result,
      t1.close_reason,
      t1.success_reason,
      DATE_FORMAT(t1.case_close_time,'%Y-%m-%d') as caseCloseTime,
      DATE_FORMAT(t1.case_delay_time,'%Y-%m-%d') as caseDelayTime,
      t1.delay_reason,
      t1.start_man_name,
      t1.start_man_role,
      t1.start_org_name,
      t1.approval_status,
      t1.first_audit_result,
      t1.first_audit_des,
      t1.second_audit_result,
      t1.second_audit_des,
      t1.start_time,
      t1.case_manage_close_reason,
      t1.suspend_reason,
      case
      when t1.approval_status = '1' and #{param.startOrgId} is not null then 'false'
      when t1.approval_status = '5' and #{param.entrustsId} is not null then 'false'
      else 'true' end as disabled
      FROM
      mdt_case_approval t1
      LEFT JOIN mdt_case t2 ON t1.case_id = t2.case_id
        <where>
            <if test="param != null">
                <if test="param.caseNo != null and param.caseNo !=''">
                  AND t2.case_no LIKE CONCAT('%', #{param.caseNo}, '%')
                </if>
                <if test="param.approvalType != null and param.approvalType !=''">
                    AND t1.approval_type = #{param.approvalType}
                </if>
                <if test="param.startOrgIdList != null and !param.startOrgIdList.isEmpty">
                  AND t1.start_org_id IN
                  <foreach collection="param.startOrgIdList" item="orgId" open="(" close=")" separator=",">
                    #{orgId}
                  </foreach>
                </if>
                <if test="param.startOrgIds != null and !param.startOrgIds.isEmpty or (param.startOrgDeptIds != null and !param.startOrgDeptIds.isEmpty) or (param.startOrgMediatorIds != null and !param.startOrgMediatorIds.isEmpty)">
                AND (
                </if>
                <if test="param.startOrgIds != null and !param.startOrgIds.isEmpty">
                    t1.start_org_id IN
                    <foreach collection="param.startOrgIds" item="startOrgId" open="(" close=")" separator=",">
                        #{startOrgId}
                    </foreach>
                    <if test="param.startOrgDeptIds != null and !param.startOrgDeptIds.isEmpty">
                        OR (
                        t1.start_dept_id IN
                        <foreach collection="param.startOrgDeptIds" item="startOrgDeptId" open="(" close=")"
                                 separator=",">
                            #{startOrgDeptId}
                        </foreach>
                        <if test="param.startOrgMediatorIds != null and !param.startOrgMediatorIds.isEmpty">
                            OR t1.start_man_id IN
                            <foreach collection="param.startOrgMediatorIds" item="startOrgMediatorId" open="(" close=")"
                                     separator=",">
                                #{startOrgMediatorId}
                            </foreach>
                        </if>
                        )
                    </if>
                    <if test="param.startOrgMediatorIds != null and !param.startOrgMediatorIds.isEmpty">
                        OR (
                        t1.start_man_id IN
                        <foreach collection="param.startOrgMediatorIds" item="startOrgMediatorId" open="(" close=")"
                                 separator=",">
                            #{startOrgMediatorId}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="param.startOrgIds == null or param.startOrgIds.isEmpty">
                    <if test="param.startOrgDeptIds != null and !param.startOrgDeptIds.isEmpty">
                        t1.start_dept_id IN
                        <foreach collection="param.startOrgDeptIds" item="startOrgDeptId" open="(" close=")"
                                 separator=",">
                            #{startOrgDeptId}
                        </foreach>
                        <if test="param.startOrgMediatorIds != null and !param.startOrgMediatorIds.isEmpty">
                            OR t1.start_man_id IN
                            <foreach collection="param.startOrgMediatorIds" item="startOrgMediatorId" open="(" close=")"
                                     separator=",">
                                #{startOrgMediatorId}
                            </foreach>
                        </if>
                    </if>
                    <if test="param.startOrgMediatorIds != null and !param.startOrgMediatorIds.isEmpty and (param.startOrgDeptIds == null or param.startOrgDeptIds.isEmpty)">
                        t1.start_man_id IN
                        <foreach collection="param.startOrgMediatorIds" item="startOrgMediatorId" open="(" close=")"
                                 separator=",">
                            #{startOrgMediatorId}
                        </foreach>
                    </if>
                </if>
                <if test="param.startOrgIds != null and !param.startOrgIds.isEmpty or (param.startOrgDeptIds != null and !param.startOrgDeptIds.isEmpty) or (param.startOrgMediatorIds != null and !param.startOrgMediatorIds.isEmpty)">
                    )
                </if>
                <if test="param.approvalStatus != null and param.approvalStatus !=''">
                    AND t1.approval_status = #{param.approvalStatus}
                </if>
                <if test="param.approvalResult != null and param.approvalResult !=''">
                    AND t1.approval_result = #{param.approvalResult}
                </if>
                <if test="param.startManId != null and param.startManId !=''">
                    AND t1.start_man_id = #{param.startManId}
                </if>
                <if test="param.startOrgId != null and param.startOrgId !=''">
                    AND t1.start_org_id = #{param.startOrgId}
                </if>
                <if test="param.startDeptId != null and param.startDeptId !=''">
                    AND t1.start_dept_id = #{param.startDeptId}
                </if>
                <if test="param.entrustsId != null and param.entrustsId !=''">
                    AND t2.entrusts_id = #{param.entrustsId}
                </if>
                <if test="param.startTime != null and param.startTime !=''">
                  AND t1.start_time >= #{param.startTime}
                </if>
                <if test="param.endTime != null and param.endTime !=''">
                  AND t1.start_time &lt;= #{param.endTime}
                </if>
                <if test="param.completeReview != null">
                    AND (t2.complete_review = #{param.completeReview} OR t1.approval_type = 2)
                </if>
                <if test="param.creatorId != null and param.creatorId != ''">
                    AND t2.creator_id = #{param.creatorId}
                </if>
                <if test="param.entrustsDeptId != null and param.entrustsDeptId != ''">
                    AND t2.entrusts_dept_id = #{param.entrustsDeptId}
                </if>
            </if>
        </where>
      order by t1.update_time desc
    </select>

    <select id="getMdtApproval" resultType="com.sct.tiaojie.repository.mdt.entity.MdtCaseApproval">
        SELECT
        t1.*
        FROM
        mdt_case_approval t1
        LEFT JOIN mdt_case t2 ON t1.case_id = t2.case_id
        <where>
            <if test="param != null">
                <if test="param.caseNo != null and param.caseNo !=''">
                    AND t2.case_no LIKE CONCAT('%', #{param.caseNo}, '%')
                </if>
                <if test="param.approvalType != null and param.approvalType !=''">
                    AND t1.approval_type = #{param.approvalType}
                </if>
                <if test="param.startOrgIdList != null and !param.startOrgIdList.isEmpty">
                    AND t1.start_org_id IN
                    <foreach collection="param.startOrgIdList" item="orgId" open="(" close=")" separator=",">
                        #{orgId}
                    </foreach>
                </if>
                <if test="param.approvalStatus != null and param.approvalStatus !=''">
                    AND t1.approval_status = #{param.approvalStatus}
                </if>
                <if test="param.approvalResult != null and param.approvalResult !=''">
                    AND t1.approval_result = #{param.approvalResult}
                </if>
                <if test="param.startManId != null and param.startManId !=''">
                    AND t1.start_man_id = #{param.startManId}
                </if>
                <if test="param.startOrgId != null and param.startOrgId !=''">
                    AND t1.start_org_id = #{param.startOrgId}
                </if>
                <if test="param.startDeptId != null and param.startDeptId !=''">
                    AND t1.start_dept_id = #{param.startDeptId}
                </if>
                <if test="param.entrustsId != null and param.entrustsId !=''">
                    AND t2.entrusts_id = #{param.entrustsId}
                </if>
                <if test="param.startTime != null and param.startTime !=''">
                    AND t1.start_time >= #{param.startTime}
                </if>
                <if test="param.endTime != null and param.endTime !=''">
                    AND t1.start_time &lt;= #{param.endTime}
                </if>
                <if test="param.completeReview != null">
                    AND (t2.complete_review = #{param.completeReview} OR t1.approval_type = 2)
                </if>
                <if test="param.creatorId != null and param.creatorId != ''">
                    AND t2.creator_id = #{param.creatorId}
                </if>
                <if test="param.entrustsDeptId != null and param.entrustsDeptId != ''">
                    AND t2.entrusts_dept_id = #{param.entrustsDeptId}
                </if>
            </if>
        </where>
        order by t1.update_time desc
    </select>

</mapper>