<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtCaseDistributeRecordMapper">

    <sql id="selectDistributeRecordLogList">
        SELECT
            a.record_id,
            b.case_id,
            b.case_no,
            c.entrusts_name,
            a.operator_name,
            CASE
                WHEN g.org_name IS NOT NULL THEN
                    g.org_name ELSE h.entrusts_name
                END AS org_name,
            CASE
                WHEN a.target_mediator_id IS NOT NULL THEN
                    a.target_mediator_name
                WHEN a.target_dept_id IS NOT NULL THEN
                    a.target_dept_name
                WHEN a.target_mdt_org_id IS NOT NULL THEN
                    a.target_mdt_org_name ELSE NULL
                END AS distribute_target_name,
            jt.from_status,
            jt.to_status,
            a.operate_time,
            a.roles
        FROM
            mdt_case_distribute_record a
                JOIN JSON_TABLE (
                    a.case_list,
                    '$[*]' COLUMNS (
        case_id BIGINT PATH '$.caseId',
        case_no VARCHAR ( 150 ) PATH '$.caseNo',
        from_status VARCHAR ( 150 ) PATH '$.fromStatus',
        to_status VARCHAR ( 150 ) PATH '$.toStatus'
        )) AS jt ON 1 = 1
                INNER JOIN mdt_case b ON jt.case_id = b.case_id
                INNER JOIN sys_entrusts c ON c.entrusts_id = b.entrusts_id
                INNER JOIN auth_employee d ON d.account_id = a.operator_id
                LEFT JOIN sys_mdt_org g ON g.company_id = d.company_id
                LEFT JOIN sys_entrusts h ON h.company_id = d.company_id
    </sql>

    <sql id="condition">
        <if test="param != null">
            <if test="param.entrustsName != null">
                and c.entrusts_name like concat('%',#{param.entrustsName},'%')
            </if>
            <if test="param.caseNo != null">
                and b.case_no like concat('%',#{param.caseNo},'%')
            </if>
            <if test="param.operatorName != null">
                and a.operator_name like concat('%',#{param.operatorName},'%')
            </if>
            <if test="param.distributeTargetName != null">
                and (a.target_mdt_org_name like concat('%',#{param.distributeTargetName},'%') or
                a.target_dept_name like concat('%',#{param.distributeTargetName},'%') or
                a.target_mediator_id like concat('%',#{param.distributeTargetName},'%'))
            </if>
            <if test="param.operateTime != null">
                and date(a.operate_time) = #{param.operateTime}
            </if>
            <if test="param.operateTimeStart != null and param.operateTimeStart != ''">
                and a.operate_time >= #{param.operateTimeStart}
            </if>
            <if test="param.operateTimeEnd != null and param.operateTimeEnd != ''">
                and a.operate_time &lt;= #{param.operateTimeEnd}
            </if>
            <if test="param.entrustsId != null">
                and b.entrusts_id = #{param.entrustsId}
            </if>
        </if>
    </sql>

    <select id="getPageDistributeRecordLog" resultType="com.sct.tiaojie.service.mdt.dto.CaseDistributeLogDto">
        <include refid="selectDistributeRecordLogList"/>
        <where>
            <include refid="condition"/>
        </where>
        order by a.operate_time desc
    </select>

    <select id="getDistributeRecordListByCaseId" resultType="com.sct.tiaojie.service.mdt.dto.CaseDistributeLogDto">
        SELECT
        a.record_id,
        b.case_id,
        b.case_no,
        c.entrusts_name,
        a.operator_name,
        CASE
        WHEN g.org_name IS NOT NULL THEN
        g.org_name ELSE h.entrusts_name
        END AS org_name,
        CASE
        WHEN a.target_mediator_id IS NOT NULL THEN
        a.target_mediator_name
        WHEN a.target_dept_id IS NOT NULL THEN
        a.target_dept_name
        WHEN a.target_mdt_org_id IS NOT NULL THEN
        a.target_mdt_org_name ELSE NULL
        END AS distribute_target_name,
        jt.from_status,
        jt.to_status,
        a.operate_time,
        a.roles
        FROM
        mdt_case_distribute_record a
        JOIN JSON_TABLE (
        a.case_list,
        '$[*]' COLUMNS (
        case_id BIGINT PATH '$.caseId',
        case_no VARCHAR ( 150 ) PATH '$.caseNo',
        from_status VARCHAR ( 150 ) PATH '$.fromStatus',
        to_status VARCHAR ( 150 ) PATH '$.toStatus'
        )) AS jt ON 1 = 1
        INNER JOIN mdt_case b ON jt.case_id = b.case_id
        INNER JOIN sys_entrusts c ON c.entrusts_id = b.entrusts_id
        INNER JOIN auth_employee d ON d.account_id = a.operator_id
        LEFT JOIN sys_mdt_org g ON g.company_id = d.company_id
        LEFT JOIN sys_entrusts h ON h.company_id = d.company_id
        <where>
            and b.case_id = #{caseId}
        </where>
        order by b.create_time desc
    </select>

    <select id="getLastDistributeRecordList" resultType="com.sct.tiaojie.service.mdt.dto.CaseDistributeLogDto">
        SELECT jt.case_id,
               CASE
                   WHEN a.target_mdt_org_id IS NOT NULL THEN
                       a.target_mdt_org_name
                   WHEN a.target_dept_id IS NOT NULL THEN
                       a.target_dept_name
                   WHEN a.target_mediator_id IS NOT NULL THEN
                       a.target_mediator_name
                   ELSE NULL
                   END AS distribute_target_name,
               a.operate_time,
               c.account_mobile AS mediator_concat_phone
        FROM mdt_case_distribute_record a
                 JOIN JSON_TABLE(
                a.case_list,
                '$[*]' COLUMNS (
			case_id BIGINT PATH '$.caseId',
			case_no VARCHAR ( 150 ) PATH '$.caseNo',
			from_status VARCHAR ( 150 ) PATH '$.fromStatus',
			to_status TINYINT ( 4 ) PATH '$.toStatus'
		)) AS jt ON 1 = 1
                 INNER JOIN mdt_case b ON b.case_id = jt.case_id
                 LEFT JOIN auth_account c ON c.account_id = a.target_mediator_id
        <where>
            jt.case_id in
            <foreach collection="caseIdList" item="caseId" open="(" close=")" separator=",">
                #{caseId}
            </foreach>
        </where>
    </select>

    <select id="selectDistributeLogList" resultType="com.sct.tiaojie.service.mdt.dto.CaseDistributeLogDto">
        <include refid="selectDistributeRecordLogList"/>
        <where>
            <include refid="condition"/>
        </where>
        order by a.operate_time desc
    </select>

</mapper>