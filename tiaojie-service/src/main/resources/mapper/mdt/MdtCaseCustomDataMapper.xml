<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtCaseCustomDataMapper">


    <select id="getOneByModuleTitle" resultType="com.sct.tiaojie.repository.mdt.entity.MdtCaseCustomData">
        SELECT t2.* FROM tmpl_case_module t1 LEFT JOIN mdt_case_custom_data t2 on t2.tmpl_module_id = t1.tmpl_module_id and t2.case_id = #{caseId}
        WHERE t1.module_title = #{moduleTitle} and t2.case_id is not null
    </select>
</mapper>