<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sct.tiaojie.repository.mdt.mapper.MdtInstrumentsMapper">


    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultType="com.sct.tiaojie.service.mdt.dto.MdtInstrumentsDTO">
        select a.instruments_id,
               a.case_id,
               a.instruments_name,
               a.instruments_file_path,
               a.instruments_identity_type,
               a.instruments_litigant_ids,
               a.file_sign_info_id,
               a.instruments_sign_status,
               a.instruments_tmpl_id,
               a.creator_id,
               b.employee_name as creator_name,
               a.create_time,
               a.updater_id,
               c.employee_name as updater_name,
               a.update_time,
               a.delete_flag,
               a.create_type
        from mdt_instruments a
                 inner join auth_employee b on b.account_id = a.creator_id
                 left join auth_employee c on c.account_id = a.updater_id
        where a.instruments_id = #{instrumentsId}
          and a.delete_flag = 0
    </select>



    <select id="getListByCaseId" resultType="com.sct.tiaojie.service.mdt.dto.MdtInstrumentsDTO">
        SELECT a.instruments_id,
               a.case_id,
               a.instruments_name,
               a.instruments_file_path,
               a.instruments_identity_type,
               a.instruments_litigant_ids,
               a.file_sign_info_id,
               a.instruments_sign_status,
               a.instruments_tmpl_id,
               d.doc_tmpl_title AS instruments_tmpl_name,
               a.creator_id,
               b.employee_name AS creator_name,
               a.create_time,
               a.updater_id,
               c.employee_name AS updater_name,
               a.update_time,
               a.delete_flag,
               a.create_type
        FROM mdt_instruments a
                 INNER JOIN auth_employee b ON b.account_id = a.creator_id
                 LEFT JOIN auth_employee c ON c.account_id = a.updater_id
                 LEFT JOIN document_template d on d.doc_tmpl_id = a.instruments_tmpl_id
        where a.case_id = #{caseId}
          and a.delete_flag = 0
        order by a.create_time desc
    </select>


</mapper>