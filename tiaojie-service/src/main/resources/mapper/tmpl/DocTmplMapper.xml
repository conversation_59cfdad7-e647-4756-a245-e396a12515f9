<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.tmpl.mapper.DocTmplMapper">

    <select id="pageList" resultType="com.sct.tiaojie.service.tmpl.dto.DocTmplDTO">
        SELECT
            t1.doc_tmpl_id,
            t1.doc_tmpl_title,
            t1.doc_tmpl_type,
            t1.tmpl_id,
            t2.tmpl_title AS tmplTitle,
            t2.entrusts_name AS entrustsName,
            t2.nature_title AS natureTitle,
            t3.employee_name as updaterName,
            t1.remark AS remark,
            t1.tmpl_status,
            t1.update_time,
            t1.is_delete
        FROM
            document_template t1
        INNER JOIN tmpl_case t2
        ON t1.tmpl_id = t2.tmpl_id
        LEFT JOIN auth_employee t3
        ON t1.updater_id = t3.account_id
        WHERE
            t1.is_delete = '0' or t1.is_delete is null
            <if test="param != null">
                <if test="param.docTmplTitle != null">
                    and t1.doc_tmpl_title like concat('%', #{param.docTmplTitle}, '%')
                </if>
                <if test="param.entrustsName != null and param.entrustsName != ''">
                    and t2.entrusts_name = #{param.entrustsName}
                </if>
                <if test="param.entrustsId != null and param.entrustsId != ''">
                    and t2.entrusts_id = #{param.entrustsId}
                </if>
                <if test="param.docTmplType != null and param.docTmplType != ''">
                    and t1.doc_tmpl_type = #{param.docTmplType}
                </if>
                <if test="param.natureTitle != null and param.natureTitle != ''">
                    and t2.nature_title = #{param.natureTitle}
                </if>
                <if test="param.tmplStatus != null">
                    and t1.tmpl_status = #{param.tmplStatus}
                </if>
                <if test="param.updaterName != null and param.updaterName != ''">
                    and t3.employee_name like concat('%', #{param.updaterName}, '%')
                </if>
            </if>
    </select>
</mapper>