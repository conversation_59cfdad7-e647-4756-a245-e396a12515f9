<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.tmpl.mapper.TmplBasisModuleMapper">

    <resultMap id="listMap" type="com.sct.tiaojie.service.tmpl.dto.TmplBasisModuleDetail"
               autoMapping="true">
        <id property="basisModuleId" column="basis_module_id" />
        <collection property="moduleFields" autoMapping="true" javaType="java.util.ArrayList"
                    ofType="com.sct.tiaojie.repository.tmpl.entity.TmplBasisModuleField"
                    columnPrefix="tmf_">
            <id property="basisModuleFieldId" column="basis_module_field_id" />
        </collection>
    </resultMap>
    <select id="basisModuleFieldList" resultMap="listMap">
        SELECT
        t2.basis_module_id as basis_module_id,
        t2.module_title as module_title,
        t2.module_desc as module_desc,
        t2.module_type as module_type,
        t2.table_name as table_name,
        t2.module_sn as module_sn,
        t2.is_fixed as is_fixed,
        t2.allow_multiple_record as allow_multiple_record,
        t3.basis_module_id as tmf_basis_module_id,
        t3.basis_module_field_id as tmf_basis_module_field_id,
        t3.field_name as tmf_field_name,
        t3.field_title as tmf_field_title,
        t3.field_desc as tmf_field_desc,
        t3.field_sn as tmf_field_sn,
        t3.field_ui as tmf_field_ui,
        t3.field_data_type as tmf_field_data_type,
        t3.field_values as tmf_field_values,
        t3.field_dict_type as tmf_field_dict_type,
        t3.is_require as tmf_is_require,
        t3.is_update as tmf_is_update,
        t3.is_desensitize as tmf_is_desensitize,
        t3.desensitize_rule as tmf_desensitize_rule,
        t3.is_validated as tmf_is_validated,
        t3.validation as tmf_validation,
        t3.show_format as tmf_show_format,
        t3.file_semantics as tmf_file_semantics,
        t3.is_disable as tmf_is_disable
        FROM tmpl_basis_module t2
        LEFT JOIN tmpl_basis_module_field t3
        ON t2.basis_module_id = t3.basis_module_id
    </select>
</mapper>