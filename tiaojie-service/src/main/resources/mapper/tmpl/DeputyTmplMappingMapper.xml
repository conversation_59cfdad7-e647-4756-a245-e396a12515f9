<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.tmpl.mapper.DeputyTmplMappingMapper">

    <resultMap id="listMap" type="com.sct.tiaojie.service.tmpl.dto.DeputyTmplDetail" autoMapping="true">
        <id property="deputyTmplId" column="deputy_tmpl_id" />
        <collection property="allModules" columnPrefix="dtm_" resultMap="moduleMap"
                    javaType="java.util.ArrayList" >
        </collection>
    </resultMap>

    <resultMap id="tmplListMap" type="com.sct.tiaojie.service.tmpl.dto.DeputyTmplDetail" autoMapping="true">
        <id property="tmplId" column="tmpl_id" />
        <collection property="allModules" columnPrefix="dtm_" resultMap="moduleMap"
                    javaType="java.util.ArrayList" >
        </collection>
    </resultMap>

    <resultMap id="moduleMap" type="com.sct.tiaojie.service.tmpl.dto.DeputyTmplModuleDTO" autoMapping="true">
        <id property="tmplModuleId" column="tmpl_module_id" />
        <result property="moduleSn" column="module_sn" />
        <result property="moduleTitle" column="module_title" />
        <result property="moduleType" column="module_type" />
        <result property="allowMultipleRecord" column="allow_multiple_record" />
        <collection property="mappings" autoMapping="true" javaType="java.util.ArrayList"
                    ofType="com.sct.tiaojie.service.tmpl.dto.DeputyTmplMappingDTO">
            <id property="mappingId" column="mapping_id" />
        </collection>
    </resultMap>

    <select id="mappingList" resultMap="listMap">
        select t1.*,
        t2.mapping_id as dtm_mapping_id,
        t2.deputy_tmpl_id as dtm_deputy_tmpl_id,
        t2.field_name as dtm_field_name,
        t2.tmpl_module_field_id as dtm_tmpl_module_field_id,
        t2.field_sn as dtm_field_sn,
        t2.converter as dtm_converter,
        t2.create_time as dtm_create_time,
        t2.creator_id as dtm_creator_id,
        t2.update_time as dtm_update_time,
        t2.updater_id as dtm_updater_id,
        t4.module_type as dtm_module_type,
        t4.module_title as dtm_module_title,
        t4.module_sn as dtm_module_sn,
        t4.allow_multiple_record as dtm_allow_multiple_record,
        t4.table_name as dtm_table_name,
        t4.tmpl_module_id as dtm_tmpl_module_id,
        IF(t2.mapping_id IS NULL, NULL, t3.field_dict_type) as dtm_field_dict_type,
        IF(t2.mapping_id IS NULL, NULL, t3.field_data_type) as dtm_field_data_type,
        IF(t2.mapping_id IS NULL, NULL, t3.field_values) as dtm_field_values,
        IF(t2.mapping_id IS NULL, NULL, t3.is_validated) as dtm_is_validated,
        IF(t2.mapping_id IS NULL, NULL, t3.is_require) as dtm_is_require,
        IF(t2.mapping_id IS NULL, NULL, t3.is_update) as dtm_is_update,
        IF(t2.mapping_id IS NULL, NULL, t3.validation) as dtm_validation,
        IF(t2.mapping_id IS NULL, NULL, t3.field_name) as dtm_table_field_name,
        IF(t2.mapping_id IS NULL, NULL, t3.to_case_ext_field) as dtm_to_case_ext_field,
        IF(t2.mapping_id IS NULL, NULL, t3.field_title) as dtm_field_title,
        IF(t2.mapping_id IS NULL, NULL, t3.original_name) as dtm_original_name
        from deputy_tmpl t1
		inner join tmpl_case t5
		on t1.tmpl_id = t5.tmpl_id
		left join tmpl_case_module t4
        on t5.tmpl_id = t4.tmpl_id
		left join tmpl_case_module_field t3
        on t4.tmpl_module_id = t3.tmpl_module_id
        left join deputy_tmpl_mapping t2
        on t3.tmpl_module_field_id = t2.tmpl_module_field_id
        and t1.deputy_tmpl_id = t2.deputy_tmpl_id
        where (t1.is_delete = '0' or t1.is_delete is null)
        and t1.deputy_tmpl_id = #{deputyTmplId}
    </select>

    <select id="tmplList" resultMap="tmplListMap">
        select
        t1.tmpl_id,
        t2.module_type as dtm_module_type,
        t2.module_title as dtm_module_title,
        t2.module_sn as dtm_module_sn,
        t2.table_name as dtm_table_name,
        t2.tmpl_module_id as dtm_tmpl_module_id,
        t2.allow_multiple_record as dtm_allow_multiple_record,
        t3.field_data_type as dtm_field_data_type,
        t3.field_values as dtm_field_values,
        t3.is_validated as dtm_is_validated,
        t3.is_require as dtm_is_require,
        t3.is_update as dtm_is_update,
        t3.validation as dtm_validation,
        t3.field_name as dtm_table_field_name,
        t3.to_case_ext_field as dtm_to_case_ext_field,
        t3.field_title as dtm_field_title,
        t3.field_sn as dtm_field_sn,
        t3.original_name as dtm_original_name,
        t3.component_relation as dtm_component_relation
        from tmpl_case t1
		left join tmpl_case_module t2
        on t1.tmpl_id = t2.tmpl_id
		left join tmpl_case_module_field t3
        on t2.tmpl_module_id = t3.tmpl_module_id
        where (t1.is_delete = '0' or t1.is_delete is null)
        and t2.tmpl_module_id = #{tmplModuleId}
    </select>
</mapper>