<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.tmpl.mapper.DeputyTmplMapper">

    <select id="deputyTmplList" resultType="com.sct.tiaojie.service.tmpl.dto.DeputyTmplDTO">
        select t1.*
        from
        (select
            t.*,
            t1.entrusts_id,
            t1.entrusts_name
            from deputy_tmpl t
            inner join tmpl_case t1
            on t.tmpl_id = t1.tmpl_id
            where t.tmpl_type = '2'
            and (t.is_delete = '0' or t.is_delete is null)
            and t1.is_delete = 0
        UNION
        select
            t.*,
            t1.entrusts_id,
            t1.entrusts_name
            from deputy_tmpl t
            inner join tmpl_case t1
            on t.tmpl_id = t1.tmpl_id
            where t.tmpl_type in ('3', '4')
            and (t.is_delete = '0' or t.is_delete is null)
        ) t1
            <where>
                <if test="param != null">
                    <if test="param.tmplType != null">
                        and t1.tmpl_type = #{param.tmplType}
                    </if>
                    <if test="param.entrustsId != null">
                        and t1.entrusts_id = #{param.entrustsId}
                    </if>
                    <if test="param.tmplStatus != null">
                        and t1.tmpl_status = #{param.tmplStatus}
                    </if>
                    <if test="param.tmplIds != null and !param.tmplIds.isEmpty">
                        AND t1.tmpl_id IN
                        <foreach collection="param.tmplIds" item="tmplId" open="(" close=")" separator=",">
                            #{tmplId}
                        </foreach>
                    </if>
                    <if test="param.deputyTmplIds != null and !param.deputyTmplIds.isEmpty">
                        AND t1.deputy_tmpl_id IN
                        <foreach collection="param.deputyTmplIds" item="deputyTmplId" open="(" close=")" separator=",">
                            #{deputyTmplId}
                        </foreach>
                    </if>
                </if>
            </where>
    </select>
</mapper>