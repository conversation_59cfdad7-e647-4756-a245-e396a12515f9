<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.tmpl.mapper.TmplCaseMapper">

    <select id="pageList" resultType="com.sct.tiaojie.service.tmpl.dto.TmplDTO">
        select * from (
            select
            t.deputy_tmpl_id as id,
            t1.tmpl_id,
            t.deputy_tmpl_id,
            t.tmpl_title,
            t.tmpl_type,
            t1.entrusts_name,
            t1.entrusts_id,
            t1.nature_title,
            t2.employee_name as updaterName,
            t.tmpl_status,
            t.update_time,
            t.tmpl_desc,
            t1.update_time as sort_time,
            t1.tmpl_id as sort_id,
            t1.is_delete as is_delete,
            t1.tmpl_title as baseTmplTitle
            from deputy_tmpl t
            inner join tmpl_case t1
            on t.tmpl_id = t1.tmpl_id
            left join auth_employee t2
            on t1.updater_id = t2.account_id
            where t.tmpl_type = '2'
            and (t.is_delete = '0' or t.is_delete is null)
            and t1.is_delete = 0

		    UNION

            select
            t.deputy_tmpl_id as id,
            t1.tmpl_id,
            t.deputy_tmpl_id,
            t.tmpl_title,
            t.tmpl_type,
            t1.entrusts_name,
            t1.entrusts_id,
            t1.nature_title,
            t2.employee_name as updaterName,
            t.tmpl_status,
            t.update_time,
            t.tmpl_desc,
            t1.update_time as sort_time,
            t1.tmpl_id as sort_id,
            t1.is_delete as is_delete,
            t1.tmpl_title as baseTmplTitle
            from deputy_tmpl t
            inner join tmpl_case t1
            on t.tmpl_id = t1.tmpl_id
            left join auth_employee t2
            on t1.updater_id = t2.account_id
            where t.tmpl_type in ('3', '4')
            and (t.is_delete = '0' or t.is_delete is null)

            UNION

            select
            t1.tmpl_id as id,
            t1.tmpl_id,
            NULL as deputy_tmpl_id,
            t1.tmpl_title,
            '1' as tmpl_type,
            t1.entrusts_name,
            t1.entrusts_id,
            t1.nature_title,
            t2.employee_name as updaterName,
            t1.tmpl_status,
            t1.update_time,
            t1.tmpl_desc,
            t1.update_time as sort_time,
            t1.tmpl_id + 1 as sort_id,
            t1.is_delete,
            NULL as baseTmplTitle
            from tmpl_case t1
            left join auth_employee t2
            on t1.updater_id = t2.account_id
            where t1.is_delete = 0
		) t
		<where>
            <if test="param != null">
                <if test="param.tmplTitle != null">
                    and t.tmpl_title like concat('%', #{param.tmplTitle}, '%')
                </if>
                <if test="param.entrustsName != null and param.entrustsName != ''">
                    and t.entrusts_name = #{param.entrustsName}
                </if>
                <if test="param.entrustsId != null and param.entrustsId != ''">
                    and t.entrusts_id = #{param.entrustsId}
                </if>
                <if test="param.tmplType != null and param.tmplType != ''">
                    and t.tmpl_type = #{param.tmplType}
                </if>
                <if test="param.natureTitle != null and param.natureTitle != ''">
                    and t.nature_title = #{param.natureTitle}
                </if>
                <if test="param.tmplStatus != null">
                    and t.tmpl_status = #{param.tmplStatus}
                </if>
                <if test="param.updaterName != null and param.updaterName != ''">
                    and t.updaterName like concat('%', #{param.updaterName}, '%')
                </if>
            </if>
        </where>
        ORDER BY t.sort_time desc, t.sort_id desc
    </select>
    <select id="list" resultType="com.sct.tiaojie.service.tmpl.dto.TmplDTO">
        select
        t1.tmpl_id,
        t1.tmpl_title,
        '1' as tmpl_type,
        t1.entrusts_id,
        t1.entrusts_name,
        t1.nature_title,
        t1.tmpl_status,
        t1.update_time,
        t1.tmpl_desc,
        t1.is_delete
        from tmpl_case t1
		<where>
            <if test="param != null">
                <if test="param.tmplName != null">
                    and t1.tmpl_name = #{param.tmplName}
                </if>
                <if test="param.isDelete != null">
                    and t1.is_delete = #{param.isDelete}
                </if>
                <if test="param.tmplStatus != null">
                    and t1.tmpl_status = #{param.tmplStatus}
                </if>
            </if>
        </where>
    </select>
</mapper>