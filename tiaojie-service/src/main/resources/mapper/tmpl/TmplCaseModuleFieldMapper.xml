<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sct.tiaojie.repository.tmpl.mapper.TmplCaseModuleFieldMapper">

    <resultMap id="listMap" type="com.sct.tiaojie.service.tmpl.dto.TmplDetail" autoMapping="true">
        <id column="tmpl_id" property="tmplId" />
        <collection property="allModules" javaType="java.util.ArrayList"
                    columnPrefix="tm_" resultMap="moduleMap">
        </collection>
    </resultMap>

    <resultMap id="moduleMap" type="com.sct.tiaojie.service.tmpl.dto.TmplModuleDetail" autoMapping="true">
        <id property="tmplModuleId" column="tmpl_module_id" />
        <collection property="moduleFields" autoMapping="true" javaType="java.util.ArrayList"
                    ofType="com.sct.tiaojie.service.tmpl.dto.TmplModuleFieldDTO"
                    columnPrefix="tmf_">
            <id property="tmplModuleFieldId" column="tmpl_module_field_id" />
            <result property="readOnlyRoles" column="read_only_roles" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        </collection>
    </resultMap>

    <select id="moduleFieldList" resultMap="listMap">
        select
        t1.tmpl_id,
        t1.tmpl_title,
        t1.tmpl_name,
        t1.entrusts_name,
        t1.entrusts_id,
        t1.nature_title,
        t1.tmpl_status,
        t1.update_time,
        t1.tmpl_desc,
        t1.create_time,
        t1.creator_id,
        t1.updater_id,
        t1.is_delete,
        t1.is_locked,
        t1.is_desensitize,
        t1.company_id,
        '1' as tmpl_type,
        t2.tmpl_module_id as tm_tmpl_module_id,
        t2.tmpl_id as tm_tmpl_id,
        t2.basis_module_id as tm_basis_module_id,
        t2.module_title as tm_module_title,
        t2.module_desc as tm_module_desc,
        t2.module_type as tm_module_type,
        t2.table_name as tm_table_name,
        t2.module_sn as tm_module_sn,
        t2.create_time as tm_create_time,
        t2.creator_id as tm_creator_id,
        t2.update_time as tm_update_time,
        t2.updater_id as tm_updater_id,
        t2.allow_multiple_record as tm_allow_multiple_record,
        t3.tmpl_module_field_id as tm_tmf_tmpl_module_field_id,
        t3.tmpl_id as tm_tmf_tmpl_id,
        t3.tmpl_module_id as tm_tmf_tmpl_module_id,
        t3.basis_module_field_id as tm_tmf_basis_module_field_id,
        t3.field_name as tm_tmf_field_name,
        t3.field_title as tm_tmf_field_title,
        t3.original_name as tm_tmf_original_name,
        t3.field_desc as tm_tmf_field_desc,
        t3.field_sn as tm_tmf_field_sn,
        t3.field_ui as tm_tmf_field_ui,
        t3.field_data_type as tm_tmf_field_data_type,
        t3.field_values as tm_tmf_field_values,
        t3.field_dict_type as tm_tmf_field_dict_type,
        t3.is_require as tm_tmf_is_require,
        t3.is_approval_validated as tm_tmf_is_approval_validated,
        t3.is_update as tm_tmf_is_update,
        t3.is_desensitize as tm_tmf_is_desensitize,
        t3.desensitize_rule as tm_tmf_desensitize_rule,
        t3.is_validated as tm_tmf_is_validated,
        t3.to_case_ext_field as tm_tmf_to_case_ext_field,
        t3.create_time as tm_tmf_create_time,
        t3.creator_id as tm_tmf_creator_id,
        t3.update_time as tm_tmf_update_time,
        t3.updater_id as tm_tmf_updater_id,
        t3.validation as tm_tmf_validation,
        t3.show_format as tm_tmf_show_format,
        t3.file_semantics as tm_tmf_file_semantics,
        t3.is_disable as tm_tmf_is_disable,
        t3.component_relation as tm_tmf_component_relation,
        t3.original_name as tm_tmf_original_name,
        t3.read_only_roles as tm_tmf_read_only_roles
        from tmpl_case t1
        left join tmpl_case_module t2
        on t1.tmpl_id = t2.tmpl_id
        left join tmpl_case_module_field t3
        on t2.tmpl_module_id = t3.tmpl_module_id
        <where>
            <if test="tmplId != null">
                and t1.tmpl_id = #{tmplId}
            </if>
        </where>
    </select>
</mapper>