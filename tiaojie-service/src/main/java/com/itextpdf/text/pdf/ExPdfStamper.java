package com.itextpdf.text.pdf;

import com.itextpdf.text.DocumentException;

import java.io.IOException;
import java.io.OutputStream;

public class ExPdfStamper extends PdfStamper {
    public static PdfStamper createSignature(final PdfReader reader, final OutputStream os, float opacity) throws DocumentException, IOException {
        ByteBuffer bout = new ByteBuffer();
        PdfStamper stp = new PdfStamper(reader, bout, '\0', true);
        stp.sigApp = new ExPdfSignatureAppearance(stp.stamper, opacity);
        stp.sigApp.setSigout(bout);
        stp.sigApp.setOriginalout(os);
        stp.sigApp.setStamper(stp);
        stp.hasSignature = true;
        PdfDictionary catalog = reader.getCatalog();
        PdfDictionary acroForm = (PdfDictionary) PdfReader.getPdfObject(catalog.get(PdfName.ACROFORM), catalog);
        if (acroForm != null) {
            acroForm.remove(PdfName.NEEDAPPEARANCES);
            stp.stamper.markUsed(acroForm);
        }
        return stp;
    }


    public static PdfStamper createSignature(final PdfReader reader, final OutputStream os) throws DocumentException, IOException {
        return createSignature(reader, os, 1.00f);
    }
}
