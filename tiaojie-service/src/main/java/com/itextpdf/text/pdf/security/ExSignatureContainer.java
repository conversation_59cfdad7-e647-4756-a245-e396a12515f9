package com.itextpdf.text.pdf.security;

import cfca.sadk.util.Base64;
import cfca.trustsign.common.vo.cs.HeadVO;
import cfca.trustsign.common.vo.cs.LocalSignVO;
import cfca.trustsign.common.vo.request.tx3.Tx3401ReqVO;
import cfca.trustsign.common.vo.response.ErrorResVO;
import cfca.trustsign.common.vo.response.tx3.Tx3401ResVO;
import com.itextpdf.text.pdf.PdfDictionary;
import com.itextpdf.text.pdf.PdfName;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.service.sign.connector.HttpConnector;
import com.sct.tiaojie.service.sign.converter.JsonObjectMapper;
import com.sct.tiaojie.service.sign.util.SecurityUtil;
import com.sct.tiaojie.service.sign.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;

@Slf4j
public class ExSignatureContainer implements ExternalSignatureContainer {
    private static JsonObjectMapper jsonObjectMapper = new JsonObjectMapper();

    private LocalSignVO localSign;
    private HttpConnector connector;

    private String hashAlgorithm = DigestAlgorithms.SHA256;
    private ExternalDigest digest = new BouncyCastleDigest();
    private MakeSignature.CryptoStandard signType = MakeSignature.CryptoStandard.CMS;

    public ExSignatureContainer(HttpConnector connector, LocalSignVO localSign) {
        this.connector = connector;
        this.localSign = localSign;
    }

    @Override
    public byte[] sign(InputStream data) throws GeneralSecurityException {
        byte hash[];
        try {
            hash = DigestAlgorithms.digest(data, digest.getMessageDigest(hashAlgorithm));
        } catch (IOException e) {
            throw new GeneralSecurityException(e);
        }

        String signatureAttr = Base64.toBase64String(hash);
        localSign.setSignatureAttr(signatureAttr);

        HeadVO head = new HeadVO();
        head.setTxTime(TimeUtil.getCurrentTime(TimeUtil.FORMAT_14));

        Tx3401ReqVO reqVO = new Tx3401ReqVO();
        reqVO.setHead(head);
        reqVO.setLocalSign(localSign);

        String request = jsonObjectMapper.writeValueAsString(reqVO);

        String response;
        try {
            String signature = SecurityUtil.p7SignMessageDetach(connector.getPrivateKey(), connector.getCertificate(), request);
            response = connector.post("platId/" + connector.getPlatId() + "/txCode/3401/transaction", request, signature);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GeneralSecurityException(e);
        }

        handleIfError(response);

        Tx3401ResVO resVO = jsonObjectMapper.readValue(response, Tx3401ResVO.class);

        if (resVO != null && resVO.getLocalSign() != null) {
            String signatureOfAttr = resVO.getLocalSign().getSignatureOfAttr();
            return Base64.decode(signatureOfAttr);
        } else {
            throw new GeneralSecurityException(response);
        }
    }

    private void handleIfError(String res) {
        JsonObjectMapper jsonObjectMapper = new JsonObjectMapper();
        ErrorResVO errorResVO = jsonObjectMapper.readValue(res, ErrorResVO.class);
        if(errorResVO != null && errorResVO.getErrorCode() != null){
            throw new ValidateBusinessException("签名失败：" + errorResVO.getErrorMessage());
        }
    }


    @Override
    public void modifySigningDictionary(PdfDictionary signDic) {
        signDic.put(PdfName.FILTER, PdfName.ADOBE_PPKLITE);
        if (this.signType == MakeSignature.CryptoStandard.CADES) {
            signDic.put(PdfName.SUBFILTER, PdfName.ETSI_CADES_DETACHED);
        } else {
            signDic.put(PdfName.SUBFILTER, PdfName.ADBE_PKCS7_DETACHED);
        }
    }

    public MakeSignature.CryptoStandard getSignType() {
        return signType;
    }

    public void setSignType(MakeSignature.CryptoStandard signType) {
        this.signType = signType;
    }
}
