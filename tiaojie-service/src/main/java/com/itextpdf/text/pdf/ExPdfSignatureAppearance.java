package com.itextpdf.text.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.error_messages.MessageLocalization;
import com.itextpdf.text.pdf.security.CertificateInfo;

import java.security.cert.Certificate;
import java.security.cert.X509Certificate;

public class ExPdfSignatureAppearance extends PdfSignatureAppearance {
    protected static final float TOP_SECTION = 0.3f;
    protected static final float MARGIN = 2;

    private float opacity = 1.00f;

    ExPdfSignatureAppearance(PdfStamperImp writer, float opacity) {
        super(writer);
        setOpacity(opacity);
    }

    ExPdfSignatureAppearance(PdfStamperImp writer) {
        this(writer, 1.00f);
    }

    @Override
    public PdfTemplate getAppearance() throws DocumentException {
        PdfTemplate app2 = getLayer(2);

        PdfGState gs = new PdfGState();
        gs.setBlendMode(PdfGState.BM_MULTIPLY);
        gs.put(PdfName.TYPE, PdfName.EXTGSTATE);
        gs.setFillOpacity(opacity);
        app2.setGState(gs);


        String text = "";
        if (getLayer2Text() != null) {
            text = getLayer2Text();
        }

        Font font = new Font();
        float size = font.getSize();

        Rectangle dataRect = null;
        Rectangle signatureRect = null;

        Rectangle rect = getRect();
        int runDirection = getRunDirection();
        Certificate signCertificate = getCertificate();
        Image signatureGraphic = getSignatureGraphic();
        RenderingMode renderingMode = getRenderingMode();


        if (renderingMode == RenderingMode.NAME_AND_DESCRIPTION
                || renderingMode == RenderingMode.GRAPHIC_AND_DESCRIPTION && signatureGraphic != null) {
            // origin is the bottom-left
            signatureRect = new Rectangle(
                    MARGIN,
                    MARGIN,
                    rect.getWidth() / 2 - MARGIN,
                    rect.getHeight() - MARGIN);
            dataRect = new Rectangle(
                    rect.getWidth() / 2 + MARGIN / 2,
                    MARGIN,
                    rect.getWidth() - MARGIN / 2,
                    rect.getHeight() - MARGIN);

            if (rect.getHeight() > rect.getWidth()) {
                signatureRect = new Rectangle(
                        MARGIN,
                        rect.getHeight() / 2,
                        rect.getWidth() - MARGIN,
                        rect.getHeight());
                dataRect = new Rectangle(
                        MARGIN,
                        MARGIN,
                        rect.getWidth() - MARGIN,
                        rect.getHeight() / 2 - MARGIN);
            }
        } else if (renderingMode == RenderingMode.GRAPHIC) {
            if (signatureGraphic == null) {
                throw new IllegalStateException(MessageLocalization.getComposedMessage("a.signature.image.should.be.present.when.rendering.mode.is.graphic.only"));
            }
            signatureRect = new Rectangle(
                    MARGIN,
                    MARGIN,
                    rect.getWidth() - MARGIN, // take all space available
                    rect.getHeight() - MARGIN);
        } else {
            dataRect = new Rectangle(
                    MARGIN,
                    MARGIN,
                    rect.getWidth() - MARGIN,
                    rect.getHeight() * (1 - TOP_SECTION) - MARGIN);
        }

        switch (renderingMode) {
            case NAME_AND_DESCRIPTION:
                String signedBy = CertificateInfo.getSubjectFields((X509Certificate) signCertificate).getField("CN");
                if (signedBy == null) {
                    signedBy = CertificateInfo.getSubjectFields((X509Certificate) signCertificate).getField("E");
                }
                if (signedBy == null) {
                    signedBy = "";
                }
                Rectangle sr2 = new Rectangle(signatureRect.getWidth() - MARGIN, signatureRect.getHeight() - MARGIN);
                float signedSize = ColumnText.fitText(font, signedBy, sr2, -1, runDirection);

                ColumnText ct2 = new ColumnText(app2);
                ct2.setRunDirection(runDirection);
                ct2.setSimpleColumn(new Phrase(signedBy, font), signatureRect.getLeft(), signatureRect.getBottom(), signatureRect.getRight(), signatureRect.getTop(), signedSize, Element.ALIGN_LEFT);

                ct2.go();
                break;
            case GRAPHIC_AND_DESCRIPTION:
                if (signatureGraphic == null) {
                    throw new IllegalStateException(MessageLocalization.getComposedMessage("a.signature.image.should.be.present.when.rendering.mode.is.graphic.and.description"));
                }
                ct2 = new ColumnText(app2);
                ct2.setRunDirection(runDirection);
                ct2.setSimpleColumn(signatureRect.getLeft(), signatureRect.getBottom(), signatureRect.getRight(), signatureRect.getTop(), 0, Element.ALIGN_RIGHT);

                Image im = Image.getInstance(signatureGraphic);
                im.scaleToFit(signatureRect.getWidth(), signatureRect.getHeight());

                Paragraph p = new Paragraph();
                // must calculate the point to draw from to make image appear in middle of column
                float x = 0;
                // experimentation found this magic number to counteract Adobe's signature graphic, which
                // offsets the y co-ordinate by 15 units
                float y = -im.getScaledHeight() + 15;

                x = x + (signatureRect.getWidth() - im.getScaledWidth()) / 2;
                y = y - (signatureRect.getHeight() - im.getScaledHeight()) / 2;
                p.add(new Chunk(im, x + (signatureRect.getWidth() - im.getScaledWidth()) / 2, y, false));
                ct2.addElement(p);
                ct2.go();
                break;
            case GRAPHIC:
                ct2 = new ColumnText(app2);
                ct2.setRunDirection(runDirection);
                ct2.setSimpleColumn(signatureRect.getLeft(), signatureRect.getBottom(), signatureRect.getRight(), signatureRect.getTop(), 0, Element.ALIGN_RIGHT);

                im = Image.getInstance(signatureGraphic);
                im.scaleToFit(signatureRect.getWidth(), signatureRect.getHeight());

                p = new Paragraph(signatureRect.getHeight());
                // must calculate the point to draw from to make image appear in middle of column
                x = (signatureRect.getWidth() - im.getScaledWidth()) / 2;
                y = (signatureRect.getHeight() - im.getScaledHeight()) / 2;
                p.add(new Chunk(im, x, y, false));
                ct2.addElement(p);
                ct2.go();
                break;
            default:
                break;
        }

        if (renderingMode != RenderingMode.GRAPHIC) {
            if (size <= 0) {
                Rectangle sr = new Rectangle(dataRect.getWidth(), dataRect.getHeight());
                size = ColumnText.fitText(font, text, sr, 12, runDirection);
            }
            ColumnText ct = new ColumnText(app2);
            ct.setRunDirection(runDirection);
            ct.setSimpleColumn(new Phrase(text, font), dataRect.getLeft(), dataRect.getBottom(), dataRect.getRight(), dataRect.getTop(), size, Element.ALIGN_LEFT);
            ct.go();
        }

        return super.getAppearance();
    }

    public float getOpacity() {
        return opacity;
    }

    public void setOpacity(float opacity) {
        if (opacity > 0 && opacity <= 1) {
            this.opacity = opacity;
        }
    }
}
