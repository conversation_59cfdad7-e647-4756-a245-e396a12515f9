package com.sct.tiaojie.repository.task.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.sct.tiaojie.repository.task.entity.TaskInstance;
import com.sct.tiaojie.repository.dto.TaskInstanceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TaskInstanceMapper extends BaseMapper<TaskInstance> {

    /**
     * 查询任务实例列表，关联账号表获取姓名信息
     * @param queryWrapper 查询条件
     * @return 任务实例列表（包含姓名信息）
     */
    List<TaskInstanceDTO> selectDTOList(@Param(Constants.WRAPPER) Wrapper<TaskInstance> queryWrapper);

    /**
     * 根据ID查询单个任务实例，关联账号表获取姓名信息
     * @param queryWrapper 查询条件
     * @return 任务实例详情（包含姓名信息）
     */
    TaskInstanceDTO selectOneDTO(@Param(Constants.WRAPPER) Wrapper<TaskInstance> queryWrapper);
}
