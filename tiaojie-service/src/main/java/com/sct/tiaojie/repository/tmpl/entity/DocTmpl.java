package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel("文书模板表")
@TableName("document_template")
public class DocTmpl {

    @TableId(value = "doc_tmpl_id", type = IdType.AUTO)
    @ApiModelProperty(value = "文书模板ID")
    private Long docTmplId;

    @ApiModelProperty("所属案件模板ID")
    private Integer tmplId;

    @ApiModelProperty("文书模板名称")
    private String docTmplTitle;

    @ApiModelProperty("制作方式")
    private String docTmplType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("模板状态")
    private Integer tmplStatus;

    @ApiModelProperty("文书模板路径")
    private String docPath;

    @ApiModelProperty("删除标识")
    private String isDelete;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改者")
    private Long updaterId;
}
