package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 部门 实体
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "部门实体")
public class AuthDept implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "部门ID", example = "1")
    private Long deptId;

    @ApiModelProperty(value = "部门名称,最大长度为50")
    @Length(min = 0, max = 50, message = "部门名称格式错误,最大长度为50")
    private String deptName;

    @ApiModelProperty(value = "父部门", example = "1")
    private Long parentDeptId;

    @ApiModelProperty(value = "级别", example = "1")
    private Integer treeLevel;

    @ApiModelProperty(value = "公司ID", example = "1")
    private Long companyId;

    @ApiModelProperty(value = "部门状态", example = "1")
    private Integer deptStatus;

    @ApiModelProperty(value = "备注", example = "1")
    private String remark;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

}