package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/***********************************************************************************************************************
 * <p>
 *      序列  实体
 * </P>
 * @copyright 2020 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2020-12-16
 * @version V1.0
 **********************************************************************************************************************/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("sys_sequence")
@Data
public class SysSequence {

    /**
     * 序列ID
     */
    @TableId(value = "sequence_id", type = IdType.ASSIGN_ID)
    private Long sequenceId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    /**
     * 序列类型
     */
    private String sequenceType;

    /**
     * 序列标题
     */
    private String sequenceTitle;

    /**
     * 数字长度
     */
    private Integer digitalLength;

    /**
     * 序列域
     */
    private String sequenceScope;

    /**
     * 序列开始数字
     */
    private Integer sequenceStart;

    /**
     * 序列结束数字
     */
    private Integer sequenceEnd;

    /**
     * 序列当前值
     */
    private Integer sequenceCurrent;

    private LocalDateTime updateTime;

    private Long updaterId;

    private LocalDateTime createTime;

    private Long creatorId;
}
