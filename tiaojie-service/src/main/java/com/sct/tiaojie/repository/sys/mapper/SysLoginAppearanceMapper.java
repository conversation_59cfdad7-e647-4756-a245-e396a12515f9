package com.sct.tiaojie.repository.sys.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysLoginAppearance;
import com.sct.tiaojie.service.sys.bo.SysLoginAppearanceBO;
import com.sct.tiaojie.service.sys.dto.SysLoginAppearanceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 登录页自定义外观;(sys_login_appearance)表数据库访问层
 *
 * <AUTHOR> llj
 * @date : 2024-7-24
 */
@Mapper
public interface SysLoginAppearanceMapper extends BaseMapper<SysLoginAppearance> {
    /**
     * 修改登录页外观数据
     *
     * @param sysLoginAppearance 修改参数
     */
    int update(SysLoginAppearance sysLoginAppearance);

    /**
     * 分页查询
     *
     * @param page  分页常用参数
     * @param param 分页查询参数
     */
    Page<SysLoginAppearanceDTO> pageList(@Param("page") Page<SysLoginAppearanceDTO> page, @Param("param") SysLoginAppearanceBO param);
}
