package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtCaseApproval;
import com.sct.tiaojie.repository.mdt.entity.MdtCaseDistributeRecord;
import com.sct.tiaojie.service.mdt.bo.MdtCaseApprovalPageBO;
import com.sct.tiaojie.service.mdt.dto.MdtCaseApprovalPageDTO;
import com.sct.tiaojie.service.sys.bo.SysEntrustsPageBO;
import com.sct.tiaojie.service.sys.dto.SysEntrustsPageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 案件呈批记录Mapper
 * <AUTHOR>
 * @Date 2024/5/31
 * @Version 1.0
 */
@Mapper
public interface MdtCaseApprovalMapper extends BaseMapper<MdtCaseApproval> {

    /**
     * 分页查询审批
     * @param page
     * @param param
     * @return
     */
    Page<MdtCaseApprovalPageDTO> pageList(Page<MdtCaseApprovalPageDTO> page, @Param("param") MdtCaseApprovalPageBO param);

    /**
     * 查询可见呈批
     * @param paramBO
     * @return
     */
    List<MdtCaseApproval> getMdtApproval(@Param("param") MdtCaseApprovalPageBO paramBO);
}
