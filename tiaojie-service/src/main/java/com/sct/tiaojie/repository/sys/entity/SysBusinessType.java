package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

@TableName(value = "sys_business_type",autoResultMap = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysBusinessType {
    /** 业务类型id */
    @TableId(type = IdType.AUTO)
    private Long businessTypeId;

    /** 业务类型名称 */
    private String businessTypeName;

    /** 描述 */
    private String description;

    /**
     *  办结流程
     *  @com.sct.tiaojie.common.enums.ProcessTypeEnusm
     * */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> processTypeList; // JSON type in DB, can be String or a custom object if you have a parser

    /** 业务状态 */
    private Integer status;

    /** 父级id */
    private Long parentId;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 创建人 */
    private Long creatorId;

    /** 更新时间 */
    private LocalDateTime updateTime;

    /** 更新人 */
    private Long updaterId;
}
