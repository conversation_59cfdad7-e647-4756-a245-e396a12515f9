package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysSequence;
import com.sct.tiaojie.service.sys.dto.SysSequenceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * <p>
 *      序列      Repository
 * </p>
 * @copyright 2019 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2020-12-16
 * @version V1.0
 **********************************************************************************************************************/
@Mapper
public interface SysSequenceMapper extends BaseMapper<SysSequence> {

    List<SysSequenceDTO> getCaseNoSequence(@Param("companyId") Long companyId);
}
