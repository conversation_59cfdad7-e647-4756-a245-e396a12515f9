package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysLog;
import com.sct.tiaojie.service.auth.bo.OperateLogPageBO;
import com.sct.tiaojie.service.auth.dto.OperateLogPageDTO;
import com.sct.tiaojie.service.call.dto.CallRecordPageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SysLogMapper extends BaseMapper<SysLog> {
    /**
     * 分页查询操作日志
     * @param page
     * @param param
     * @return
     */
    Page<OperateLogPageDTO> pageList(Page<OperateLogPageDTO> page, @Param("param") OperateLogPageBO param);

}
