package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("副模板字段映射表")
@TableName("deputy_tmpl_mapping")
public class DeputyTmplMapping {

    @TableId(value = "mapping_id", type = IdType.AUTO)
    @ApiModelProperty("映射ID")
    private Integer mappingId;

    @ApiModelProperty("副模板ID")
    private Integer deputyTmplId;

    @ApiModelProperty("字段名称")
    private String fieldName;

    @ApiModelProperty("系统字段ID")
    private String tmplModuleFieldId;

    @ApiModelProperty("字段序号")
    private Integer fieldSn;

    @ApiModelProperty("映射转换器")
    private String converter;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改者")
    private Long updaterId;
}
