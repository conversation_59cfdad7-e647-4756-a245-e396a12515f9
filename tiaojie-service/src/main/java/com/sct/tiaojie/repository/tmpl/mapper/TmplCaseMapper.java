package com.sct.tiaojie.repository.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.page.dto.PageDTO;
import com.sct.tiaojie.repository.tmpl.entity.TmplCase;
import com.sct.tiaojie.service.tmpl.bo.TmplBO;
import com.sct.tiaojie.service.tmpl.dto.TmplDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TmplCaseMapper extends BaseMapper<TmplCase> {

    /**
     * 分页查询模板列表
     * @param page
     * @param param
     * @return
     */
    Page<TmplDTO> pageList(Page<TmplDTO> page, @Param("param") TmplBO param);

    /**
     * 查询最新版本主模板列表
     * @param bo
     * @return
     */
    List<TmplDTO> list(@Param("param") TmplBO bo);
}
