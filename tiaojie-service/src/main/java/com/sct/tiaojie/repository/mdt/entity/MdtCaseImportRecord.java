package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @author: yiliang
* @date: 2024/9/2 10:33
* description: 案件导入记录实体
*/
@Data
@ApiModel(value = "案件导入记录实体")
@TableName(value = "mdt_case_import_record", autoResultMap = true)
@NoArgsConstructor
public class MdtCaseImportRecord implements Serializable {

    @ApiModelProperty(value = "案件导入ID")
    @TableId(value = "case_import_id", type = IdType.ASSIGN_ID)
    private Long caseImportId;


    @ApiModelProperty(value = "委托方ID（案源方）")
    private Long entrustsId;

    @ApiModelProperty(value = "委托方部门id")
    private Long entrustsDeptId;

    @ApiModelProperty(value = "导入模板ID")
    private Long deputyTmplId;

    @ApiModelProperty("是否办结审核 0否，1是")
    private Integer completeReview;

    @ApiModelProperty("案件调解开始时间")
    private LocalDateTime mediateBeginTime;

    @ApiModelProperty("案件调解到期时间")
    private LocalDateTime mediateEndTime;

    @ApiModelProperty("时限设置方式(1:模板导入;2:页面设置)")
    private Integer dateConfigType;

    @ApiModelProperty("操作类型(1:新增；2:更新)")
    private Integer impFlag;

    @ApiModelProperty("导入人ID")
    private Long importAccountId;

    @ApiModelProperty("源上传excel文件名")
    private String importFileName;

    @ApiModelProperty("源上传excel文件路径")
    private String importFilePath;

    @ApiModelProperty("错误内容标记的excel路径")
    private String errorMarkFilePath;

    @ApiModelProperty("全部内容标记的excel路径")
    private String totalMarkFilePath;

    @ApiModelProperty("总案件数")
    private Integer totalCases;

    @ApiModelProperty("导入失败的案件数")
    private Integer failedCases;

    @ApiModelProperty("导入成功的案件数")
    private Integer successCases;


    @ApiModelProperty("导入状态 -1内部异常 1正在导入 2成功 3部分成功 4失败")
    private Integer importStatus;

    @ApiModelProperty("忽略错误并继续导入")
    private Boolean isIgnoreError;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "调解类型")
    private String caseMediateType;

    @ApiModelProperty("业务类型")
    private Long businessType;

    @ApiModelProperty("是否脱敏")
    private Boolean isDesensitize;

}