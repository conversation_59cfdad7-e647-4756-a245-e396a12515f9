package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 消息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR> Panda
 * @date          2020-06-12
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "消息实体")
public class SysMsg implements Serializable {

    @ApiModelProperty(value = "消息ID", example = "1")
    @TableId(value = "msg_id", type = IdType.ASSIGN_ID)
    private Long msgId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "消息模块", example = "1")
    private Integer msgModule;

    @ApiModelProperty(value = "消息类型", example = "1")
    private Integer msgType;

    @ApiModelProperty(value = "消息标题,最大长度为100")
    @Length(min = 0, max = 100, message = "消息标题格式错误,最大长度为100")
    private String msgTitle;

    @ApiModelProperty(value = "消息内容,最大长度为500")
    @Length(min = 0, max = 500, message = "消息内容格式错误,最大长度为500")
    private String msgContent;

    @ApiModelProperty(value = "业务ID", example = "1")
    private Long bizId;

    @ApiModelProperty(value = "额外参数,最大长度为1000000000")
    @Length(min = 0, max = 10000000, message = "额外参数格式错误,最大长度为1000000000")
    private String othParam;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTm;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "最近修改时间")
    private LocalDateTime updateTm;

    @ApiModelProperty(value = "最近修改用户", example = "1")
    private Long updaterId;

}