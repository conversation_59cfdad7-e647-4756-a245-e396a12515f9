package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("sys_log_case")
@ApiModel("系统日志关联案件信息表")
public class SysLogCase {

    @TableId(value = "log_case_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "案件日志ID")
    private Long logCaseId;

    @ApiModelProperty(value = "日志ID")
    private Long logId;

    @ApiModelProperty(value = "日志参数")
    private String logParam;

    @ApiModelProperty(value = "案件ID")
    private Long caseId;

    @ApiModelProperty(value = "案件号")
    private String caseNo;

    @ApiModelProperty(value = "委托方ID（案源方）")
    private Long entrustsId;

    @ApiModelProperty(value = "委托方名称（案源方）")
    private String entrustsName;

    @ApiModelProperty(value = "委托方部门id")
    private Long entrustsDeptId;

    @ApiModelProperty(value = "案源方部门名称")
    private String entrustsDeptName;
}
