package com.sct.tiaojie.repository.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "流程信息实体")
@TableName(value = "task_workflow") // 修正为实际表名
@NoArgsConstructor
public class TaskWorkFlow implements Serializable {

    @ApiModelProperty(value = "流程ID", example = "1")
    @TableId(value = "workflow_id", type = IdType.AUTO) // 匹配主键字段
    private Long workflowId;

    @ApiModelProperty(value = "流程名称", example = "标准调解流程")
    @Length(min = 0, max = 100, message = "流程名称格式错误,最大长度为100")
    @TableField("workflow_name")
    private String workflowName;

    @ApiModelProperty(value = "案源方ID", example = "10001")
    @TableField("entrusts_id")
    private Long entrustsId;

    @ApiModelProperty(value = "案源方名称", example = "10001")
    @TableField("entrusts_name")
    private String entrustsName;

    @ApiModelProperty(value = "案件信息模板id", example = "2001")
    @TableField("template_id")
    private Long templateId;

    @ApiModelProperty(value = "案件信息模板名称", example = "2001")
    @TableField("template_name")
    private String templateName;

    @ApiModelProperty(value = "状态(0:禁用 1:启用)")
    private Boolean status;

    @ApiModelProperty(value = "触发条件(1:案件导入 2:案件分派)")
    @TableField("trigger_condition")
    private Integer triggerCondition;

    @ApiModelProperty(value = "备注", example = "特殊流程需审批")
    @Length(min = 0, max = 500, message = "备注格式错误,最大长度为500")
    private String remark;

    @ApiModelProperty(value = "创建人ID", example = "1001")
    @TableField("creator_id")
    private Long creatorId;

    @ApiModelProperty(value = "更新人ID", example = "1002")
    @TableField("updater_id")
    private Long updaterId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;


    @ApiModelProperty(value = "创建人名称")
    @TableField(exist = false)
    private String creatorName;

    // 生成 creatorName和updaterName,这两个字段在数据库中不存在
    @ApiModelProperty(value = "更新人名称")
    @TableField(exist = false)
    private String updaterName;


}