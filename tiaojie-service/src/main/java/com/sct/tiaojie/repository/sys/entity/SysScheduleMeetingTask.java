package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SysScheduleMeetingTask {

    @TableId(value = "meeting_id")
    private String meetingId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    private String meetingNumber;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Long creatorId;

}
