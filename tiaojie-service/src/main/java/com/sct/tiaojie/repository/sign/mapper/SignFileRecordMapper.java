package com.sct.tiaojie.repository.sign.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sign.entity.SignFileRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @description 文件签名记录信息Repository
 * <AUTHOR>
 */
@Mapper
public interface SignFileRecordMapper extends BaseMapper<SignFileRecord> {

  @Select("SELECT * FROM sign_file_record WHERE file_sign_info_id = #{fileSignInfoId} AND sign_man_biz_id = #{signManBizId} LIMIT 1")
  SignFileRecord findFirstByFileSignInfoIdAndSignManName(@Param("fileSignInfoId") Long fileSignInfoId, @Param("signManBizId") Long signManBizId);

}
        