package com.sct.tiaojie.repository.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.page.dto.PageDTO;
import com.sct.tiaojie.repository.tmpl.entity.DocTmpl;
import com.sct.tiaojie.service.tmpl.bo.DocTmplBO;
import com.sct.tiaojie.service.tmpl.dto.DocTmplDTO;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

@Mapper
public interface DocTmplMapper extends BaseMapper<DocTmpl> {
    Page<DocTmplDTO> pageList(Page<DocTmplDTO> page, @Param("param") DocTmplBO param);
}
