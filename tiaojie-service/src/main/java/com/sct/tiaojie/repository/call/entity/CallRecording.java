package com.sct.tiaojie.repository.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sct.tiaojie.repository.mdt.entity.AIHandle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("call_recording")
@ApiModel("外呼录音实体")
public class CallRecording extends AIHandle {

    @TableId(value = "recording_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "录音ID")
    private Long recordingId;

    @ApiModelProperty(value = "记录ID")
    private Long recordId;

    @ApiModelProperty(value = "坐席ID")
    private String agentId;

    @ApiModelProperty(value = "录音文件名")
    private String recordName;

    @ApiModelProperty(value = "通话时长")
    private String timeLength;

    @ApiModelProperty(value = "录音标识")
    private String voiceSign;

    @ApiModelProperty(value = "费用")
    private BigDecimal fee;

    @ApiModelProperty(value = "录音文件名路径")
    private String filename;

    @ApiModelProperty(value = "呼叫结果")
    private String result;

    @ApiModelProperty(value = "地区")
    private String area;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "运营商名称")
    private String spName;

    @ApiModelProperty(value = "录音本地路径")
    private String filePath;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;
}
