package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("模板模块字段表")
@TableName(value = "tmpl_case_module_field", autoResultMap = true)
public class TmplCaseModuleField {

    @TableId(value = "tmpl_module_field_id", type = IdType.AUTO)
    @ApiModelProperty("模板模块字段ID")
    private Integer tmplModuleFieldId;

    @ApiModelProperty("模板ID")
    private Integer tmplId;

    @ApiModelProperty("模板模块ID")
    private Integer tmplModuleId;

    @ApiModelProperty("基础模块字段ID")
    private Integer basisModuleFieldId;

    @ApiModelProperty("对应数据表字段名")
    private String fieldName;

    @ApiModelProperty("字段标题")
    private String fieldTitle;

    @ApiModelProperty("字段描述")
    private String fieldDesc;

    @ApiModelProperty("字段序号")
    private Integer fieldSn;

    @ApiModelProperty("字段ui")
    private String fieldUi;

    @ApiModelProperty("字段数据类型")
    private String fieldDataType;

    @ApiModelProperty("字段取值")
    private String fieldValues;

    @ApiModelProperty("系统字典Type")
    private String fieldDictType;


    @ApiModelProperty("验证格式，规则")
    private String validation;

    @ApiModelProperty("呈现格式")
    private String showFormat;

    @ApiModelProperty("是否必须")
    private Integer isRequire;

    @ApiModelProperty("是否结案批准校验")
    private Integer isApprovalValidated;

    @ApiModelProperty("是否可修改")
    private Integer isUpdate;

    @ApiModelProperty("是否失效")
    private Integer isDisable;

    @ApiModelProperty("是否脱敏")
    private Integer isDesensitize;

    @ApiModelProperty("原始的字段名")
    private String originalName;

    @ApiModelProperty("脱敏规则")
    private String desensitizeRule;

    @ApiModelProperty("字段语义")
    private String fileSemantics;

    @ApiModelProperty("是否校验数据格式")
    private Integer isValidated;

    @ApiModelProperty("扩展字段名")
    private String toCaseExtField;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改者")
    private Long updaterId;

    @ApiModelProperty("组件关联")
    private String componentRelation;

    @ApiModelProperty("只读权限角色ID列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> readOnlyRoles;
}
