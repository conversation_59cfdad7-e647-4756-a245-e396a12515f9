package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 任务运行日志 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-18
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "任务运行日志实体")
public class SysTaskRunLog implements Serializable {

    @ApiModelProperty(value = "日志ID", example = "1")
    @TableId(type = IdType.ASSIGN_ID)
    private Long logId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "业务ID", example = "1")
    private Long bizId;

    @ApiModelProperty(value = "任务状态(1:未运行,2:运行中,3:执行成功,4:执行失败)", example = "1")
    private Integer taskStatus;

    @ApiModelProperty(value = "任务类型", example = "1")
    private Integer taskType;

    @ApiModelProperty(value = "任务描述,最大长度为200")
    @Length(min = 0, max = 200, message = "任务描述格式错误,最大长度为200")
    private String taskDesc;

    @ApiModelProperty(value = "计划执行时间")
    private LocalDateTime planExecTime;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "执行结果,最大长度为1000")
    @Length(min = 0, max = 1000, message = "执行结果格式错误,最大长度为1000")
    private String execResult;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "导入的文件路径")
    private String filePath;

}