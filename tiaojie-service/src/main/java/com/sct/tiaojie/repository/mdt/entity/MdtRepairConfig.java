package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel(value = "失联修复配置表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mdt_repair_config")
public class MdtRepairConfig {

    @ApiModelProperty(value = "ID")
    @TableId(value = "repair_config_id",type = IdType.ASSIGN_ID)
    private Long repairConfigId;

    @ApiModelProperty("组织Id")
    private Long companyId;

    @ApiModelProperty("配置信息")
    private String orderConfig;

    @ApiModelProperty("修复状态 1:单次修复 2：批量修复")
    private String orderStatus;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
}
