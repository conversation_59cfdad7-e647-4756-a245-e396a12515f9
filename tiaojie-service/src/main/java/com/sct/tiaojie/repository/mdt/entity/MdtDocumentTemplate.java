package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sct.tiaojie.common.contants.DocumentParamType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/4/21 11:09
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mdt_document_template")
public class MdtDocumentTemplate {
    @ApiModelProperty(value = "类型模板ID")
    @TableId(value = "doc_template_id",type = IdType.ASSIGN_ID)
    private Long docTemplateId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "文书ID")
    private Long documentId;

    @ApiModelProperty(value = "别名")
    private String mapName;

    @ApiModelProperty(value = "类型码")
    private String productCode;

    @ApiModelProperty(value = "案件类型ID")
    private String intelligentCaseTypeId;

    @ApiModelProperty(value = "文书附件路径")
    private String filePath;


    @ApiModelProperty(value = "签章方式   生成文书后自动签章")
    private String sealWay;

    @ApiModelProperty(value = "盖章类型")
    private String stampType;

    @ApiModelProperty(value = "盖章个数  一个或多个")
    private String stampCount;

    @ApiModelProperty(value = "状态  1启用 0禁用")
    private Boolean status;
    @ApiModelProperty(value = "创建人ID")
    private Long createManId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人ID")
    private Long updateManId;

    /**
     * List<List<String>>
     * 1 参数名称
     * 2 参数code，获取参数值
     * 3 参数类型， {@link DocumentParamType}
     * 4 额外参数 备用
     */
    @ApiModelProperty(value = "替换参数")
    private String param;

    @ApiModelProperty(value = "签名关键字")
    private String keyword;
}
