package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/***********************************************************************************************************************
 * @copyright 2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2022-06-14
 **********************************************************************************************************************/
@Data
@ApiModel("系统异常请求记录实体")
public class SysExceptionLog implements Serializable {

    @ApiModelProperty(value = "日志ID")
    @TableId(value = "exception_log_id", type = IdType.ASSIGN_ID)
    private Long exceptionLogId;

    @ApiModelProperty(value = "请求URL")
    private String requestUrl;

    @ApiModelProperty(value = "请求方法")
    private String requestMethod;

    @ApiModelProperty(value = "请求参数")
    private String requestParam;

    @ApiModelProperty(value = "异常信息")
    private String exceptionInfo;

    @ApiModelProperty(value = "用户ID")
    private Long accountId;

    @ApiModelProperty(value = "用户登陆名")
    private String loginName;

    @ApiModelProperty(value = "请求IP")
    private String requestIp;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private Long creatorId;
}
