package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 员工 实体
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "员工实体")
@TableName("auth_employee")
public class AuthEmployee implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "员工ID", example = "1")
    private Long employeeId;

    @ApiModelProperty(value = "帐号ID", example = "1")
    private Long accountId;

    @ApiModelProperty(value = "员工名称,最大长度为50")
    @Length(min = 0, max = 50, message = "员工名称格式错误,最大长度为50")
    private String employeeName;

    @ApiModelProperty(value = "员工编号,最大长度为50")
    @Length(min = 0, max = 50, message = "员工编号格式错误,最大长度为50")
    private String employeeNumber;

    @ApiModelProperty(value = "员工手机,最大长度为50")
    @Length(min = 0, max = 50, message = "员工手机格式错误,最大长度为50")
    private String employeeMobile;

    @ApiModelProperty(value = "员工邮箱,最大长度为50")
    @Length(min = 0, max = 50, message = "员工邮箱格式错误,最大长度为50")
    private String employeeEmail;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "员工地址")
    private String employeeAddress;

    @ApiModelProperty(value = "员工状态")
    private Integer employeeStatus;

    @ApiModelProperty(value = "员工性别", example = "1")
    private Integer employeeGender;

    @ApiModelProperty(value = "是否调解员")
    private Boolean mediatorFlag;

    @ApiModelProperty(value = "组织ID", example = "1")
    private Long companyId;

}