package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 案件文书;
 *
 * <AUTHOR> llj
 * @date : 2024-7-31
 */
@ApiModel(value = "案件文书")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "mdt_instruments", autoResultMap = true)
public class MdtInstruments implements Serializable {
    /**
     * 文书id
     */
    @ApiModelProperty("文书id")
    @TableId
    private Long instrumentsId;
    /**
     * 案件id
     */
    @ApiModelProperty("案件id")
    private Long caseId;
    /**
     * 文书名称
     */
    @ApiModelProperty("文书名称")
    private String instrumentsName;
    /**
     * 文书文件地址
     */
    @ApiModelProperty("文书文件地址")
    private String instrumentsFilePath;
    /**
     * 文书文件类型
     */
    @ApiModelProperty("文书文件类型")
    private String instrumentsFileType;
    /**
     * 文书签名人身份
     */
    @ApiModelProperty("文书签名人身份")
    private String instrumentsIdentityType;
    /**
     * 文书签名人id
     */
    @ApiModelProperty("文书签名人id")
    private String instrumentsLitigantIds;
    /**
     * 文件签名id
     */
    @ApiModelProperty("文件签名id")
    private Long fileSignInfoId;
    /**
     * 文书签名状态
     */
    @ApiModelProperty("文书签名状态")
    private Integer instrumentsSignStatus;
    /**
     * 文书模板id
     */
    @ApiModelProperty("文书模板id")
    private Long instrumentsTmplId;
    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long creatorId;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 修改人id
     */
    @ApiModelProperty("修改人id")
    private Long updaterId;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;
    /**
     * 逻辑删除标志
     */
    @ApiModelProperty("逻辑删除标志")
    private Boolean deleteFlag;

    /**
     * 文书创建方式
     */
    @ApiModelProperty("创建方式")
    private String createType;

    /**
     * 调解录音id集合
     */
    @ApiModelProperty("调解录音id集合")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> recordIdList;
}
