package com.sct.tiaojie.repository.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * <p>
 * 任务配置表 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          [CurrentDate]
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "任务配置实体")
@TableName(value = "task_config", autoResultMap = true)
@NoArgsConstructor
public class TaskConfig implements Serializable {

    @ApiModelProperty(value = "配置ID", example = "1")
    @TableId(value = "config_id", type = IdType.AUTO)
    private Long configId;

    @ApiModelProperty(value = "流程ID", example = "1001")
    @TableField("workflow_id")
    private Long workflowId;

    @ApiModelProperty(value = "任务类型", example = "1")
    @TableField("task_type")
    private Integer taskType;

    @ApiModelProperty(value = "任务时限(小时)", example = "24")
    @TableField("time_limit")
    private Integer timeLimit;

    @ApiModelProperty(value = "负责人accountId集合")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> managerIdList;
}
