package com.sct.tiaojie.repository.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sct.tiaojie.service.call.dto.TenCallCaseMsgDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel("腾讯外呼信息")
@Data
@TableName("call_info_ten")
public class CallInfoTen {

    @ApiModelProperty("ID")
    @TableId(value = "info_id", type = IdType.ASSIGN_ID)
    private Long infoId;

    @ApiModelProperty("session")
    private String sessionId;

    /**
     * @see TenCallCaseMsgDTO
     */
    @ApiModelProperty("呼叫信息")
    private String callInfo;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;
}
