package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.mdt.entity.MdtDocumentTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2022/4/21 11:18
 * @Version 1.0
 */
@Mapper
public interface MdtDocumentTemplateMapper extends BaseMapper<MdtDocumentTemplate> {

    /**
     * 获取文书签名关键字
     * @param title
     * @param companyId
     * @return
     */
    String getKeyWord(@Param("title") String title, @Param("companyId") Long companyId);
}
