package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 帐号 实体
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "帐号实体")
public class AuthAccount implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "帐号ID", example = "1")
    private Long accountId;

    @ApiModelProperty(value = "登陆名,最大长度为20")
    @Length(min = 0, max = 20, message = "登陆名格式错误,最大长度为20")
    private String loginName;

    @ApiModelProperty(value = "登陆密码,最大长度为50")
    @Length(min = 0, max = 50, message = "登陆密码格式错误,最大长度为50")
    private String loginPwd;

    @ApiModelProperty(value = "手机,最大长度为20")
    @Length(min = 0, max = 20, message = "手机格式错误,最大长度为20")
    private String accountMobile;

    @ApiModelProperty(value = "邮箱,最大长度为80")
    @Length(min = 0, max = 80, message = "邮箱格式错误,最大长度为80")
    private String accountEmail;

    @ApiModelProperty(value = "帐号状态：0 停用、 1 正常、 2 锁定")
    private Integer accountStatus;

    @ApiModelProperty(value = "实名认证ID", example = "1")
    private Long personId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "密码错误次数", example = "1")
    private Integer passwdErrorTimes;

    @ApiModelProperty(value = "组织ID", example = "1")
    private Long companyId;

    @ApiModelProperty(value = "是否平台用户", example = "1")
    private Integer isplateform;

    @ApiModelProperty(value = "账号是否首次登录", example = "1")
    private Integer isFirstLogin;

    @ApiModelProperty(value = "账号锁定时间")
    private LocalDateTime lockTime;

    @ApiModelProperty(value = "密码更新时间")
    private LocalDateTime pwdUpdateTime;

    @ApiModelProperty(value = "账号ip限制")
    private String ipConfig;
}