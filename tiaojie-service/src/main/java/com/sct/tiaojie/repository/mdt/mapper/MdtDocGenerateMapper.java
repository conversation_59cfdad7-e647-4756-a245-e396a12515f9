package com.sct.tiaojie.repository.mdt.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface MdtDocGenerateMapper {

    @SelectProvider(type = ModuleDataSqlProvider.class, method = "buildSelectSql")
    List<Map<String, Object>> selectModuleData(@Param("tableName") String tableName,
                                               @Param("fieldName") String fieldName,
                                               @Param("caseId") Long caseId);

    @SelectProvider(type = ModuleDataSqlProvider.class, method = "buildSelectSqlByCondition")
    List<Map<String, Object>> selectModuleDataByCondition(@Param("tableName") String tableName,
                                               @Param("fieldName") String fieldName,
                                               @Param("caseId") Long caseId,
                                               @Param("conditionName") String conditionName,
                                               @Param("conditionValue") String conditionValue);

    @SelectProvider(type = ModuleDataSqlProvider.class, method = "buildSelectSqlByValue")
    List<Map<String, Object>> selectValueField(@Param("tableName") String tableName,
                                               @Param("keyFieldName") String keyFieldName,
                                               @Param("valueFieldName") String valueFieldName,
                                               @Param("value") Object value);

    class ModuleDataSqlProvider {
        public String buildSelectSql(@Param("tableName") String tableName,
                                     @Param("fieldName") String fieldName,
                                     @Param("caseId") Long caseId) {
            return new SQL() {{
                SELECT(fieldName);
                FROM(tableName);
                WHERE("case_id = #{caseId}");
            }}.toString();
        }

        public String buildSelectSqlByCondition(@Param("tableName") String tableName,
                                     @Param("fieldName") String fieldName,
                                     @Param("caseId") Long caseId,
                                     @Param("conditionName") String conditionName,
                                     @Param("conditionValue") String conditionValue) {
            return new SQL() {{
                SELECT(fieldName);
                FROM(tableName);
                WHERE("case_id = #{caseId}");
                AND();
                WHERE(conditionName + " = #{conditionValue}");
            }}.toString();
        }

        public String buildSelectSqlByValue(@Param("tableName") String tableName,
                                            @Param("keyFieldName") String keyFieldName,
                                            @Param("valueFieldName") String valueFieldName,
                                            @Param("value") Object value) {
            return new SQL() {{
                SELECT(valueFieldName);
                FROM(tableName);
                WHERE(keyFieldName + " = #{value}");
            }}.toString();
        }
    }
}
