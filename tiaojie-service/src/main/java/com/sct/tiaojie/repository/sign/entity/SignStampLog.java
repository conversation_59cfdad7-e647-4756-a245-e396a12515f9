package com.sct.tiaojie.repository.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 电子签章日志实体
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2020-11-19
 */
@TableName(value = "sign_stamp_log")
@Data
public class SignStampLog {
    /**
     * 日志表记录ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long stampLogId;

    /**
     * 签章文件路径
     */
    private String filePath;

    /**
     * 用户姓名    仲裁员或秘书名字
     */
    private String personName;

    /**
     * 签章类型
     */
    private Integer stampType;

    /**
     * '签章方式    手动、自动
     */
    private Integer stampWay;

    /**
     * 操作者类别    仲裁员、秘书、机器
     */
    private Integer operatorType;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;


}
