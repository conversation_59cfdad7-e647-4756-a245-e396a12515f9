package com.sct.tiaojie.repository.vo;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "任务配置实体")
@NoArgsConstructor
public class TaskConfigVo implements Serializable {

    @ApiModelProperty(value = "配置ID", example = "1")
    @TableId(value = "config_id", type = IdType.AUTO)
    private Long configId;

    @ApiModelProperty(value = "流程ID", example = "1001")
    @TableField("workflow_id")
    private Long workflowId;

    @ApiModelProperty(value = "任务类型", example = "1")
    private Integer taskType;

    @ApiModelProperty(value = "任务时限(小时)", example = "24")
    @TableField("time_limit")
    private Integer timeLimit;

    @ApiModelProperty(value = "负责人accountId集合")
    private List<Long> managerIdList;
}
