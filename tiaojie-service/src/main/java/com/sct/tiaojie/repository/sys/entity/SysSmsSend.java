package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 短信发送记录 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2021-06-05
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "短信发送记录实体")
public class SysSmsSend implements Serializable {

    @ApiModelProperty(value = "主键，短信发送ID", example = "1")
    @TableId(value = "sms_send_id", type = IdType.ASSIGN_ID)
    private Long smsSendId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "腾讯云短信唯一sid,最大长度为500")
    @Length(min = 0, max = 500, message = "腾讯云短信唯一sid格式错误,最大长度为500")
    private String smsId;

    @ApiModelProperty(value = "业务类型，可为空，由调用方传入", example = "1")
    private Integer bizType;

    @ApiModelProperty(value = "业务ID，可为空，由调用方传入", example = "1")
    private Long bizId;

    @ApiModelProperty(value = "短信模板", example = "1")
    private Integer smsTld;

    @ApiModelProperty(value = "短信参数,最大长度为30")
    @Length(min = 0, max = 30, message = "短信参数格式错误,最大长度为30")
    private String smsParam;

    @ApiModelProperty(value = "手机地区码前缀,最大长度为20")
    @Length(min = 0, max = 20, message = "手机地区码前缀格式错误,最大长度为20")
    private String nationCode;

    @ApiModelProperty(value = "接收者手机号码,最大长度为20")
    @Length(min = 0, max = 20, message = "接收者手机号码格式错误,最大长度为20")
    private String receivePhone;

    @ApiModelProperty(value = "短信接收时间")
    private LocalDateTime userReceiveTime;

    @ApiModelProperty(value = "短信内容,最大长度为255")
    @Length(min = 0, max = 255, message = "短信内容格式错误,最大长度为255")
    private String smsContent;

    @ApiModelProperty(value = "状态码,最大长度为20")
    @Length(min = 0, max = 20, message = "状态码格式错误,最大长度为20")
    private String statusCode;

    @ApiModelProperty(value = "状态描述,最大长度为255")
    @Length(min = 0, max = 255, message = "状态描述格式错误,最大长度为255")
    private String statusDesc;

    @ApiModelProperty(value = "错误信息描述,最大长度为255")
    @Length(min = 0, max = 255, message = "错误信息描述格式错误,最大长度为255")
    private String errorMsg;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

}