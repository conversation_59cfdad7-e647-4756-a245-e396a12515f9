package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("基础模块字段表")
@TableName("tmpl_basis_module_field")
public class TmplBasisModuleField {

    @TableId(value = "basis_module_field_id", type = IdType.AUTO)
    @ApiModelProperty("基础模块字段ID")
    private Integer basisModuleFieldId;

    @ApiModelProperty("基础模块ID")
    private Integer basisModuleId;

    @ApiModelProperty("对应数据表字段名")
    private String fieldName;

    @ApiModelProperty("字段标题")
    private String fieldTitle;

    @ApiModelProperty("字段描述")
    private String fieldDesc;

    @ApiModelProperty("字段序号")
    private Integer fieldSn;

    @ApiModelProperty("字段ui")
    private String fieldUi;

    @ApiModelProperty("字段数据类型")
    private String fieldDataType;

    @ApiModelProperty("字段取值")
    private String fieldValues;

    @ApiModelProperty("系统字典Type")
    private String fieldDictType;

    @ApiModelProperty("是否校验数据格式")
    private Integer isValidated;

    @ApiModelProperty("是否必须")
    private Integer isRequire;

    @ApiModelProperty("是否结案批准校验")
    private Integer isApprovalValidated;

    @ApiModelProperty("是否可修改")
    private Integer isUpdate;

    @ApiModelProperty("是否脱敏")
    private Integer isDesensitize;

    @ApiModelProperty("脱敏规则")
    private String desensitizeRule;

    @ApiModelProperty("字段语义")
    private String fileSemantics;

    @ApiModelProperty("是否失效")
    private Integer isDisable;

    @ApiModelProperty("呈现格式")
    private String showFormat;

    @ApiModelProperty("验证格式，规则")
    private String validation;
}
