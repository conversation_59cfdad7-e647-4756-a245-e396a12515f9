package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "案件呈批实体")
public class MdtCaseApproval {

    @TableId(value = "approval_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long approvalId;

    @ApiModelProperty(value = "案件id")
    private Long caseId;

    @ApiModelProperty(value = "呈批类型")
    private Integer approvalType;

    @ApiModelProperty(value = "呈批流程类型  onlyFirst,both,onlySecond")
    private String approvalFlowType;

    @ApiModelProperty(value = "发起人ID")
    private Long startManId;

    @ApiModelProperty(value = "发起人名字")
    private String startManName;

    @ApiModelProperty(value = "发起人角色名字")
    private String startManRole;

    @ApiModelProperty(value = "发起人所属调解组织ID")
    private Long startOrgId;

    @ApiModelProperty(value = "发起人所属调解组织名称")
    private String startOrgName;

    @ApiModelProperty(value = "发起人所属调解团队ID")
    private Long startDeptId;

    @ApiModelProperty(value = "发起时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "调解结果")
    private String mdtResult;

    @ApiModelProperty(value = "（调解/案管）办结发起时间")
    private LocalDateTime caseCloseTime;

    @ApiModelProperty(value = "案件延期日期")
    private LocalDateTime caseDelayTime;

    @ApiModelProperty(value = "延期理由")
    private String delayReason;

    @ApiModelProperty(value = "终止理由")
    private String closeReason;

    @ApiModelProperty(value = "成功原因")
    private String successReason;

    @ApiModelProperty(value = "中止原因")
    private String suspendReason;

    @ApiModelProperty(value = "案管办结原因")
    private String caseManageCloseReason;

    @ApiModelProperty(value = "繁简分流打分")
    private Integer score;

    @ApiModelProperty(value = "审批状态")
    private String approvalStatus;

    @ApiModelProperty(value = "审批结果")
    private String approvalResult;


    @ApiModelProperty(value = "一级审批人ID")
    private Long firstManId;

    @ApiModelProperty(value = "一级审批人名字")
    private String firstManName;

    @ApiModelProperty(value = "一级审批人角色名字")
    private String firstManRole;

    @ApiModelProperty(value = "一级审批人所属调解组织ID")
    private Long firstOrgId;

    @ApiModelProperty(value = "一级审批人所属调解组织名称")
    private String firstOrgName;

    @ApiModelProperty(value = "一级审核结果")
    private String firstAuditResult;

    @ApiModelProperty(value = "一级审核说明")
    private String firstAuditDes;

    @ApiModelProperty(value = "一级审核时间")
    private LocalDateTime firstAuditTime;


    @ApiModelProperty(value = "二级审批人ID")
    private Long secondManId;

    @ApiModelProperty(value = "二级审批人名字")
    private String secondManName;

    @ApiModelProperty(value = "二级审批人角色名字")
    private String secondManRole;

    @ApiModelProperty(value = "二级审批人所属组织ID")
    private Long secondOrgId;

    @ApiModelProperty(value = "二级审批人所属组织名称")
    private String secondOrgName;

    @ApiModelProperty(value = "二级审核结果")
    private String secondAuditResult;

    @ApiModelProperty(value = "二级审核说明")
    private String secondAuditDes;

    @ApiModelProperty(value = "二级审核时间")
    private LocalDateTime secondAuditTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;


}
