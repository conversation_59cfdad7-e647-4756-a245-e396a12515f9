package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtCaseImportRecord;
import com.sct.tiaojie.service.mdt.bo.MdtCaseImportRecordPageBo;
import com.sct.tiaojie.service.mdt.dto.MdtCaseImportRecordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MdtCaseImportRecordMapper extends BaseMapper<MdtCaseImportRecord> {

    Page<MdtCaseImportRecordDTO> pageList(Page<MdtCaseImportRecord> page,@Param("param") MdtCaseImportRecordPageBo param);
}
