package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtSmsTemplate;
import com.sct.tiaojie.service.mdt.bo.MdtSmsLogPageBo;
import com.sct.tiaojie.service.mdt.bo.MdtSmsTempPageBO;
import com.sct.tiaojie.service.mdt.dto.MdtSmsLogDTO;
import com.sct.tiaojie.service.mdt.dto.MdtSmsTempPageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/7 9:32
 * @Version 1.0
 */
public interface MdtSmsTemplateMapper extends BaseMapper<MdtSmsTemplate> {

    /**
     * 分页查询
     * @param page
     * @param param
     * @return
     */
    Page<MdtSmsTempPageDTO> pageList(Page<MdtSmsTempPageDTO> page, @Param("param") MdtSmsTempPageBO param);

    /**
     * 短信发送记录分页查询
     */
    Page<MdtSmsLogDTO> logPageList(Page<MdtSmsLogDTO> page, @Param("param") MdtSmsLogPageBo param);

    /**
     * 案件详情--获取短信发送列表
     * @param caseId 案件id
     */
    List<MdtSmsLogDTO> logListByCaseId(@Param("caseId") Long caseId);

    /**
     * 根据bo条件查询短信日志记录
     * @param mdtSmsLogPageBo 查询条件
     */
    List<MdtSmsLogDTO> selectSmsListByBo(@Param("param") MdtSmsLogPageBo mdtSmsLogPageBo);
}
