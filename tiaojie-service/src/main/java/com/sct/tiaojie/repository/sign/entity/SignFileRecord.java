package com.sct.tiaojie.repository.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description 文件签名记录信息实体
 * <AUTHOR>
 */
@Data
@TableName(value = "sign_file_record")
@ApiModel("文件签名记录信息实体")
public class SignFileRecord {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "文件签名记录ID, 长度为12")
    private Long fileSignRecordId;
    
    @ApiModelProperty(value = "文件签名信息, 长度为12")
    private Long fileSignInfoId;
    
    @ApiModelProperty(value = "签名人名字, 长度为128")
    private String signManName;
    
    @ApiModelProperty(value = "签名人类型：参考参会人员类型")
    private Integer signManBizType;
    
    @ApiModelProperty(value = "签名人对应业务ID（当事人或代理人或仲裁员）, 长度为12")
    private Long signManBizId;
    
    @ApiModelProperty(value = "签名人实名personID, 长度为12")
    private Long signManPersonId;
    
    @ApiModelProperty(value = "签名关键字, 长度为128")
    private String keyword;
    
    @ApiModelProperty(value = "签名位置横坐标")
    private Float locationX;
    
    @ApiModelProperty(value = "签名位置纵坐标")
    private Float locationY;
    
    @ApiModelProperty(value = "状态：1待签名，2拒绝签名，3签名完成")
    private Integer status;
    
    @ApiModelProperty(value = "操作时间，签名时间或拒绝签名时间")
    private LocalDateTime processTime;
    
    @ApiModelProperty(value = "拒绝签名的理由, 长度为128")
    private String rejectReason;
    
    @ApiModelProperty(value = "创建人ID, 长度为12")
    private Long createManId;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "更新人ID, 长度为12")
    private Long updateManId;
    
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    
} 
        