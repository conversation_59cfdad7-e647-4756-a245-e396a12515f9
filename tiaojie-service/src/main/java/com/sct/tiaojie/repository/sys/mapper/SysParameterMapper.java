package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysParameter;
import com.sct.tiaojie.service.sys.dto.SysParameterDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/***********************************************************************************************************************
 * <p>
 *   系统参数 Mapper 接口
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2019-06-23
 * @version       V1.0
 **********************************************************************************************************************/
@Repository
public interface SysParameterMapper extends BaseMapper<SysParameter> {

    List<SysParameterDTO> getSysParameterByModule(@Param("moduleCode") String moduleCode);

    SysParameterDTO getSysParameterByModuleAndParam(@Param("moduleCode") String moduleCode, @Param("paramCode") String paramCode);

    List<SysParameterDTO> getAllSysParameter();

    String getValueSysParameterByModuleAndParam(@Param("moduleCode") String moduleCode, @Param("paramCode") String paramCode);

    @Update("UPDATE sys_parameter SET param_value = #{param.paramValue} WHERE module_code = #{param.moduleCode} AND param_code = #{param.paramCode}")
    void updateParameter(@Param("param") SysParameter sysParameter);

    @Update("UPDATE sys_parameter SET param_value = '1' WHERE module_code = #{task} AND param_code = #{paramCode} AND param_value = '0'")
    boolean startTask(String task, String paramCode);

    @Update("UPDATE sys_parameter SET param_value = '0' WHERE module_code = #{task} AND param_code = #{paramCode}")
    boolean endTask(String task, String paramCode);
}