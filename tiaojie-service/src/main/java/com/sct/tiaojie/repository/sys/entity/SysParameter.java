package com.sct.tiaojie.repository.sys.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/***********************************************************************************************************************
 * <p>
 *   系统参数 实体
 * </p>
 * @copyright 2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2019-06-23
 * @version V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "系统参数实体")
public class SysParameter implements Serializable {

    @ApiModelProperty(value = "模块代码,最大长度为100")
    @Length(min = 0, max = 100, message = "模块代码格式错误,最大长度为100")
    private String moduleCode;

    @ApiModelProperty(value = "参数代码,最大长度为100")
    @Length(min = 0, max = 100, message = "参数代码格式错误,最大长度为100")
    private String paramCode;

    @ApiModelProperty(value = "参数名称,最大长度为100")
    @Length(min = 0, max = 100, message = "参数名称格式错误,最大长度为100")
    private String paramName;

    @ApiModelProperty(value = "参数值,最大长度为1000")
    @Length(min = 0, max = 1000, message = "参数值格式错误,最大长度为1000")
    private String paramValue;

    @ApiModelProperty(value = "参数备注,最大长度为100")
    @Length(min = 0, max = 100, message = "参数备注格式错误,最大长度为100")
    private String paramCmnt;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "最近修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "最近修改用户", example = "1")
    private Long updaterId;

}