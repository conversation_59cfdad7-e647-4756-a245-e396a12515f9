package com.sct.tiaojie.repository.task.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * <p>
 * 触发条件组表 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          [CurrentDate]
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "触发条件组实体")
@TableName(value = "task_trigger_condition_group", autoResultMap = true)
@NoArgsConstructor
public class TaskTriggerConditionGroup implements Serializable {

    @ApiModelProperty(value = "条件组ID", example = "1")
    @TableId(value = "group_id", type = IdType.AUTO)
    private Long groupId;

    @ApiModelProperty(value = "配置ID", example = "1001")
    @TableField("config_id")
    private Long configId;

    @ApiModelProperty(value = "父组ID", example = "0")
    @TableField("parent_group_id")
    private Long parentGroupId;

    @ApiModelProperty(value = "组间逻辑运算符(1:AND 2:OR)", example = "1")
    @TableField("logic_operator")
    private Integer logicOperator;

    @ApiModelProperty(value = "排序序号", example = "1")
    @TableField("sort_order")
    private Integer sortOrder;
}