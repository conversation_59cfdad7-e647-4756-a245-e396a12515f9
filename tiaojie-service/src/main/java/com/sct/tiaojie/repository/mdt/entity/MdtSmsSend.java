package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/5/12 9:34
 * @Version 1.0
 */
@Data
@ApiModel(value = "短信发送实体")
public class MdtSmsSend {
    @TableId(value = "sms_send_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long smsSendId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "是否批量发送")
    private Boolean isBatch;

    @ApiModelProperty(value = "业务上下文")
    private String businessContext;

    @ApiModelProperty(value = "短信拓展号码")
    private String extendCode;

    @ApiModelProperty(value = "短信签名")
    private String signName;

    @ApiModelProperty(value = "腾讯生成的模板id")
    private String txTemplateId;

    @ApiModelProperty(value = "模板内容")
    private String templateContent;

    @ApiModelProperty(value = "参数")
    private String templateParams;

    @ApiModelProperty(value = "短信内容")
    private String messageContent;

    @ApiModelProperty(value = "发送人")
    private Long senderId;

    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;
}
