package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel(value = "失联修复记录表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mdt_repair_record")
public class MdtRepairRecord {

    @ApiModelProperty(value = "ID")
    @TableId(value = "repair_record_id",type = IdType.ASSIGN_ID)
    private Long repairRecordId;

    @ApiModelProperty("批次Id")
    private Long repairId;

    @ApiModelProperty(value = "失联修复当事人表Id")
    private Long repairLitigantId;

    @ApiModelProperty("案件Id")
    private Long caseId;

    @ApiModelProperty("当事人ID")
    private Long litigantId;

    @ApiModelProperty("批次号")
    private String taskNo;

    @ApiModelProperty("(云宝宝)修复客户请求Id")
    private String reqId;

    @ApiModelProperty("修复渠道")
    private Integer repairNet;

    @ApiModelProperty("加密方式")
    private String maskModel;

    @ApiModelProperty("修复状态")
    private String repairStatus;

    @ApiModelProperty("修复结果")
    private String repairResult;

    @ApiModelProperty("修复失败错误码")
    private String repairFailCode;

    @ApiModelProperty("修复失败错误信息")
    private String repairFailMsg;

    @ApiModelProperty("(全网、异网、电信)修复成功后的号码列表")
    private String phoneList;

    @ApiModelProperty("（全网、异网）结果Id")
    private String resultId;

    @ApiModelProperty("修复成功号码数量")
    private Integer phoneNumber;

    @ApiModelProperty("修复完成时间")
    private LocalDateTime repairCompleteTime;

    @ApiModelProperty("过期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty("上传失联修复数据原样记录")
    private String uploadRequestBody;

    @ApiModelProperty("下一个失联修复渠道")
    private Integer nextOrder;

    @ApiModelProperty("发起人Id")
    private Long accountId;

    @ApiModelProperty("发起组织Id")
    private Long companyId;


}
