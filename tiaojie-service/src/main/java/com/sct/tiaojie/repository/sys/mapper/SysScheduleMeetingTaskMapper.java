package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysScheduleMeetingTask;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface SysScheduleMeetingTaskMapper extends BaseMapper<SysScheduleMeetingTask> {

    /**
     * 删除任务
     * @param meetingId
     */
    @Delete("DELETE FROM sys_schedule_meeting_task WHERE meeting_id = #{meetingId}")
    void deleteByMeetingId(@Param("meetingId") String meetingId);
}
