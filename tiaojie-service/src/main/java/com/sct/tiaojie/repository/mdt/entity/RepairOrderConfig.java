package com.sct.tiaojie.repository.mdt.entity;

import com.cloudbae.finance.collection.Fix;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel(value = "失联修复配置")
@Data
public class RepairOrderConfig {

    //netOrder 表示进行失联修复的顺序 1表示全网 2表示异网 3表示电信 4表示云宝宝，[1,2,3]就按顺序使用全网、异网、电信
    @ApiModelProperty("执行顺序 netOrder 表示进行失联修复的顺序 1表示全网 2表示异网 3表示电信 4表示云宝宝，[1,2,3]就按顺序使用全网、异网、电信")
    private List<Integer> netOrder;

//    baseUrl: http://*************:8080/telecom-lostcustomer-ws-api
//    appId-woldNet: 965956edd1a147d3bf85d9d70b13ab95
//    appKey-woldNet: app123
//    credential-woldNet: 6fc57ef25fd747dd955c0f687f5fb1cd
//    appId-diffNet: 8f0f541d1939487b9f38f10d30ff63fd
//    appKey-diffNet: app123
//    credential-diffNet: 437f94a8872743a0bf4c6db1e5927eff
//    signature: 1046ac9e14b6450681c94227ae160654
//    privateKey: 89a6d74a6d9d4f6f
//    tyUrl: http://*************:8080/https:api.tycredit.com/
//    testFlag: 0
//    reqSys: ChangNingCourt001
//    acttivityCode: 1005
//    Fix.verifyPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkHhjZHflQk7caU3AoSWVvs8KDkFc6n6zP5ngSG3Jw9pRqarQZyl+75PdrCI7fTehqq8wtlvUKq0pS2fjyAh2wKKfXeMGw7TAD14YV/Em8jdtdRVyMdOPQqEJ7TFvVRNnfee3FvHZif4x5P/BOIUXIe23Tna8LNeBlOZipCcZniHbPWuWHChsuKYdh+mw+T4J//YPVzvpSRZ7XAYf6iZyiDKsb+M+K/mVN7M99j4Ok5IzT+BUGynVN8IZNlFiA8kc++KZdueFvmQRTSBTcKoS3iFVTOPJdZZ7dut9PpHfbDib3w+oT06C0LBkX9XUvv87mqRHjl2SBof7Ak4h+RZ4CwIDAQAB";
//    Fix.privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCQeGNkd+VCTtxpTcChJZW+zwoOQVzqfrM/meBIbcnD2lGpqtBnKX7vk92sIjt9N6GqrzC2W9QqrSlLZ+PICHbAop9d4wbDtMAPXhhX8SbyN211FXIx049CoQntMW9VE2d957cW8dmJ/jHk/8E4hRch7bdOdrws14GU5mKkJxmeIds9a5YcKGy4ph2H6bD5Pgn/9g9XO+lJFntcBh/qJnKIMqxv4z4r+ZU3sz32Pg6TkjNP4FQbKdU3whk2UWIDyRz74pl254W+ZBFNIFNwqhLeIVVM48l1lnt2630+kd9sOJvfD6hPToLQsGRf1dS+/zuapEeOXZIGh/sCTiH5FngLAgMBAAECggEAf2G2+WoBufKAw1SsKSpTm5c/3YviWlx2vfh2WkZ7ZXYFFR2ofZ2H78Qn61OPsnuvzyiDQfc73pkp1PP4UnKsCnQZIpY/a6Xexm4H9tqJpX4bw+Wx9577LRuC/iHdcV0XnlsGA3QhhFueMLUU8DnhSKgqPUPR7ZPjDKAKj4RuQQ9uxSm0xOfTJiwtklX4pV07fKrZ0T12mWcmcAjM/wvk+VXySPG4eIlwUMS6hA2cQ3jrQ0ob4B/FlACgv/7euM6KmzwfEz5EZsu0E8L76GH8/mtUED2xC8mQ5SiCTRIKje8hYE5OyjWg6wRlDWNVNM8B10jIffZvakFLb2Tr+Z7JsQKBgQDlCKbw7OJDgMbt7TdizHfSC1Xyjak8Gtg94crx7cSQsOvKhbYKwbsvR5Wpnn3hAKYGwHlCX8QJE4+oMCeN8UMHATsIu4y5FYsHhJPXLcrpr930cD/bHsojB8H2jm4af+lqGzikyWeBjD1gtYzhur3xQEKcpo7d7wLx5Rl/Mg9UmQKBgQCheuRUmKltuQG4elqAxVn4qnv6OzcxeM4E4XH9eKQ6oDfw6DbFz3KvKyJ6X7KsXuk4ZHDwZeaF6QIpf/pwjItFlTDG0HaQr/0lIQmhr6Epqo5PE5N8GpR8e+7HiPP0oLRmuxCmg0hBqF8T+mq/yzlTk1H/uR0AXkajK/lwC250QwKBgGZNFAw/Mt2F8tUphPi7bbXDXHOH9RSkxPv3E+g2FPuwGtIOIX7yg8KdjesCoSVUXdRcOo18KxsGwr8UK0bKge7qDM1cwIGXbs8/161ghecVp+VcEhm0ygMVnvybJ/DsO30qhYWNdI837d55mWDmQZs14csI1n2pGb6kX1wiJxjZAoGBAIyj0u/XEmGM63G3ptFWkRjhqx7yqgx9cbmQfM5NX4Pr/SzloqbAGdt0jw/T/3LfKvLBkBfgOFlS6l4op3+U+Mu+t3/BGjLEBI9s6c2XgJ8ge9dlbmN9ZuvQqNPCdO8PdG+w5zyPRMRil4R36rdmkWQ4AAfgOuESOyVsbFBotGevAoGBAKhSYgLIBcEYEXFexpQiww1C92ab4cncGTG+P8DBfvMhA9Xn6gV2IG0woHNTbR++KoV8vRMiAchHZo3wEuOz6NO5fI1g/T5QBf+25DinaCaIswxXZURuJmHfRhNeoLV9cHtoORis/cGDP7LcyqM+Z3Rb6Xtfj/RmXdIdvveQdcFs";
//    Fix.merchantNo = "1627917021670318080";
//    Fix.secret = "37668ab3c193136188a0b02d0cca4f24";
    @ApiModelProperty("(全网、异网) 基本url")
    private String baseUrl;

    @ApiModelProperty("(全网) appId-woldNet")
    private String woldNetAppId;

    @ApiModelProperty("(全网) appKey-woldNet")
    private String woldNetAppKey;

    @ApiModelProperty("(全网) credential-woldNet")
    private String woldNetCredential;

    @ApiModelProperty("(异网) appId-diffNet")
    private String diffNetAppId;

    @ApiModelProperty("(异网)  appKey-diffNet")
    private String diffNetAppKey;

    @ApiModelProperty("(异网) credential-diffNet")
    private String diffNetCredential;

    @ApiModelProperty("(全网、异网) signature")
    private String signature;

    @ApiModelProperty("(电信) privateKey")
    private String tyPrivateKey;

    @ApiModelProperty("(电信) tyUrl")
    private String tyUrl;

    @ApiModelProperty("(电信) testFlag")
    private Integer testFlag;

    @ApiModelProperty("(电信) reqSys")
    private String reqSys;

    @ApiModelProperty("(电信) activityCode")
    private String activityCode;

    @ApiModelProperty("(电信)虚拟号码")
    private List<String> tyVirtualNo;

    @ApiModelProperty("(电信)坐席号码")
    private List<String> tyCallNo;

    @ApiModelProperty("(云宝宝)坐席号码")
    private List<String> BaeCallNo;

    @ApiModelProperty("(云宝宝) verifyPublicKey")
    private String verifyPublicKey;

    @ApiModelProperty("(云宝宝) privateKey")
    private String baePrivateKey;

    @ApiModelProperty("(云宝宝) merchantNo")
    private String merchantNo;

    @ApiModelProperty("(云宝宝) secret")
    private String secret;




}
