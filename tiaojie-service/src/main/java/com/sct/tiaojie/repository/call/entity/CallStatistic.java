package com.sct.tiaojie.repository.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("呼叫统计表")
@TableName("mdt_call_statistic")
public class CallStatistic implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("统计ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long statisticId;

    @ApiModelProperty("呼叫类型,1-呼入 2-呼出")
    private Integer callType;

    @ApiModelProperty("统计年")
    private String statisticYear;

    @ApiModelProperty("统计月")
    private String statisticMonth;

    @ApiModelProperty("统计日")
    private String statisticDay;

    @ApiModelProperty("案源方id")
    private Long entrustsId;

    @ApiModelProperty("调解组织ID")
    private Long orgId;

    @ApiModelProperty("调解组织名称")
    private String orgName;

    @ApiModelProperty("部门ID")
    private Long deptId;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("案由")
    private String caseCause;

    @ApiModelProperty("坐席ID")
    private String agentId;

    @ApiModelProperty("调解员ID")
    private Long mediatorId;

    @ApiModelProperty("调解员姓名")
    private String mediatorName;

    @ApiModelProperty("通话总数")
    private Long callCount;

    @ApiModelProperty("接通总数")
    private Long throughCount;

    @ApiModelProperty("有效接通总数")
    private Long validCallCount;

    @ApiModelProperty("有效通话总时长")
    private Long validCallTimeTotal;

    @ApiModelProperty("通话总时长")
    private Long callTimeTotal;

    @ApiModelProperty("未接听数")
    private Long callMissedCount;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}