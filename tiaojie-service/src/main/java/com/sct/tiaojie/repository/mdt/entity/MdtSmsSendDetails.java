package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/5/12 9:07
 * @Version 1.0
 */
@Data
@ApiModel(value = "短信发送详情实体")
public class MdtSmsSendDetails {
    @TableId(value = "sms_details_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long smsDetailsId;

    @ApiModelProperty(value = "短信发送表id")
    private Long smsSendId;

    @ApiModelProperty(value = "序列号")
    private String serialNo;

    @ApiModelProperty(value = "ISO标准的国家/地区代码")
    private String isoCode;

    @ApiModelProperty(value = "电话号码")
    private String phoneNumber;

    @ApiModelProperty(value = "不带任何前缀的电话号码")
    private String subScriberNumber;

    @ApiModelProperty(value = "短信数量")
    private Long fee;

    @ApiModelProperty(value = "发送状态")
    private String sendStatus;

    @ApiModelProperty(value = "发送的错误信息")
    private String sendMessage;

    @ApiModelProperty(value = "国家/地区代码")
    private String countryCode;

    @ApiModelProperty(value = "用户接收时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "接收状态")
    private String receiveStatus;

    @ApiModelProperty(value = "接收描述")
    private String receiveDescription;

    @ApiModelProperty(value = "短信内容")
    private String content;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
