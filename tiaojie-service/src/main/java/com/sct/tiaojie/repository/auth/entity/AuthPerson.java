package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AuthPerson {

    @ApiModelProperty(value = "实名ID", example = "1")
    @TableId(type = IdType.ASSIGN_ID)
    private Long personId;

    @ApiModelProperty(value = "实名信息ID", example = "1")
    private Long realnameId;

    @ApiModelProperty(value = "证件号,最大长度为50")
    @Length(min = 0, max = 50, message = "证件号格式错误,最大长度为50")
    private String idCard;

    @ApiModelProperty(value = "姓名,最大长度为50")
    @Length(min = 0, max = 50, message = "姓名格式错误,最大长度为50")
    private String name;

    @ApiModelProperty(value = "手机,最大长度为50")
    @Length(min = 0, max = 50, message = "手机格式错误,最大长度为50")
    private String mobile;

    @ApiModelProperty(value = "性别,最大长度为5")
    @Length(min = 0, max = 5, message = "性别格式错误,最大长度为5")
    private String gender;

    @ApiModelProperty(value = "民族,最大长度为50")
    @Length(min = 0, max = 50, message = "民族格式错误,最大长度为50")
    private String ocrNation;

    @ApiModelProperty(value = "地址,最大长度为255")
    @Length(min = 0, max = 255, message = "地址格式错误,最大长度为255")
    private String ocrAddress;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;
}
