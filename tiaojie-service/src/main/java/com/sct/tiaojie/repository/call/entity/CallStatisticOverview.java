package com.sct.tiaojie.repository.call.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("每日呼叫统计")
@Data
public class CallStatisticOverview {

    @ApiModelProperty("统计年")
    private String statisticYear;

    @ApiModelProperty("统计月")
    private String statisticMonth;

    @ApiModelProperty("统计日")
    private String statisticDay;

    @ApiModelProperty("通话总数")
    private Long callCount;

    @ApiModelProperty("接通总数")
    private Long throughCount;

    @ApiModelProperty("通话总时长")
    private Long callTimeTotal;

    @ApiModelProperty("活跃坐席数")
    private Long agentCount;
}
