package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

@Data
@TableName("sys_login_log")
@ApiModel("登陆日志表")
public class SysLoginLog {

    @TableId(value = "login_log_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "登陆日志ID")
    private Long loginLogId;

    @ApiModelProperty(value = "登陆名,最大长度为20")
    @Length(min = 0, max = 20, message = "登陆名格式错误,最大长度为20")
    private String loginName;

    @ApiModelProperty(value = "登陆密码,最大长度为50")
    @Length(min = 0, max = 50, message = "登陆密码格式错误,最大长度为50")
    private String loginPwd;

    @ApiModelProperty(value = "帐号ID")
    private Long accountId;

    @ApiModelProperty(value = "请求IP")
    private String requestIp;

    @ApiModelProperty(value = "创建成功返回token")
    private String token;

    @ApiModelProperty(value = "员工名称")
    private String employeeName;

    @ApiModelProperty(value = "所属组织Id")
    private Long orgId;

    @ApiModelProperty(value = "所属组织名称")
    private String orgName;

    @ApiModelProperty(value = "操作者")
    private Long creatorId;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "登录位置信息")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private LoginLocation loginLocation;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LoginLocation {
        private String longitude;
        private String latitude;
    }
}
