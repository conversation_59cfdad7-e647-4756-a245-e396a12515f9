package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.auth.entity.AuthTenAgent;
import com.sct.tiaojie.service.call.dto.TenAgentDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AuthTenAgentMapper extends BaseMapper<AuthTenAgent> {

    /**
     * 通过坐席ID获取坐席
     * @param accountId
     * @return
     */
    @Select("SELECT * FROM auth_ten_agent WHERE account_id = #{accountId}")
    AuthTenAgent getByAccountId(@Param("accountId") Long accountId);

    List<TenAgentDTO> list();
}
