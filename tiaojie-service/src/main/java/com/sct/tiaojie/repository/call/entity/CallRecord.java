package com.sct.tiaojie.repository.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sct.tiaojie.repository.mdt.entity.AIHandle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("外呼记录实体")
@TableName(value = "call_record",autoResultMap = true)
public class CallRecord extends AIHandle {

    @ApiModelProperty("记录ID")
    @TableId(value = "record_id", type = IdType.ASSIGN_ID)
    private Long recordId;

    @ApiModelProperty(value = "坐席号")
    private String agentId;

    @ApiModelProperty(value = "案件号")
    private Long caseId;

    @ApiModelProperty(value = "分机号")
    private String extensionId;

    @ApiModelProperty(value = "主叫电话")
    private String callerPhone;

    @ApiModelProperty(value = "被叫电话")
    private String calleePhone;

    @ApiModelProperty(value = "呼叫结果")
    private String callResult;

    @ApiModelProperty(value = "session")
    private String session;

    @ApiModelProperty(value = "服务提供商")
    private String providerName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty("呼叫开始时间")
    private String startTime;

    @ApiModelProperty("振铃开始时间")
    private String ringTime;

    @ApiModelProperty("呼叫接通时间")
    private String answerTime;

    @ApiModelProperty("呼叫结束时间")
    private String byeTime;

    @ApiModelProperty("录音文件")
    private String recordFile;

    @ApiModelProperty("业务类型")
    private String service;

    @ApiModelProperty("通话时长")
    private String timeLength;

    @ApiModelProperty("挂断原因")
    private String releaseCause;

    @ApiModelProperty("挂断类型:成功、失败等")
    private String typeResult;

    @ApiModelProperty("呼叫方式: 呼入、呼出等")
    private String usageMode;

    @ApiModelProperty("姓名")
    private String calleeName;

    @ApiModelProperty("当事人id列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> litigantIdList;

    @ApiModelProperty("与被申请人关系")
    private Integer contactRela;

    @ApiModelProperty(value = "电话类型", example = "1")
    private Integer phoneType;

    @ApiModelProperty(value = "调解员姓名")
    private String mediatorName;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty("振铃时长")
    private String ringLength;

    @ApiModelProperty(value = "所属组织")
    private String orgName;

    @ApiModelProperty(value = "所属部门id")
    private Long deptId;

    @ApiModelProperty(value = "所属部门")
    private String deptName;

    @ApiModelProperty(value = "调解员ID")
    private Long mediatorId;

    @ApiModelProperty(value = "ASR内容")
    private String asrContent;

    @ApiModelProperty(value = "ASR的原始json格式")
    private String asrJson;
}
