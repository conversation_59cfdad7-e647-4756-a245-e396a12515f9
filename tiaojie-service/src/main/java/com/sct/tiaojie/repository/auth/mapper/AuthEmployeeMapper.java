package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.auth.entity.AuthEmployee;
import com.sct.tiaojie.service.auth.bo.AuthEmployeePageBO;
import com.sct.tiaojie.service.auth.bo.EmployeeBO;
import com.sct.tiaojie.service.auth.dto.AuthEmployeeDTO;
import com.sct.tiaojie.service.auth.dto.AuthEmployeePageDTO;
import com.sct.tiaojie.service.auth.dto.MediatorDTO;
import com.sct.tiaojie.service.auth.dto.UserInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * <p>
 *   员工 Mapper 接口
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface AuthEmployeeMapper extends BaseMapper<AuthEmployee> {

    /**
     * 分页查询员工信息
     * @param page
     * @param param
     * @return
     */
    Page<AuthEmployeePageDTO> pageList(Page<AuthEmployeePageDTO> page, @Param("param") AuthEmployeePageBO param);

    /**
     * 获取用户详情
     * @param employeeId
     * @param companyId
     * @return
     */
    UserInfoDTO getUserInfo(@Param("employeeId") Long employeeId, @Param("companyId") Long companyId);

    /**
     * 查询所有调解员
     * @param companyId
     * @return
     */
    List<MediatorDTO> getMediator(@Param("companyId") Long companyId);

    /**
     * 获取部门下所有员工
     * @param deptId
     * @param companyId
     * @return
     */
    List<AuthEmployeeDTO> getUserByDept(@Param("deptId") Long deptId, @Param("companyId") Long companyId);

    /**
     * 获取调解员姓名
     * @param mediatorIds
     * @return
     */
    List<Map<String, Object>> getNameByIds(@Param("mediatorIds") List<Long> mediatorIds);

    /**
     * 简单查询员工
     * @param param
     * @return
     */
    List<AuthEmployeeDTO> getEmployee(@Param("param") EmployeeBO param);

    /**
     * 获取部门下所有的调解员
     * @param deptId
     * @param companyId
     * @return
     */
    List<AuthEmployeeDTO> getMediatorByDept(@Param("deptId") Long deptId, @Param("companyId") Long companyId);

    /**
     * 通过部门id列表获取所有的调解员
     * @param deptIdList
     * @param companyId
     * @return
     */
    List<AuthEmployeeDTO> getMediatorByDeptIdList(@Param("deptIdList") List<Long> deptIdList, @Param("companyId") Long companyId);

    /**
     * 分页查询员工
     * @param param
     * @return
     */
    List<AuthEmployeeDTO> batchListEmployees(@Param("param") EmployeeBO param);

    /**
     * 查询关联用户列表
     * @param employeeNumber
     * @return
     */
    LinkedList<UserInfoDTO> getRelevanceUserInfoList(@Param("employeeNumber")String employeeNumber);

    /**
     * 查询员工编号的数量
     * @param employeeNumber
     * @return
     */
    int checkRelevanceAccount(@Param("employeeNumber") String employeeNumber);

    /**
     * 禁用关联账号
     * @param employeeNumber
     */
    void updateRelevanceAccountStatus(@Param("employeeNumber") Long employeeNumber);
}
