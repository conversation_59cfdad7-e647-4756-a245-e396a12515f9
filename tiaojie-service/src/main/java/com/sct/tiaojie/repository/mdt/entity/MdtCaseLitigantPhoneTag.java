package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 当事人手机号标记
 */
@Data
@TableName("mdt_case_litigant_phone_tag")
public class MdtCaseLitigantPhoneTag implements Serializable {
    /**
     * 主见id
     */
    @TableId(value = "litigant_phone_id", type = IdType.ASSIGN_ID)
    private Long litigantPhoneId;
    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 当事人id
     */
    private Long litigantId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 号码检测标记
     * 0-未验证 1-已验证
     */
    private Integer checkTag;

    /**
     * 上次联系标记
     * 0-未联系 1-已联系
     */
    private Integer preTag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updaterId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
