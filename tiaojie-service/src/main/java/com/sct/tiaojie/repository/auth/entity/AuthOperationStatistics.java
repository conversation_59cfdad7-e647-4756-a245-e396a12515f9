package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户操作统计实体")
@TableName("auth_operation_statistics")
public class AuthOperationStatistics {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "统计ID", example = "1")
    private Long statisticsId;

    @ApiModelProperty("统计年")
    private String statisticYear;

    @ApiModelProperty("统计月")
    private String statisticMonth;

    @ApiModelProperty("统计日")
    private String statisticDay;

    @ApiModelProperty(value = "登陆名")
    private String loginName;

    @ApiModelProperty(value = "员工名称")
    private String employeeName;

    @ApiModelProperty(value = "员工所属组织ID")
    private Long orgId;

    @ApiModelProperty(value = "员工所属组织名称")
    private String orgName;

    @ApiModelProperty(value = "员工所属部门ID")
    private Long deptId;

    @ApiModelProperty(value = "员工所属部门名称")
    private String deptName;

    @ApiModelProperty(value = "操作序列")
    private String operationSequence;

    @ApiModelProperty(value = "操作时间")
    private Long operationDuration;

    @ApiModelProperty(value = "操作间隔")
    private Integer operationGap;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
