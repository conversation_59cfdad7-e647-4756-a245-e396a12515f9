package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SecretIdentity {

    @ApiModelProperty(value = "ID", example = "1")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "相关类型")
    private String relationType;

    @ApiModelProperty(value = "相关ID")
    private Long relationId;

    private String secretId;

    private String secretKey;

    @ApiModelProperty(value = "是否上锁")
    private Boolean isLocked;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;
}
