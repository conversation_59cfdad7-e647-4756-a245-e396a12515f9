package com.sct.tiaojie.repository.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description 签名信息实体
 * <AUTHOR>
 */
@Data
@TableName(value = "sign_info")
@ApiModel("签名信息实体")
public class SignInfo {
    

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "签名信息ID, 长度为12")
    private Long signInfoId;
    
    @ApiModelProperty(value = "person认证Key(手机号-身份证), 长度为300")
    private String personId;
    
    @ApiModelProperty(value = "安心签ID, 长度为128")
    private String trustSignId;
    
    @ApiModelProperty(value = "签名图片, 长度为500")
    private String signImageFile;
    
    @ApiModelProperty(value = "是否授权签名")
    private Boolean signAuthFlag;
    
    @ApiModelProperty(value = "安心签项目code, 长度为32")
    private String projectCode;
    
    @ApiModelProperty(value = "安心签预留手机号, 长度为32")
    private String trustSignMobile;
    
    @ApiModelProperty(value = "安心签状态(1: 启用；0：禁用)")
    private Boolean trustSignStatus;
    
    @ApiModelProperty(value = "安心签证件类型, 长度为64")
    private String trustSignCertType;
    
    @ApiModelProperty(value = "安心签证件号码, 长度为64")
    private String trustSignCertNo;
    
    @ApiModelProperty(value = "创建人账号ID, 长度为12")
    private Long createManId;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
} 
        