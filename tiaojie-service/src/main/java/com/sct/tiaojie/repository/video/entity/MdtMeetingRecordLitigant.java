package com.sct.tiaojie.repository.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("mdt_meeting_record_litigant")
public class MdtMeetingRecordLitigant {

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("会议记录ID")
    private Long recordId;

    @ApiModelProperty("案件ID")
    private String caseId;

    @ApiModelProperty("当事人ID")
    private String litigantId;

    @ApiModelProperty("当事人名称")
    private String litigantName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
