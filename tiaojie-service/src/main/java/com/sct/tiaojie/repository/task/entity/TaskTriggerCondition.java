package com.sct.tiaojie.repository.task.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * <p>
 * 触发条件项表 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          [CurrentDate]
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "触发条件项实体")
@TableName(value = "task_trigger_condition", autoResultMap = true)
@NoArgsConstructor
public class TaskTriggerCondition implements Serializable {

    @ApiModelProperty(value = "条件项ID", example = "1")
    @TableId(value = "condition_id", type = IdType.AUTO)
    private Long conditionId;

    @ApiModelProperty(value = "组ID", required = true, example = "1001")
    @TableField("group_id")
    private Long groupId;

    @ApiModelProperty(value = "模板模块ID", required = true, example = "1")
    @TableField("tmpl_module_id")
    private Integer tmplModuleId;

    @ApiModelProperty(value = "模板字段ID", required = true, example = "1")
    @TableField("tmpl_module_field_id")
    private Integer tmplModuleFieldId;

    @ApiModelProperty(value = "运算符(1:有值 2:等于 3:包含)", required = true, example = "1")
    @TableField("operator")
    private Integer operator;

    @ApiModelProperty(value = "目标值", example = "合同纠纷")
    @TableField("target_value")
    private String targetValue;

    @ApiModelProperty(value = "行间逻辑运算符(1:AND 2:OR)", example = "1")
    @TableField("logic_operator")
    private Integer logicOperator;
}
