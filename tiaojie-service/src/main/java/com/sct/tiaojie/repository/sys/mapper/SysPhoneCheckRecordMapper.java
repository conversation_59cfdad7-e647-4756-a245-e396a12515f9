package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysPhoneCheckRecord;
import com.sct.tiaojie.service.sys.bo.SysPhoneCheckRecordBo;
import com.sct.tiaojie.service.sys.dto.SysPhoneCheckRecordDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysPhoneCheckRecordMapper extends BaseMapper<SysPhoneCheckRecord> {

    /**
     * 查询案件联系人号码等信息
     * @param caseId 案件id
     *
     */
    SysPhoneCheckRecordDto getUserConcatPhone(@Param("caseId") Long caseId,@Param("litigantId")Long litigantId);

    /**
     * 更新数据
     *
     * @param sysPhoneCheckRecord 实例对象
     * @return 影响行数
     */
    int update(SysPhoneCheckRecord sysPhoneCheckRecord);

    /**
     * 批量新增数据
     *
     * @param entities List<SysPhoneCheckRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SysPhoneCheckRecord> entities);

    /**
     * 获取号码检测分页记录
     */
    Page<SysPhoneCheckRecordDto> getPhoneCheckPageLog(Page<SysPhoneCheckRecordDto> page,@Param("param") SysPhoneCheckRecordBo param);

    /**
     * 案件详情-号码检测记录
     * @param caseId 案件id
     */
    List<SysPhoneCheckRecordDto> getPhoneCheckLogByCaseId(@Param("caseId") Long caseId);

    /**
     * 查询号码检测记录
     * @param sysPhoneCheckRecordBo 查询条件
     */
    List<SysPhoneCheckRecordDto> selectPhoneCheckLogList(@Param("param") SysPhoneCheckRecordBo sysPhoneCheckRecordBo);
}
