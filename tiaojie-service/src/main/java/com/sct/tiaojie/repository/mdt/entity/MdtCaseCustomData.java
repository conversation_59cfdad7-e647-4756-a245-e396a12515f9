package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "案件自定义数据表")
@TableName("mdt_case_custom_data")
public class MdtCaseCustomData extends AIHandle{

    @ApiModelProperty(value = "ID")
    @TableId(value = "data_id", type = IdType.ASSIGN_ID)
    private Long dataId;

    @ApiModelProperty(value = "案件ID")
    private Long caseId;

    @ApiModelProperty(value = "模板自定义模块ID")
    private Integer tmplModuleId;

    @ApiModelProperty(value = "字段值")
    private String fieldValue;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "最近修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "最近修改用户", example = "1")
    private Long updaterId;
}
