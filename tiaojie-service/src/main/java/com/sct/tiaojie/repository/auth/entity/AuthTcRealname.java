package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description 腾讯实名认证信息实体
 * <AUTHOR>
 */
@Data
@ApiModel("腾讯实名认证信息实体")
public class AuthTcRealname implements Serializable {
    
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "实名认证ID")
    private Long realnameId;

    @ApiModelProperty(value = "实名服务类型(1:h5;2:E证通)")
    private Integer realnameServer;

    @ApiModelProperty(value = "实名用户ID")
    private Long userId;
    
    @ApiModelProperty(value = "关联业务ID, 长度为32")
    private String refId;

    @ApiModelProperty(value = "关联业务ID_2, 长度为32")
    private String refId2;
    
    @ApiModelProperty(value = "关联业务类型")
    private Integer refType;
    
    @ApiModelProperty(value = "平台ID")
    private Long platformId;
    
    @ApiModelProperty(value = "证件类型(内部字典)")
    private Integer idcardType;
    
    @ApiModelProperty(value = "tc实名业务token, 长度为255")
    private String bizToken;
    
    @ApiModelProperty(value = "biz_token过期时间")
    private LocalDateTime bizTokenExpireTime;
    
    @ApiModelProperty(value = "tc实名请求业务id, 长度为255")
    private String requestId;
    
    @ApiModelProperty(value = "tc实名认证url, 长度为700")
    private String realNameUrl;
    
    @ApiModelProperty(value = "实名结果code")
    private Long errCode;
    
    @ApiModelProperty(value = "实名结果提示, 长度为255")
    private String errMsg;
    
    @ApiModelProperty(value = "证件号, 长度为50")
    private String idCard;
    
    @ApiModelProperty(value = "姓名, 长度为50")
    private String name;

    @ApiModelProperty(value = "性别, 长度为5")
    private String ocrGender;
    
    @ApiModelProperty(value = "民族, 长度为50")
    private String ocrNation;
    
    @ApiModelProperty(value = "地址, 长度为255")
    private String ocrAddress;
    
    @ApiModelProperty(value = "证件权威机构, 长度为255")
    private String ocrAuthority;
    
    @ApiModelProperty(value = "证件有效期, 长度为255")
    private String ocrValidDate;
    
    @ApiModelProperty(value = "实名比对库源类型, 长度为10")
    private String compareLibType;
    
    @ApiModelProperty(value = "ocr证件正面照base64编码, 长度为10000")
    private String ocrFront;
    
    @ApiModelProperty(value = "ocr证件反面照base64编码, 长度为10000")
    private String ocrBack;
    
    @ApiModelProperty(value = "活体比对最佳帧base64编码, 长度为10000")
    private String bestFrame;
    
    @ApiModelProperty(value = "活体比对视频信息base64编码, 长度为10000")
    private String livenessVideo;

    @ApiModelProperty(value = "意愿核身问答模式结果,其中包含全程问题和回答音频，mp4格式（base64）, 长度为10000")
    private String intentionQuestionVideo;

    @ApiModelProperty(value = "意愿确认环节中录制视频的最佳帧（base64）, 长度为10000")
    private String intentionVerifyBestFrame;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "创建人ID")
    private Long createManId;
    
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty(value = "最后更新人ID")
    private Long updateManId;
    
} 
        