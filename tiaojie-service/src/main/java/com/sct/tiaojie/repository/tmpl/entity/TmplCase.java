package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("模板表")
@TableName("tmpl_case")
public class TmplCase {

    @TableId(value = "tmpl_id", type = IdType.AUTO)
    @ApiModelProperty("模板ID")
    private Integer tmplId;

    @ApiModelProperty("模板标题")
    private String tmplTitle;

    @ApiModelProperty("模板名称")
    private String tmplName;

    @ApiModelProperty("模板描述")
    private String tmplDesc;

    @ApiModelProperty("模板状态")
    private Integer tmplStatus;

    @ApiModelProperty("是否脱敏")
    private Integer isDesensitize;

    @ApiModelProperty("是否锁定，锁定表示已经导入过案件")
    private Integer isLocked;

    @ApiModelProperty("是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "委托方ID", example = "1")
    private Long entrustsId;

    @ApiModelProperty(value = "委托方名称")
    private String entrustsName;

    @ApiModelProperty(value = "案由标题")
    private String natureTitle;

    @ApiModelProperty(value = "企业ID", example = "1")
    private Long companyId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改者")
    private Long updaterId;
}
