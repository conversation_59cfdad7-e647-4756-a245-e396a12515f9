package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("sys_log")
@ApiModel("系统日志表")
public class SysLog {

    @TableId(value = "log_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "日志ID")
    private Long logId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "操作者姓名")
    private String accountName;

    @ApiModelProperty(value = "日志标题")
    private String logTitle;

    @ApiModelProperty(value = "日志参数")
    private String logParam;

    @ApiModelProperty(value = "请求IP")
    private String requestIp;

    @ApiModelProperty(value = "请求URL")
    private String requestUrl;

    @ApiModelProperty(value = "操作者")
    private Long creatorId;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "操作结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "修改者")
    private Long updaterId;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "员工ID")
    private Long accountId;

    @ApiModelProperty(value = "员工组织ID")
    private Long accountOrgId;

    @ApiModelProperty(value = "员工组织名称")
    private String accountOrgName;

    @ApiModelProperty(value = "员工部门ID")
    private Long accountDeptId;

    @ApiModelProperty(value = "员工部门名称")
    private String accountDeptName;

    @ApiModelProperty(value = "操作方式")
    private String operateType;
}
