package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("auth_litigant_login_log")
@ApiModel("当事人登录记录实体")
public class AuthLitigantLoginLog {

    @ApiModelProperty(value = "记录ID", example = "1")
    @TableId(type = IdType.ASSIGN_ID)
    private Long logid;

    @ApiModelProperty(value = "登录者姓名")
    private String loginName;

    @ApiModelProperty(value = "登录者电话")
    private String mobile;

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;

    @ApiModelProperty(value = "IP地址")
    private String ip;

    @ApiModelProperty(value = "登录状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
