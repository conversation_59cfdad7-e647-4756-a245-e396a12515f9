package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/4/21 10:59
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mdt_document")
public class MdtDocument {
    @ApiModelProperty(value = "文书ID")
    @TableId(value = "document_id",type = IdType.ASSIGN_ID)
    private Long documentId;

    @ApiModelProperty(value = "文书名称")
    private String documentName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "子文件夹目录")
    private String subFolder;

    @ApiModelProperty(value = "文书驼峰英文名称")
    private String documentCamelname;

    @ApiModelProperty(value = "是否显示")
    private Boolean hidden;
}
