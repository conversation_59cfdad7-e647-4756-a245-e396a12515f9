package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysTaskRunLog;
import com.sct.tiaojie.service.sys.bo.SysTaskRunLogPageBO;
import com.sct.tiaojie.service.sys.dto.SysTaskRunLogDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/***********************************************************************************************************************
 * <p>
 *   任务运行日志 Mapper 接口
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-18
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface SysTaskRunLogMapper extends BaseMapper<SysTaskRunLog> {

    Page<SysTaskRunLogDTO> pageQuery(@Param("page") Page<SysTaskRunLogDTO> page, @Param("param") SysTaskRunLogPageBO param, @Param("taskType") Integer taskType, @Param("companyId") Long companyId);
}
