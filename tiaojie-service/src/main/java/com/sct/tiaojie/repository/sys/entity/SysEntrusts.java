package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel("委托方实体")
@Data
@TableName("sys_entrusts")
public class SysEntrusts implements Serializable {

    @TableId(value = "entrusts_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("委托方ID")
    private Long entrustsId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty("委托方姓名")
    @Length(min = 0, max = 50, message = "委托方姓名格式错误,最大长度为50")
    private String entrustsName;

    @ApiModelProperty("委托方联系人姓名")
    @Length(min = 0, max = 50, message = "委托方联系人姓名格式错误,最大长度为50")
    private String contactsName;

    @ApiModelProperty("委托方电话")
    @Length(min = 0, max = 50, message = "委托方电话格式错误,最大长度为50")
    private String entrustsMobile;

    @ApiModelProperty("委托方邮箱")
    @Length(min = 0, max = 50, message = "委托方邮箱格式错误,最大长度为50")
    private String entrustsEmail;

    @ApiModelProperty("委托方地址")
    @Length(min = 0, max = 200, message = "委托方地址格式错误,最大长度为200")
    private String entrustsAddress;

    @ApiModelProperty("备注")
    @Length(min = 0, max = 255, message = "备注格式错误,最大长度为255")
    private String remark;

    @ApiModelProperty("委托方状态")
    private Boolean entrustsStatus;

    @ApiModelProperty("委托方编码")
    private String entrustsCode;

    @ApiModelProperty("当前案件编号")
    private Integer nextCaseNo;

    @ApiModelProperty("调解时效(天)")
    private Integer timeLimit;

    @ApiModelProperty("委托方简称")
    private String entrustsShortName;


    //----------------------新增字段-----------

    @ApiModelProperty("案源方类型")
    private String entrustsType;

    @ApiModelProperty("案源方联系方式")
    private String contactWay;

    @ApiModelProperty("调解时效启用状态,true/false")
    private Boolean timeLimitStatus;

    @ApiModelProperty("是否进行结案审核,true/false")
    private Boolean closeCaseAuditFlag;


    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

}
