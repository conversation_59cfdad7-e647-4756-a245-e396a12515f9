package com.sct.tiaojie.repository.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description 文件签名信息实体
 * <AUTHOR>
 */
@Data
@TableName(value = "sign_file_info")
@ApiModel("文件签名信息实体")
public class SignFileInfo {

    @ApiModelProperty(value = "主键ID, 长度为12")
    @TableId(type = IdType.ASSIGN_ID)
    private Long fileSignInfoId;

    @ApiModelProperty(value = "被签名文件全路径, 长度为500")
    private String filePath;

    @ApiModelProperty(value = "关联业务ID, 长度为12")
    private Long refId;

    @ApiModelProperty(value = "文书类型：结案文书，中止文书，庭审笔录")
    private Integer fileNature;

    @ApiModelProperty(value = "签名二维码图片路径, 长度为500")
    private String fileSignQrcodeFilePath;

    @ApiModelProperty(value = "状态：1签名中，2待签名，3已完成, 4已作废")
    private Integer status;

    @ApiModelProperty(value = "是否可以签名")
    private Boolean canSignFlag;

    @ApiModelProperty(value = "签名完成时间")
    private LocalDateTime signCompleteTime;

    @ApiModelProperty(value = "更新人ID, 长度为12")
    private Long updateManId;

    @ApiModelProperty(value = "创建人ID, 长度为12")
    private Long createManId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

} 
        