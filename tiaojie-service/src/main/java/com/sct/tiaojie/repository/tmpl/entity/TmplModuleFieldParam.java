package com.sct.tiaojie.repository.tmpl.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("文件模板字段映射参数")
public class TmplModuleFieldParam {
    @ApiModelProperty("模板模块字段ID")
    private Integer tmplModuleFieldId;

    @ApiModelProperty("模板ID")
    private Integer tmplId;

    @ApiModelProperty("模板模块ID")
    private Integer tmplModuleId;

    @ApiModelProperty("基础模块字段ID")
    private Integer basisModuleFieldId;

    @ApiModelProperty("对应数据表字段名")
    private String fieldName;

    @ApiModelProperty("字段标题")
    private String fieldTitle;

    @ApiModelProperty("字段描述")
    private String fieldDesc;

    @ApiModelProperty("字段序号")
    private Integer fieldSn;

    @ApiModelProperty("字段ui")
    private String fieldUi;

    @ApiModelProperty("字段数据类型")
    private String fieldDataType;

    @ApiModelProperty("字段取值")
    private String fieldValues;

    @ApiModelProperty("是否必须")
    private Integer isRequire;

    @ApiModelProperty("是否结案批准校验")
    private Integer isApprovalValidated;

    @ApiModelProperty("是否可修改")
    private Integer isUpdate;

    @ApiModelProperty("是否脱敏")
    private Integer isDesensitize;

    @ApiModelProperty("脱敏规则")
    private String desensitizeRule;

    @ApiModelProperty("是否校验数据格式")
    private Integer isValidated;

    @ApiModelProperty("扩展字段名")
    private String toCaseExtField;

    @ApiModelProperty("字段语义")
    private String fileSemantics;

    @ApiModelProperty("是否失效")
    private Integer isDisable;

    @ApiModelProperty("呈现格式")
    private String showFormat;

    @ApiModelProperty("验证格式，规则")
    private String validation;
}
