package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 调解案件催记信息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-24
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "调解案件催记信息实体")
public class MdtCaseCollectRecord extends AIHandle implements Serializable {

    @ApiModelProperty(value = "主键", example = "1")
    @TableId(type = IdType.ASSIGN_ID)
    private Long recordId;

    @ApiModelProperty(value = "案件ID", example = "1")
    private Long caseId;

    @ApiModelProperty(value = "电话类型", example = "1")
    private Integer phoneType;

    @ApiModelProperty(value = "联系用户关系", example = "1")
    private Integer contactRela;

    @ApiModelProperty(value = "调解结果旧", example = "1")
    private Integer mdtResult;

    @ApiModelProperty(value = "通话记录,最大长度为1000")
    @Length(min = 0, max = 1000, message = "通话记录格式错误,最大长度为1000")
    private String callRecord;

    @ApiModelProperty(value = "减免金额")
    private BigDecimal reducteAmount;

    @ApiModelProperty(value = "减免类型", example = "1")
    private Integer reducteType;

    @ApiModelProperty(value = "调解结果新", example = "1")
    private String mediateResult;

    @ApiModelProperty(value = "下次跟进日期")
    private LocalDateTime nextDoTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTm;

    @ApiModelProperty(value = "调解员员工号")
    private Long mediatorEmployId;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "最近修改时间")
    private LocalDateTime updateTm;

    @ApiModelProperty(value = "最近修改用户", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "跟进人账号")
    private String account;

    @ApiModelProperty(value = "跟进人姓名")
    private String accountName;

    @ApiModelProperty(value = "联络结果")
    private Integer contactResult;



    @ApiModelProperty(value = "联系电话,最大长度为100")
    @Length(min = 0, max = 100, message = "联系电话格式错误,最大长度为100")
    private String contactPhone;

    @ApiModelProperty(value = "联系用户,最大长度为50")
    @Length(min = 0, max = 50, message = "联系用户格式错误,最大长度为50")
    private String contactorUser;

    @ApiModelProperty(value = "联系用户当事人ID")
    private Long litigantId;

    @ApiModelProperty(value = "调解状态")
    private String mediateStatus;

    @ApiModelProperty(value = "当事人身份")
    private String identityType;

    @ApiModelProperty(value = "调解时间")
    private LocalDateTime mediateTime;

    @ApiModelProperty(value = "调解类型")
    private String mediateType;

    @ApiModelProperty(value = "是否接听")
    private String isAnswer;

    @ApiModelProperty(value = "意向金额")
    private BigDecimal intentionAmount;

    @ApiModelProperty(value = "其他诉求")
    private String otherDemand;

    @ApiModelProperty(value = "需配合内容")
    private String needHelpContent;

    @ApiModelProperty(value = "调解员姓名")
    private String mediatorName;


    @ApiModelProperty(value = "预计调成日期")
    private LocalDateTime expectedSuccessDate;

}