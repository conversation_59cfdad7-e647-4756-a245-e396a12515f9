package com.sct.tiaojie.repository.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("会议录屏文件表")
public class MdtMeetingVideo {

    @ApiModelProperty("ID")
    @TableId(value = "video_id", type = IdType.ASSIGN_ID)
    private Long videoId;

    @ApiModelProperty("会议记录ID")
    private Long recordId;

    @ApiModelProperty("调解ID")
    private String mediationId;

    @ApiModelProperty("会议ID")
    private String meetingId;

    @ApiModelProperty("视频文件地址")
    private String filePath;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}
