package com.sct.tiaojie.repository.common.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/***********************************************************************************************************************
 * @copyright 2019 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR> Panda
 * @date 2020-06-30
 * @version V1.0
 * @description
 **********************************************************************************************************************/
@Repository
public interface CommonMapper {

    /**
     * 从数据库拿当前时间字符串
     * @return
     */
    String nowDateTimeStr();

    /**
     * 从数据库拿当前时间
     * @return
     */
    LocalDateTime nowDateTime();


    /**
     * 执行sql
     *
     * @param sql
     */
    @Update("${sql_script}")
    void excuteSql(@Param("sql_script") String sql);
}
