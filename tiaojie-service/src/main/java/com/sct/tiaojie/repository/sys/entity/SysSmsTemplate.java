package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;


/***********************************************************************************************************************
 * <p>
 * 短信模板信息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2021-06-05
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "短信模板信息实体")
public class SysSmsTemplate implements Serializable {

    @ApiModelProperty(value = "主键", example = "1")
    @TableId(value = "sms_template_id", type = IdType.AUTO)
    private Integer smsTemplateId;

    @ApiModelProperty(value = "模板类型", example = "1")
    private String templateType;

    @ApiModelProperty(value = "模板内容,最大长度为1000")
    @Length(min = 0, max = 1000, message = "模板内容格式错误,最大长度为1000")
    private String templateContent;

    @ApiModelProperty(value = "备注,最大长度为255")
    @Length(min = 0, max = 255, message = "备注格式错误,最大长度为255")
    private String remark;

}