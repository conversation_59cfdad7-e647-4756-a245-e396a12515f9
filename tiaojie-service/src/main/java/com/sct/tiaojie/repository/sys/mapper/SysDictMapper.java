package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysDict;
import com.sct.tiaojie.service.sys.dto.SysDictDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface SysDictMapper extends BaseMapper<SysDict> {

    /**
     * 获取所有字典值
     * @return
     */
    List<SysDictDTO> getAllSysDictWithData();

    /**
     * 分页查询
     * @param page
     * @param param
     * @param companyId
     * @return
     */
    Page<SysDictDTO> pageList(Page<SysDictDTO> page, @Param("param") SysDictDTO param, @Param("companyId") Long companyId);
}
