package com.sct.tiaojie.repository.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 呼出统计临时表
 * @Author: llz
 * @Date: 2024/7/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="mdt_call_statistic_temp")
public class CallStatisticTemp implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 统计ID
     */
    @TableId(value = "statistic_id", type = IdType.ASSIGN_ID)
    private Long statisticId;

    /**
     * 呼叫类型: 1-呼入  2-呼出
     */
    @TableField(value = "call_type")
    private Integer callType;

    /**
     * 统计-年
     */
    @TableField(value = "statistic_year")
    private String statisticYear;

    /**
     * 统计月
     */
    @TableField(value = "statistic_month")
    private String statisticMonth;

    /**
     * 统计-日
     */
    @TableField(value = "statistic_day")
    private String statisticDay;

    /**
     * 统计-时
     */
    @TableField(value = "statistic_hour")
    private String statisticHour;

    /**
     * 调解组织ID
     */
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 调解组织名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 部门名称
     */
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 案由
     */
    @TableField(value = "case_cause")
    private String caseCause;

    /**
     * 坐席ID
     */
    @TableField(value = "agent_id")
    private String agentId;

    /**
     * 调解员ID
     */
    @TableField(value = "mediator_id")
    private Long mediatorId;

    /**
     * 调解员姓名
     */
    @TableField(value = "mediator_name")
    private String mediatorName;

    /**
     * 通话总数
     */
    @TableField(value = "call_count")
    private Long callCount;

    /**
     * 接通总数
     */
    @TableField(value = "through_count")
    private Long throughCount;

    /**
     * 有效接通总数
     */
    @TableField(value = "valid_call_count")
    private Long validCallCount;

    /**
     * 有效通话总时长
     */
    @TableField(value = "valid_call_time_total")
    private Long validCallTimeTotal;

    /**
     * 通话总时长
     */
    @TableField(value = "call_time_total")
    private Long callTimeTotal;

    /**
     * 未接听数
     */
    @TableField(value = "call_missed_count")
    private Long callMissedCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 删除标识
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

}