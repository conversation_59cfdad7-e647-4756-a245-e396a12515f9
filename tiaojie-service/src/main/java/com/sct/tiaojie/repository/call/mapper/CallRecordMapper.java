package com.sct.tiaojie.repository.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.call.entity.CallRecord;
import com.sct.tiaojie.repository.call.entity.CallStatistic;
import com.sct.tiaojie.service.call.bo.CallRecordPageBO;
import com.sct.tiaojie.service.call.dto.CallRecordPageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface CallRecordMapper extends BaseMapper<CallRecord> {

    /**
     * 分页查询呼叫记录
     * @param page
     * @param param
     * @return
     */
    Page<CallRecordPageDTO> pageList(Page<CallRecordPageDTO> page, @Param("param") CallRecordPageBO param);

    /**
     * 查询指定id记录
     * @param recordIds
     * @return
     */
    List<Map<String, Object>> selectAll(@Param("recordIds") List<Long> recordIds);

    List<Long> getRecordIds(@Param("param") CallRecordPageBO param);

    /**
     * 获取已经保存的session
     * @param sessionIds
     * @return
     */
    List<String> getSameIds(@Param("sessionIds") List<String> sessionIds);

    /**
     * 查询案件下外呼记录
     * @param caseId
     * @return
     */
    List<CallRecordPageDTO> list(@Param("caseId") Long caseId);

    /**
     * 获取呼叫统计记录
     * @return
     */
    List<CallStatistic> getCallStatistic();

    List<CallRecordPageDTO> selectCallRecordList(@Param("param") CallRecordPageBO callRecordPageBO);
}
