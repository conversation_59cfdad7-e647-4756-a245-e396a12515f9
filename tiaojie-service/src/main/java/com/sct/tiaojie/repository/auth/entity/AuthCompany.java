package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 公司 实体
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "公司实体")
@TableName("auth_company")
public class AuthCompany implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "公司ID", example = "1")
    private Long companyId;

    @ApiModelProperty(value = "公司名称,最大长度为255")
    @Length(min = 0, max = 255, message = "公司名称格式错误,最大长度为255")
    private String companyName;

    @ApiModelProperty(value = "父公司ID", example = "1")
    private Long parentCompanyId;

    @ApiModelProperty(value = "级别", example = "1")
    private Integer treeLevel;

    @ApiModelProperty(value = "公司状态")
    @TableField("company_status")
    private Integer companyStatus;

    @ApiModelProperty(value = "公司备注,最大长度为1000")
    @Length(min = 0, max = 1000, message = "公司备注格式错误,最大长度为1000")
    private String companyRemark;

    @ApiModelProperty(value = "公司图标,最大长度为255")
    @Length(min = 0, max = 255, message = "公司图标格式错误,最大长度为255")
    private String companyIcon;

    @ApiModelProperty(value = "公司地址,最大长度为255")
    @Length(min = 0, max = 255, message = "公司图标格式错误,最大长度为255")
    private String address;

    @ApiModelProperty(value = "公司邮箱,最大长度为100")
    @Length(min = 0, max = 100, message = "公司图标格式错误,最大长度为100")
    private String email;

    @ApiModelProperty(value = "公司电话,最大长度为100")
    @Length(min = 0, max = 100, message = "公司图标格式错误,最大长度为100")
    private String mobile;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "删除时间")
    private LocalDateTime deleteTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "是否平台组织", example = "1")
    private Integer isplateform;

    @ApiModelProperty(value = "企业类型", example = "1")
    private String companyType;

}