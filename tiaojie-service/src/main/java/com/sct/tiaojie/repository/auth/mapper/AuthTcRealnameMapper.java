package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.auth.entity.AuthTcRealname;
import com.sct.tiaojie.service.auth.dto.AuthTcRealnameSampleDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 腾讯实名认证信息Repository
 */
@Mapper
public interface AuthTcRealnameMapper extends BaseMapper<AuthTcRealname> {

    /**
     * 不查大字段
     *
     * @return
     */
    @Select(" select " +
            " realname_id, realname_server, user_id, ref_id, ref_id2, ref_type, platform_id, idcard_type, biz_token, biz_token_expire_time, request_id, real_name_url, err_code, err_msg, id_card, name, ocr_gender, ocr_nation, ocr_address, ocr_authority, ocr_valid_date, compare_lib_type " +
            " from auth_tc_realname where ref_id = #{refId} and ref_type = #{refType} and platform_id = #{platformId}")
    List<AuthTcRealnameSampleDTO> getByRefIdType(@Param("refId") String refId, @Param("refType") int refType, @Param("platformId") Long platformId);

    @Select(" select " +
            " realname_id, realname_server, user_id, ref_id, ref_id2, ref_type, platform_id, idcard_type, biz_token, biz_token_expire_time, request_id, real_name_url, err_code, err_msg, id_card, name, ocr_gender, ocr_nation, ocr_address, ocr_authority, ocr_valid_date, compare_lib_type " +
            " from auth_tc_realname where ref_id = #{refId} and ref_type = #{refType}")
    List<AuthTcRealnameSampleDTO> getByRefIdType(@Param("refId") String refId, @Param("refType") int refType);


    /**
     * 不查大字段
     *
     * @return
     */
    @Select(" select " +
            " realname_id, realname_server, user_id, ref_id, ref_id2, ref_type, platform_id, idcard_type, biz_token, biz_token_expire_time, request_id, real_name_url, err_code, err_msg, id_card, name, ocr_gender, ocr_nation, ocr_address, ocr_authority, ocr_valid_date, compare_lib_type " +
            " from auth_tc_realname where ref_id2 = #{refId2} and ref_type = #{refType} and platform_id = #{platformId}")
    List<AuthTcRealnameSampleDTO> getByRefId2Type(@Param("refId2") String refId2, @Param("refType") int refType, @Param("platformId") Long platformId);

    /**
     * 不查大字段
     *
     * @return
     */
    @Select(" select " +
            " realname_id, realname_server, user_id, ref_id, ref_id2, ref_type, platform_id, idcard_type, biz_token, biz_token_expire_time, request_id, real_name_url, err_code, err_msg, id_card, name, ocr_gender, ocr_nation, ocr_address, ocr_authority, ocr_valid_date, compare_lib_type " +
            " from auth_tc_realname where realname_id = #{id}")
    AuthTcRealnameSampleDTO getById(@Param("id") Long id);

    /**
     * 不查大字段
     *
     * @param bizToken
     * @return
     */
    @Select(" select " +
            " realname_id, realname_server, user_id, ref_id, ref_id2, ref_type, platform_id, idcard_type, biz_token, biz_token_expire_time, request_id, real_name_url, err_code, err_msg, id_card, name, ocr_gender, ocr_nation, ocr_address, ocr_authority, ocr_valid_date, compare_lib_type " +
            " from auth_tc_realname where biz_token = #{bizToken} ")
    AuthTcRealnameSampleDTO getByBizToken(@Param("bizToken") String bizToken);
}
        