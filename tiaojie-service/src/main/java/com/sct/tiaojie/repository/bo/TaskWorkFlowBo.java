package com.sct.tiaojie.repository.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.google.gson.JsonArray;
import com.sct.tiaojie.common.validate.AddGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel("流程业务对象")
public class TaskWorkFlowBo implements Serializable {

    @ApiModelProperty(value = "流程ID", example = "1")
    private Long workflowId;

    @ApiModelProperty(value = "流程名称", required = true)
    @NotNull(message = "流程名称不能为空",groups = {AddGroup.class})
    private String workflowName;

    @ApiModelProperty(value = "案源方ID", required = true)
    @NotNull(message = "案源方ID不能为空",groups = {AddGroup.class})
    private Long entrustsId;

    @ApiModelProperty(value = "案件信息模板ID", example = "2001")
    private Long templateId;

    @ApiModelProperty(value = "案源方名称", example = "10001")
    private String entrustsName;

    @ApiModelProperty(value = "案件信息模板名称", example = "2001")
    private String templateName;

    @ApiModelProperty(value = "涉及任务类型")
    private Long[] taskType;

    @ApiModelProperty(value = "涉及任务类型的配置id")
    private Long[] taskWorkFlowdIds;

    @ApiModelProperty(value = "涉及任务负责人")
    private Long[] mdtEntrustOrgUserIds;

    @ApiModelProperty(value = "案源方ids")
    private Long[] entrustsIds;

    @ApiModelProperty(value = "触发条件(1:案件导入 2:案件分派)")
    private Integer triggerCondition;

    @ApiModelProperty(value = "状态", required = true)
    @NotNull(message = "状态不能为空",groups = {AddGroup.class})
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人ID", example = "1001")
    private Long creatorId;

    @ApiModelProperty(value = "更新人ID", example = "1002")
    private Long updaterId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
