package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel(value = "失联修复获取号码表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mdt_repair_phone")
public class MdtRepairPhone {

    @ApiModelProperty(value = "ID")
    @TableId(value = "repair_phone_id",type = IdType.ASSIGN_ID)
    private Long repairPhoneId;

    @ApiModelProperty("批次Id")
    private Long repairId;

    @ApiModelProperty(value = "失联修复当事人表Id")
    private Long repairLitigantId;

    @ApiModelProperty("失联修复记录Id")
    private Long repairRecordId;

    @ApiModelProperty("案件Id")
    private Long caseId;

    @ApiModelProperty("当事人ID")
    private Long litigantId;

    @ApiModelProperty("主叫号")
    private String callNo;

    @ApiModelProperty("虚拟号")
    private String virtualNo;

    @ApiModelProperty("绑定标识")
    private String bindId;

    @ApiModelProperty("获取结果")
    private String acquireResult;

    @ApiModelProperty("获取状态")
    private String acquireStatus;

    @ApiModelProperty("号码获取时间")
    private LocalDateTime numberAcquireTime;

    @ApiModelProperty("失败错误码")
    private String failCode;

    @ApiModelProperty("失败错误原因")
    private String failMsg;

    @ApiModelProperty("号码过期时间")
    private LocalDateTime numberExpireTime;

}
