package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtInstruments;
import com.sct.tiaojie.service.mdt.dto.MdtInstrumentsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 案件文书;(mdt_instruments)表数据库访问层
 *
 * <AUTHOR> llj
 * @date : 2024-7-31
 */
@Mapper
public interface MdtInstrumentsMapper extends BaseMapper<MdtInstruments> {

    /**
     * 通过ID查询单条数据（使用MyBatis-Plus注解方式）
     *
     * @param instrumentsId 主键
     * @return 实例对象
     */
    MdtInstrumentsDTO queryById(Long instrumentsId);

    /**
     * 根据案件ID查询文书列表（使用MyBatis-Plus注解方式）
     *
     * @param caseId 案件ID
     * @return 文书列表
     */
    List<MdtInstrumentsDTO> getListByCaseId(@Param("caseId") Long caseId);
}
