package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 菜单 实体
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "菜单实体")
public class SysMenu implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "菜单ID", example = "1")
    private Long menuId;

    @ApiModelProperty(value = "父菜单ID", example = "1")
    private Long parentMenuId;

    @ApiModelProperty(value = "菜单名称,最大长度为100")
    @Length(min = 0, max = 100, message = "菜单名称格式错误,最大长度为100")
    private String menuName;

    @ApiModelProperty(value = "菜单归属主模块,最大长度为100")
    @Length(min = 0, max = 100, message = "菜单归属主模块格式错误,最大长度为100")
    private String menuModule;

    @ApiModelProperty(value = "菜单类型 1:左侧显示菜单;2:左侧不显示的，隐藏菜单;3:左侧不显示的，功能菜单", example = "1")
    private Integer menuType;

    @ApiModelProperty(value = "级别", example = "1")
    private Integer treeLevel;

    @ApiModelProperty(value = "菜单图标,最大长度为60")
    @Length(min = 0, max = 60, message = "菜单图标格式错误,最大长度为60")
    private String menuIcon;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sortNo;

    @ApiModelProperty(value = "菜单链接,最大长度为255")
    @Length(min = 0, max = 255, message = "菜单链接格式错误,最大长度为255")
    private String menuHref;

    @ApiModelProperty(value = "菜单标志,最大长度为255")
    @Length(min = 0, max = 255, message = "菜单标志格式错误,最大长度为255")
    private String menuFlag;

    @ApiModelProperty(value = "菜单路由名,最大长度为255")
    @Length(min = 0, max = 255, message = "菜单路由名格式错误,最大长度为255")
    private String menuRouteName;

    @ApiModelProperty(value = "启动标志")
    @TableField("enable_flag")
    private Boolean enableFlag;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

}