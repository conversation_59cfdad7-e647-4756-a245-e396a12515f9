package com.sct.tiaojie.repository.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务实例分页查询响应DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "任务实例分页查询响应DTO")
public class TaskInstanceDTO implements Serializable {

    @ApiModelProperty(value = "实例ID", example = "1")
    private Long instanceId;

    @ApiModelProperty(value = "案件ID", example = "1001")
    private Long caseId;

    @ApiModelProperty(value = "流程ID", example = "2001")
    private Long workflowId;

    @ApiModelProperty(value = "配置ID", example = "3001")
    private Long configId;

    @ApiModelProperty(value = "任务类型", example = "MEDIATION", notes = "任务类型代码")
    private Integer taskType;

    @ApiModelProperty(value = "任务时限（时）", example = "24", notes = "单位：时")
    private Integer timeLimit;

    //截至时间
    @ApiModelProperty(value = "截至时间")
    private LocalDateTime deadline;

    @ApiModelProperty(value = "任务负责人ID集合", notes = "多个负责人的ID列表")
    private List<Long> managerIdList;

    @ApiModelProperty(value = "任务负责人名字", notes = "多个负责人的名字用,分割")
    private String managerNames;

    @ApiModelProperty(value = "当前状态(1:未开始 2:进行中 3:已完成 4:异常)", example = "1")
    private Integer taskStatus;

    @ApiModelProperty(value = "任务状态名称", example = "未开始", notes = "状态显示名称")
    private String taskStatusName;

    @ApiModelProperty(value = "备注", example = "需要优先处理")
    private String remark;

    @ApiModelProperty(value = "任务发起人ID", example = "10001", notes = "发起任务的账号ID")
    private Long assignAccountId;

    @ApiModelProperty(value = "发起人姓名")
    private String assignAccountName;

    @ApiModelProperty(value = "分派登记时间", notes = "任务发起时间")
    private LocalDateTime assignTime;

    @ApiModelProperty(value = "完成人id", example = "10002")
    private Long finishAccountId;

    @ApiModelProperty(value = "完成人姓名")
    private String finishAccountName;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "创建人", example = "10001")
    private Long creatorId;

    @ApiModelProperty(value = "更新人", example = "10002")
    private Long updaterId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
