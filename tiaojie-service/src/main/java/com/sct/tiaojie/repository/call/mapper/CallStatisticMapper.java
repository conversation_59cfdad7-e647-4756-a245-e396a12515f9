package com.sct.tiaojie.repository.call.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.call.entity.CallStatistic;
import com.sct.tiaojie.repository.call.entity.CallStatisticOverview;
import com.sct.tiaojie.service.call.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【mdt_call_statistic(呼出统计表)】的数据库操作Mapper
* @createDate 2024-07-15 10:25:24
*/
@Mapper
public interface CallStatisticMapper extends BaseMapper<CallStatistic> {

    /**
     * 获取呼出统计数据
     * @param callStatisticIds
     * @return
     */
    List<CallStatisticOverview> getCallStatisticOverviewList(@Param("callStatisticIds") List<Long> callStatisticIds, @Param("dateStart") String dateStart, @Param("dateEnd") String dateEnd);

    CallStatisticDTO getCallStatisticDTO(@Param("callStatisticIds") List<Long> callStatisticIds, @Param("dateStart") String dateStart, @Param("dateEnd") String dateEnd);

    List<ValidAnswerRateDTO> getValidAnswerRateDTO(@Param("callStatisticIds") List<Long> callStatisticIds, @Param("groupCondition") Integer groupCondition, @Param("dateStart") String dateStart, @Param("dateEnd") String dateEnd);

    List<CallsPerPersonDTO> getCallsPerPersonDTO(@Param("callStatisticIds") List<Long> callStatisticIds, @Param("groupCondition") Integer groupCondition, @Param("dateStart") String dateStart, @Param("dateEnd") String dateEnd);

    List<AvgCallTimeDTO> getAvgCallTimeDTO(@Param("callStatisticIds") List<Long> callStatisticIds, @Param("groupCondition") Integer groupCondition, @Param("dateStart") String dateStart, @Param("dateEnd") String dateEnd);

    List<AverageEffectiveCallDTO> getAverageEffectiveCallDTO(@Param("callStatisticIds") List<Long> callStatisticIds, @Param("groupCondition") Integer groupCondition, @Param("dateStart") String dateStart, @Param("dateEnd") String dateEnd);

    List<CallFailureRateDTO> getCallFailureRateDTO(@Param("callStatisticIds") List<Long> callStatisticIds, @Param("groupCondition") Integer groupCondition, @Param("dateStart") String dateStart, @Param("dateEnd") String dateEnd);
}




