package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.auth.entity.AuthUser;
import com.sct.tiaojie.service.auth.bo.UserMgrPageBO;
import com.sct.tiaojie.service.auth.dto.UserMgrDetailDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @description 用户信息Repository
 * <AUTHOR>
 */
@Mapper
public interface AuthUserMapper extends BaseMapper<AuthUser> {

    Page<UserMgrDetailDTO> pageUserMgrList(@Param("page") Page<UserMgrDetailDTO> page,
                                           @Param("param") UserMgrPageBO param);


    @Select("SELECT * FROM auth_user WHERE biz_user_id = #{bizUserId}")
    AuthUser selectByBizUserId(@Param("bizUserId") String bizUserId);
}
        