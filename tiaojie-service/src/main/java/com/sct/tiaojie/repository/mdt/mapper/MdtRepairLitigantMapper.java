package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtRepairLitigant;
import com.sct.tiaojie.service.mdt.bo.MdtContactRepairNewPageBo;
import com.sct.tiaojie.service.mdt.bo.MdtContactRepairPageBo;
import com.sct.tiaojie.service.mdt.dto.MdtRepairRecordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MdtRepairLitigantMapper extends BaseMapper<MdtRepairLitigant> {

    /**
     * 失联修复记录分页信息
     * @param page
     * @param param
     */
    Page<MdtRepairRecordDTO> pageRepairLogNew(@Param("page") Page<MdtRepairRecordDTO> page, @Param("param") MdtContactRepairNewPageBo param);
}
