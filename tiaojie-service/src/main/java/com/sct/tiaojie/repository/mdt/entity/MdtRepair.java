package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel(value = "失联修复批次表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mdt_repair")
public class MdtRepair {

    @ApiModelProperty(value = "失联修复批次ID")
    @TableId(value = "repair_id",type = IdType.ASSIGN_ID)
    private Long repairId;

    @ApiModelProperty("发起人Id")
    private Long accountId;

    @ApiModelProperty("发起人名称")
    private String accountName;

    @ApiModelProperty("发起组织Id")
    private Long companyId;

    @ApiModelProperty("发起组织名称")
    private String companyName;

    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty("批次修复状态")
    private String repairStatus;

    @ApiModelProperty("修复完成时间")
    private LocalDateTime repairCompleteTime;

    @ApiModelProperty("是否批量")
    private Integer isBatch;
}
