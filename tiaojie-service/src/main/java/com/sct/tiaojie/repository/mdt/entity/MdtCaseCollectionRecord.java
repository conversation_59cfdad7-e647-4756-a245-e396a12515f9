package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("mdt_case_collection_record")
public class MdtCaseCollectionRecord {

    @ApiModelProperty(value = "收案ID", example = "1")
    @TableId(value = "collection_id", type = IdType.ASSIGN_ID)
    private Long collectionId;

    @ApiModelProperty(value = "案件信息")
    private String caseInfo;

    @ApiModelProperty(value = "接受方式")
    private String collectionType;

    @ApiModelProperty(value = "案件创建结果")
    private String collectionResult;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "创建人所属组织")
    private String companyName;

    @ApiModelProperty(value = "所属案源方")
    private String entrustsName;

    @ApiModelProperty(value = "案件基础信息模板")
    private String tmplName;

    @ApiModelProperty(value = "相关附件")
    private String filePath;

    @ApiModelProperty(value = "接收时间")
    private LocalDateTime acceptTime;
}
