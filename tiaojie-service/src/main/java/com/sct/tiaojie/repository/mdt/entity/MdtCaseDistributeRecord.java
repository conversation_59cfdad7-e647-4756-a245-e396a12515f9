package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sct.tiaojie.service.mdt.handler.CaseStatusTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("案件分批记录实体")
public class MdtCaseDistributeRecord {

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "record_id",type = IdType.ASSIGN_ID)
    private Long recordId;

    @ApiModelProperty(value = "操作类型：assign/cancel")
    private String action;

    @ApiModelProperty(value = "分派的调解组织ID")
    private Long targetMdtOrgId;

    @ApiModelProperty("调解组织名字")
    private String targetMdtOrgName;

    @ApiModelProperty(value = "分派的调解团队ID（deptId）")
    private Long targetDeptId;

    @ApiModelProperty("调解团队名字")
    private String targetDeptName;

    @ApiModelProperty(value = "分派的调解员ID")
    private Long targetMediatorId;

    @ApiModelProperty("调解员名字")
    private String targetMediatorName;

    @ApiModelProperty(value = "案件列表")
    private String caseList;

    @ApiModelProperty(value = "操作者组织ID")
    private Long operatorOrgId;

    @ApiModelProperty(value = "操作者组织名称")
    private String operatorOrgName;

    @ApiModelProperty(value = "操作者ID")
    private Long operatorId;

    @ApiModelProperty(value = "操作者名字")
    private String operatorName;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "操作者角色名称")
    private String roles;

}
