package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.auth.entity.AuthDept;
import com.sct.tiaojie.service.auth.bo.DeptBO;
import com.sct.tiaojie.service.auth.dto.DeptDTO;
import com.sct.tiaojie.service.auth.dto.DeptSmsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * <p>
 *   部门 Mapper 接口
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface AuthDeptMapper extends BaseMapper<AuthDept> {

    /**
     * 分页查询部门信息
     * @param page
     * @param param
     * @param companyId
     * @return
     */
    Page<DeptDTO> pageList(Page<DeptDTO> page, @Param("param") DeptDTO param, @Param("companyId") Long companyId);

    /**
     * 部门的简单查询
     * @param param
     * @return
     */
    List<DeptDTO> getDept(@Param("param") DeptBO param);

    /**
     * 获取公司下部门最大排序，默认0
     * @param companyId
     * @return
     */
    Integer findMaxSortByCompanyId(@Param("companyId") Long companyId);

    /**
     * 获取公司所有的部门
     * @return
     */
    List<DeptSmsDTO> getAllDept();
}
