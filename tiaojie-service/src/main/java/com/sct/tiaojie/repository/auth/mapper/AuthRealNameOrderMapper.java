package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.auth.entity.AuthRealnameOrder;
import com.sct.tiaojie.service.auth.bo.RealNamePageBO;
import com.sct.tiaojie.service.auth.dto.RealNamePageDTO;
import com.sct.tiaojie.service.mdt.dto.FaceImgDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AuthRealNameOrderMapper extends BaseMapper<AuthRealnameOrder> {

    /**
     * 分页查询实名认证记录
     * @param page
     * @param param
     * @param companyId
     * @return
     */
    Page<RealNamePageDTO> pageList(Page<RealNamePageDTO> page, @Param("param") RealNamePageBO param, @Param("companyId") Long companyId);

    /**
     * 获取刷脸图片
     * @param orderIds
     * @param param
     * @param companyId
     * @return
     */
    List<FaceImgDTO> getFaceImg(@Param("orderIds") List<Long> orderIds, @Param("param") RealNamePageBO param, @Param("companyId") Long companyId);
}
