package com.sct.tiaojie.repository.sign.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sign.entity.SignInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @description 签名信息Repository
 * <AUTHOR>
 */
@Mapper
public interface SignInfoMapper extends BaseMapper<SignInfo> {

  @Select("select * from sign_info where person_id = #{personKey} limit 1")
  SignInfo findFirstByPersonId(@Param("personKey") String personKey);

  @Select("select * from sign_info where trust_sign_mobile = #{mobile} AND trust_sign_cert_no = #{idCardNo} AND trust_sign_cert_type = #{certType} limit 1")
  SignInfo findFirstByMobileAndIdCard(@Param("mobile") String mobile, @Param("idCardNo") String idCardNo, @Param("certType") String certType);

  @Select("select * from sign_info where trust_sign_mobile = #{mobile} limit 1")
  SignInfo findFirstByMoble(@Param("mobile") String mobile);
}
