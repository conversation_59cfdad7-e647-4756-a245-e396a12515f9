package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AuthRealnameOrder {

    @ApiModelProperty(value = "实名订单ID", example = "1")
    @TableId(type = IdType.ASSIGN_ID)
    private Long orderId;

    @ApiModelProperty(value = "SIGN类型的ticket")
    private String signTicket;

    @ApiModelProperty(value = "案件ID")
    private Long caseId;

    @ApiModelProperty(value = "signTicket过期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String idCardNo;

    @ApiModelProperty(value = "认证结果")
    private String result;

    @ApiModelProperty(value = "活体检测得分")
    private String liveRate;

    @ApiModelProperty(value = "人脸对比得分")
    private String similarity;

    @ApiModelProperty(value = "人脸核身时的照片,base64位编码")
    private String photo;

    @ApiModelProperty(value = "人脸核身时的视频,base64位编码")
    private String video;

    @ApiModelProperty(value = "刷脸的时间")
    private LocalDateTime occurredTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;
}
