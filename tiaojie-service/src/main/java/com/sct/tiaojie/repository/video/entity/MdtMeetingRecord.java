package com.sct.tiaojie.repository.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("视频调解预约记录")
public class MdtMeetingRecord {

    @ApiModelProperty("ID")
    @TableId(value = "record_id", type = IdType.ASSIGN_ID)
    private Long recordId;

    @ApiModelProperty("案件ID")
    private String caseId;

    @ApiModelProperty(value = "调解ID")
    private String mediationId;

    @ApiModelProperty(value = "会议室ID")
    private String meetingId;

    @ApiModelProperty("预约会议的调解员ID")
    private String mediatorId;

    @ApiModelProperty("预约会议的调解员姓名")
    private String mediatorName;

    @ApiModelProperty(value = "会议室名")
    private String meetingName;

    @ApiModelProperty(value = "参会人")
    private String participant;

    @ApiModelProperty(value = "会议状态")
    private String meetingStatus;

    @ApiModelProperty(value = "会议密码")
    private String password;

    @ApiModelProperty(value = "会议链接")
    private String meetingUrl;

    @ApiModelProperty("会议时间")
    private LocalDateTime mediationTime;

    @ApiModelProperty("会议视频下载地址")
    private String videoUrl;

    @ApiModelProperty("会议发言信息")
    private String textContent;

    @ApiModelProperty("删除标识")
    private String deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}
