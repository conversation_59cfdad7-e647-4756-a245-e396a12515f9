package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 登录页自定义外观;
 *
 * <AUTHOR> llj
 * @date : 2024-7-24
 */
@ApiModel(value = "登录页自定义外观")
@TableName("sys_login_appearance")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysLoginAppearance implements Serializable {
    /**
     * 外观id
     */
    @ApiModelProperty("外观id")
    @TableId
    private Long appearanceId;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String appearanceName;
    /**
     * 路径
     */
    @ApiModelProperty("路径")
    private String appearancePath;
    /**
     * 域名
     */
    @ApiModelProperty("域名")
    private String appearanceDomainName;
    /**
     * 登录地址
     */
    @ApiModelProperty("登录地址")
    private String appearanceUrl;
    /**
     * 启用状态(0未启用,1启用)
     */
    @ApiModelProperty("启用状态(0未启用,1启用)")
    private Integer appearanceEnableStatus;
    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long creatorId;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人id
     */
    @ApiModelProperty("修改人id")
    private Long updaterId;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
     * 是否删除(0未删除,1已删除)
     */
    @ApiModelProperty("是否删除(0未删除,1已删除)")
    private Integer isDeleted;
    /**
     * 所属用户
     */
    @ApiModelProperty("所属用户")
    private String appearanceUserName;
    /**
     * 导航栏名称
     */
    @ApiModelProperty("导航栏名称")
    private String navigationName;
    /**
     * 导航栏logo地址
     */
    @ApiModelProperty("导航栏logo地址")
    private String navigationPath;
    /**
     * 标签页展示名称
     */
    @ApiModelProperty("标签页展示名称")
    private String tabName;
    /**
     * 标签页logo地址
     */
    @ApiModelProperty("标签页logo地址")
    private String tabPath;
    /**
     * 背景图logo地址
     */
    @ApiModelProperty("背景图logo地址")
    private String backgroundPath;
    /**
     * 登录框标题
     */
    @ApiModelProperty("登录框标题")
    private String loginBoxTitle;
    /**
     * 品牌标题
     */
    @ApiModelProperty("品牌标题")
    private String brandTitle;
    /**
     * 品牌logo地址
     */
    @ApiModelProperty("品牌logo地址")
    private String brandPath;

}
