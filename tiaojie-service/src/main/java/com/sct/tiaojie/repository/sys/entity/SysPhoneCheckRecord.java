package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 号码检测记录;
 *
 * <AUTHOR> llj
 * @date : 2024-6-28
 */
@ApiModel(value = "号码检测记录")
@TableName("sys_phone_check_record")
@Data
public class SysPhoneCheckRecord implements Serializable {
    /**
     * 检测记录id
     */
    @ApiModelProperty(value ="检测记录id")
    @TableId
    private Long recordId;
    /**
     * 案件id
     */
    @ApiModelProperty(value ="案件id")
    private Long caseId;
    /**
     * 案件编号
     */
    @ApiModelProperty(value ="案件编号")
    private String caseNo;
    /**
     * 案源方ID
     */
    @ApiModelProperty(value ="案源方ID")
    private Long entrustsId;
    /**
     * 案源方名称
     */
    @ApiModelProperty(value ="案源方名称")
    private String entrustsName;
    /**
     * 检测操作人id
     */
    @ApiModelProperty(value ="检测操作人id")
    private Long operatorId;
    /**
     * 检测操作人名称
     */
    @ApiModelProperty(value ="检测操作人名称")
    private String operatorName;
    /**
     * 检测操作人公司ID
     */
    @ApiModelProperty(value ="检测操作人公司ID")
    private Long operatorCompanyId;
    /**
     * 当事人id
     */
    @ApiModelProperty(value ="当事人id")
    private Long litigantId;
    /**
     * 当事人名称
     */
    @ApiModelProperty(value ="当事人名称")
    private String litigantName;
    /**
     * 当事人身份
     */
    @ApiModelProperty(value ="当事人身份")
    private String identityType;
    /**
     * 当事人类型
     */
    @ApiModelProperty(value ="当事人类型")
    private String litigantType;
    /**
     * 号码状态
     */
    @ApiModelProperty(value ="号码状态")
    private Integer phoneStatus;
    /**
     * 检测手机号码
     */
    @ApiModelProperty(value ="检测手机号码")
    private String litigantPhone;
    /**
     * 号码检测结果
     */
    @ApiModelProperty(value ="号码检测结果")
    private String phoneCheckResult;
    /**
     * 检测时间
     */
    @ApiModelProperty(value ="检测时间")
    private Date checkDate;
    /**
     * http状态码
     */
    @ApiModelProperty(value ="http状态码")
    private Integer httpStatus;
    /**
     * http原始响应信息
     */
    @ApiModelProperty(value ="http原始响应信息")
    private String httpResponse;
}
