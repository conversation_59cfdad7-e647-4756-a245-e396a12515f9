package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysMsg;
import com.sct.tiaojie.service.sys.bo.SysMsgPageBO;
import com.sct.tiaojie.service.sys.dto.SysMsgDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * <p>
 *   消息 Mapper 接口
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR> Panda
 * @date          2020-06-12
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface SysMsgMapper extends BaseMapper<SysMsg> {

    /**
     * 分页查询
     *
     * @param page
     * @param params
     * @param companyId
     * @return
     */
    Page<SysMsgDTO> pageList(Page<SysMsgDTO> page, @Param("params") SysMsgPageBO params, @Param("companyId") Long companyId);

    /**
     * 查询用户所有的未读消息
     *
     * @param userId
     * @return
     */
    List<SysMsgDTO> listUnReadMsg(@Param("userId") Long userId);

    /**
     * 获取消息详情
     *
     * @param msgId
     * @param receiveUserId
     * @return
     */
    SysMsgDTO getDetail(@Param("msgId") Long msgId, @Param("receiveUserId") Long receiveUserId);

}
