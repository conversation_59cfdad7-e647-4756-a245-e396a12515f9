package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description 用户信息实体
 * <AUTHOR>
 */
@Data
@ApiModel("用户信息实体")
public class AuthUser implements Serializable {
    
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "用户ID, 长度为22")
    private Long userId;
    
    @ApiModelProperty(value = "用户登录名, 长度为20")
    private String loginUname;
    
    @ApiModelProperty(value = "用户登录密码, 长度为300")
    private String loginPassword;
    
    @ApiModelProperty(value = "用户中文名, 长度为20")
    private String userCname;
    
    @ApiModelProperty(value = "用户状态(0:停用,1:启用,2:锁定)")
    private Integer userStatus;

    @ApiModelProperty(value = "登录错误次数")
    private Integer loginErrorTimes;
    
    @ApiModelProperty(value = "手机号, 长度为20")
    private String mobile;
    
    @ApiModelProperty(value = "所属部门, 长度为20")
    private String deptName;

    @ApiModelProperty(value = "关联平台用户ID")
    private String bizUserId;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "创建人ID, 长度为22")
    private Long createManId;
    
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty(value = "最后更新人ID, 长度为22")
    private Long updateManId;
    
} 
        