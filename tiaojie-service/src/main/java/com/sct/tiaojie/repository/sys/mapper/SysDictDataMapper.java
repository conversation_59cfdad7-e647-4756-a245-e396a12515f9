package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysDictData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-03 17:24:15
 */
@Mapper
public interface SysDictDataMapper extends BaseMapper<SysDictData> {

    /**
     * 查询最大的dictKey
     * @param dictId
     * @return
     */
    @Select("SELECT dict_key FROM sys_dict_data WHERE dict_id = #{dictId} ORDER BY dict_key DESC LIMIT 1")
    Integer getBestKey(@Param("dictId") Integer dictId);

    /**
     * 通过dictType查询字段值
     * @param dictType
     * @return
     */
    @Select("SELECT t1.dict_Id, t1.dict_key, t1.dict_tag FROM sys_dict_data t1 INNER JOIN sys_dict t2 ON t1.dict_id = t2.dict_id WHERE t2.dict_type = #{dictType} and t1.enable_flag = 1")
    List<SysDictData> getByDictType(@Param("dictType") String dictType);

    /**
     * 查询字典值id
     * @param dictType
     * @param dictKey
     * @return
     */
    Long getDictDateIdByDictTypeAndDictKey(@Param("dictType") String dictType, @Param("dictKey")String dictKey);

    /**
     * 获取顶级字典的dict key
     * @param dictDateId
     * @return
     */
    Integer getTopDictDataKey(@Param("dictDateId") Long dictDateId);
}
