package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("文书模板字段映射表")
@TableName("doc_tmpl_mapping")
public class DocTmplMapping {

    @TableId(value = "mapping_id", type = IdType.AUTO)
    @ApiModelProperty("映射ID")
    private Long mappingId;

    @ApiModelProperty("文书模板ID")
    private Long docTmplId;

    @ApiModelProperty("是否允许多条记录")
    private Integer allowMultipleRecord;

    @ApiModelProperty("参数名称")
    private String parameterName;

    @ApiModelProperty("所属案件字段")
    private String caseFields;

    @ApiModelProperty("所属案件模板ID")
    private Integer tmplId;

    @ApiModelProperty("分隔符")
    private String delimiter;

    @ApiModelProperty("参数内容")
    private String parameterContent;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改者")
    private Long updaterId;
}
