package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.mdt.entity.MdtContactRepairRecord;
import com.sct.tiaojie.service.mdt.dto.MdtContactRepairRecordDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 失联修复记录表;(mdt_contact_repair_record)表数据库访问层
 *
 * <AUTHOR> laolinjie
 * @date : 2024-7-11
 */
@Mapper
public interface MdtContactRepairRecordMapper extends BaseMapper<MdtContactRepairRecord> {
    /**
     * 通过ID查询单条数据
     *
     * @param recordId 主键
     * @return 实例对象
     */
    MdtContactRepairRecord queryById(Long recordId);

    /**
     * 统计总行数
     *
     * @param mdtContactRepairRecord 查询条件
     * @return 总行数
     */
    long count(MdtContactRepairRecord mdtContactRepairRecord);

    /**
     * 新增数据
     *
     * @param mdtContactRepairRecord 实例对象
     * @return 影响行数
     */
    int insert(MdtContactRepairRecord mdtContactRepairRecord);

    /**
     * 批量新增数据
     *
     * @param entities List<MdtContactRepairRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<MdtContactRepairRecord> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities List<MdtContactRepairRecord> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<MdtContactRepairRecord> entities);

    /**
     * 更新数据
     *
     * @param mdtContactRepairRecord 实例对象
     * @return 影响行数
     */
    int update(MdtContactRepairRecord mdtContactRepairRecord);

    /**
     * 通过主键删除数据
     *
     * @param recordId 主键
     * @return 影响行数
     */
    int deleteById(Long recordId);

    /**
     * 获取案件最新修复记录
     */
    List<MdtContactRepairRecordDto> getLeastRecord(@Param("caseId") Long caseId);
}
