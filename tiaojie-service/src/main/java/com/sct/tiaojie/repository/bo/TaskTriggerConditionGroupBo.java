package com.sct.tiaojie.repository.bo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * <p>
 * 触发条件项表 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          [CurrentDate]
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "触发条件项bo")
@NoArgsConstructor
public class TaskTriggerConditionGroupBo implements Serializable {
    @ApiModelProperty(value = "条件组ID", example = "1")
    private Long groupId;

    @ApiModelProperty(value = "配置ID", example = "1001")
    private Long configId;

    @ApiModelProperty(value = "父组ID", example = "0")
    private Long parentGroupId;

    @ApiModelProperty(value = "组间逻辑运算符(1:AND 2:OR)", example = "1")
    private Integer logicOperator;

    @ApiModelProperty(value = "行间逻辑运算符(1:AND 2:OR)", example = "1")
    private Integer rowOperator;

    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer sortOrder;

    List<TaskTriggerConditionBo> items;
}
