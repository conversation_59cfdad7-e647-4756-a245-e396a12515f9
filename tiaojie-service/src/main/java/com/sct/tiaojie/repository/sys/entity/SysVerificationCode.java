package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 验证码记录 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2021-06-05
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "验证码记录实体")
public class SysVerificationCode implements Serializable {

    @ApiModelProperty(value = "验证码记录id", example = "1")
    @TableId(value = "validation_code_id", type = IdType.ASSIGN_ID)
    private Long validationCodeId;

    @ApiModelProperty(value = "验证码,最大长度为10")
    @Length(min = 0, max = 10, message = "验证码格式错误,最大长度为10")
    private String validationCode;

    @ApiModelProperty(value = "手机号 或 邮箱", example = "1")
    private Integer sendType;

    @ApiModelProperty(value = "手机号码 或 邮箱账号,最大长度为50")
    @Length(min = 0, max = 50, message = "手机号码 或 邮箱账号格式错误,最大长度为50")
    private String targetNumber;

    @ApiModelProperty(value = "是否被使用 0:未使用 1:已使用")
    @TableField("used")
    private Boolean used;

    @ApiModelProperty(value = "业务场景", example = "1")
    private Integer bizType;

    @ApiModelProperty(value = "有效时间 (分钟),最大长度为3")
    private Long validTime;

    @ApiModelProperty(value = "过期时间")
    private LocalDateTime expirationTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

}