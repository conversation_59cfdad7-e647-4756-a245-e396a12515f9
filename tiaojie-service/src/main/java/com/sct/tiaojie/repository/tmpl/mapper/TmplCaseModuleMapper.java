package com.sct.tiaojie.repository.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.tmpl.entity.TmplCaseModule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
@Mapper
public interface TmplCaseModuleMapper extends BaseMapper<TmplCaseModule> {

    /**
     * 查询最大的序号
     * @param tmplId
     * @param moduleType
     * @return
     */
    @Select("SELECT MAX(module_sn) FROM tmpl_case_module WHERE tmpl_id = #{tmplId} AND module_type = #{moduleType}")
    Integer selectMaxSn(@Param("tmplId") Integer tmplId, @Param("moduleType") String moduleType);
}
