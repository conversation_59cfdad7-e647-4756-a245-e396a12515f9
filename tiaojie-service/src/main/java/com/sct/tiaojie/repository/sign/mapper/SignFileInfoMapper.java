package com.sct.tiaojie.repository.sign.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sign.entity.SignFileInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @description 文件签名信息Repository
 * <AUTHOR>
 */
@Mapper
public interface SignFileInfoMapper extends BaseMapper<SignFileInfo> {

  @Select("SELECT * FROM sign_file_info WHERE file_path = #{filePath} AND status != #{status}")
  SignFileInfo findByFilePathAndStatusNot(@Param("filePath") String filePath, @Param("status") Integer status);

  @Update("update sign_file_info a set a.status = 1,a.update_time = Now()  where a.file_sign_info_id = #{fileSignInfoId} and a.status = 2")
  int updateInfoProcessing(@Param("fileSignInfoId") Long fileSignInfoId);

  @Update("update sign_file_info a set a.status = 2,a.update_time = Now()  where a.file_sign_info_id = #{fileSignInfoId} and a.status = 1")
  int updateInfoWaitProcess(@Param("fileSignInfoId") Long fileSignInfoId);

  @Update("update sign_file_info SET sign_complete_time = null, can_sign_flag = 1, status = 2 WHERE file_sign_info_id = #{fileSignInfoId}")
  void updateToSupplementSign(@Param("fileSignInfoId") Long fileSignInfoId);
}
        