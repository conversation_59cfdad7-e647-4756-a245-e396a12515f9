package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 案由 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-13
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "案由实体")
public class SysNature implements Serializable {

    @ApiModelProperty(value = "案由ID", example = "1")
    @TableId(value = "nature_id", type = IdType.ASSIGN_ID)
    private Long natureId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "父ID", example = "1")
    private Long parentId;

    @ApiModelProperty(value = "标题,最大长度为128")
    @Length(min = 0, max = 128, message = "标题格式错误,最大长度为128")
    private String title;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "是否可选")
    @TableField("selectable")
    private Boolean selectable;

    @ApiModelProperty(value = "是否常用")
    @TableField("constant_flag")
    private Boolean constantFlag;

    @ApiModelProperty(value = "描述,最大长度为500")
    @Length(min = 0, max = 500, message = "描述格式错误,最大长度为500")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

}