package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 失联修复记录表;
 *
 * <AUTHOR> laolinjie
 * @date : 2024-7-11
 */
@ApiModel(value = "失联修复记录", description = "失联修复记录")
@Data
public class MdtContactRepairRecord implements Serializable {
    /**
     * 记录id
     */
    @ApiModelProperty("记录id")
    @TableId
    private Long recordId;
    /**
     * 修复id
     */
    @ApiModelProperty("修复id")
    private Long repairId;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    private String batchNo;
    /**
     * 案件id
     */
    @ApiModelProperty("案件id")
    private Long caseId;
    /**
     * 当事人id
     */
    @ApiModelProperty("当事人id")
    private Long litigantId;
    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String idCard;
    /**
     * sha加密密文
     */
    @ApiModelProperty("sha加密密文")
    private String idCardSha;
    /**
     * md5加密密文
     */
    @ApiModelProperty("md5加密密文")
    private String idCardMd5;
    /**
     * 待修复用户名
     */
    @ApiModelProperty("待修复用户名")
    private String repairerName;
    /**
     * 待修复号码
     */
    @ApiModelProperty("待修复号码")
    private String repairerNumber;
    /**
     * 当事人类型
     */
    @ApiModelProperty("当事人类型")
    private String litigantType;
    /**
     * 当事人身份
     */
    @ApiModelProperty("当事人身份")
    private String identityType;
    /**
     * 号码状态
     */
    @ApiModelProperty("号码状态")
    private String phoneStatus;
    /**
     * 结果 id
     */
    @ApiModelProperty("结果 id")
    private String resultId;
    /**
     * 加密方式:1-SHA256; 2-MD5
     */
    @ApiModelProperty("加密方式:1-SHA256,2-MD5")
    private Integer maskModel;
    /**
     * 修复成功后的号码列表
     */
    @ApiModelProperty("修复成功后的号码列表")
    private String phoneList;
    /**
     * 匹配-修复结果
     */
    @ApiModelProperty("匹配-修复结果")
    private String matchRepairResult;
    /**
     * 匹配-修复失败的原因
     */
    @ApiModelProperty("匹配-修复失败的原因")
    private String matchFailMessage;
    /**
     * 主呼号
     */
    @ApiModelProperty("主呼号")
    private String callNo;
    /**
     * 外显号码
     */
    @ApiModelProperty("外显号码")
    private String displayNo;
    /**
     * 修复的电话序号
     */
    @ApiModelProperty("修复的电话序号")
    private String phoneSeqNo;
    /**
     * 修复的电话区号
     */
    @ApiModelProperty("修复的电话区号")
    private String phoneAreaCode;
    /**
     * 修复结果源
     */
    @ApiModelProperty("修复结果源")
    private String dataSource;
    /**
     * 虚拟号
     */
    @ApiModelProperty("虚拟号")
    private String virtualNo;
    /**
     * 绑定标识
     */
    @ApiModelProperty("绑定标识")
    private String bindId;
    /**
     * 获取中间号-返回值
     */
    @ApiModelProperty("获取中间号-返回值")
    private String numberRepairResult;
    /**
     * 获取中间号-错误码描述
     */
    @ApiModelProperty("获取中间号-错误码描述")
    private String numberFailMessage;
    /**
     * 获取虚拟号时间
     */
    @ApiModelProperty("获取虚拟号时间")
    private Date repairVirtualNoTime;
    /**
     *记录修复状态
     */
    @ApiModelProperty("记录修复状态")
    private Integer recordRepairStatus;
    /**
     * 操作调解员id
     */
    @ApiModelProperty("操作调解员id")
    private Long userId;
    /**
     * 操作人公司id
     */
    @ApiModelProperty("操作人公司id")
    private Long companyId;

}
