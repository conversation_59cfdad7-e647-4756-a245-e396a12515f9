package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/5/7 15:22
 * @Version 1.0
 */
@Data
@ApiModel(value = "案件事件实体")
public class MdtCaseEvent {
    @TableId(value = "event_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long eventId;

    @ApiModelProperty(value = "案件id")
    private Long caseId;

    @ApiModelProperty(value = "案件类型")
    private String operationType;

    @ApiModelProperty(value = "操作时设置的数据")
    private String setValues;

    @ApiModelProperty(value = "备注信息")
    private String comment;

    @ApiModelProperty(value = "操作者id")
    private Long operatorId;

    @ApiModelProperty(value = "操作者名字")
    private String operator;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operatorTime;
}
