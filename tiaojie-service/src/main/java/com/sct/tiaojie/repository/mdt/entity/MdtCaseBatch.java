package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 调节案件批次信息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-17
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "调节案件批次信息实体")
public class MdtCaseBatch implements Serializable {

    @ApiModelProperty(value = "批次ID", example = "1")
    @TableId(value = "batch_id", type = IdType.ASSIGN_ID)
    private Long batchId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "批次号,最大长度为50")
    @Length(min = 0, max = 50, message = "批次号格式错误,最大长度为50")
    private String batchNo;

    @ApiModelProperty(value = "委案金融机构ID", example = "1")
    private Long finOrgId;

    @ApiModelProperty(value = "委托方ID", example = "1")
    private Long entrustsId;

    @ApiModelProperty(value = "委案日期")
    private LocalDate entrustCaseDate;

    @ApiModelProperty(value = "案件类型ID", example = "1")
    private Integer caseTypeId;

    @ApiModelProperty(value = "预计退案日期")
    private LocalDate expectQuitCaseDate;

    @ApiModelProperty(value = "目标汇款律")
    private Double expectCollectRate;

    @ApiModelProperty(value = "备注,最大长度为500")
    @Length(min = 0, max = 500, message = "备注格式错误,最大长度为500")
    private String remark;

    @ApiModelProperty(value = "户数", example = "1")
    private Integer caseNum;

    @ApiModelProperty(value = "总金额")
    private BigDecimal caseTotal;

    @ApiModelProperty(value = "退案状态(1:未退案;2:已退案)", example = "1")
    private Integer quitCaseStatus;

    @ApiModelProperty(value = "状态（1:未导入;2:已导入）", example = "1")
    private Integer importCaseStatus;

    @ApiModelProperty(value = "实际退案日期")
    private LocalDateTime actualQuitCaseDate;

    @ApiModelProperty(value = "录入人ID")
    private Long importManId;

    @ApiModelProperty(value = "录入日期")
    private LocalDateTime importDate;

    @ApiModelProperty(value = "逻辑删除标志")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "告知书是否需要签名")
    private Boolean signFlag;

    @ApiModelProperty(value = "导入是否后暂不受理")
    private Boolean inadmissibleFlag;

}