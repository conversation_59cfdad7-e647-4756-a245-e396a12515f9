package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("mdt_case_litigant")
public class MdtCaseLitigant extends AIHandle implements Serializable {

    @ApiModelProperty(value = "ID")
    @TableId(value = "litigant_id", type = IdType.ASSIGN_ID)
    private Long litigantId;

    @ApiModelProperty(value = "案件ID")
    private Long caseId;

    @ApiModelProperty(value = "当事人类型")
    private String litigantType;

    @ApiModelProperty(value = "当事人身份")
    private String identityType;

    @ApiModelProperty(value = "当事人名称")
    private String litigantName;

    @ApiModelProperty(value = "当事人证件类型")
    private String idType;

    @ApiModelProperty(value = "当事人证件号码")
    private String idNo;

    @ApiModelProperty(value = "当事人联系方式")
    private String litigantPhone;

    @ApiModelProperty(value = "当事人性别")
    private String sex;

    @ApiModelProperty(value = "当事人职务")
    private String post;

    @ApiModelProperty(value = "电话类型")
    private String phoneStatus;

    @ApiModelProperty(value = "联系状态")
    private Integer contactStatus;

    @ApiModelProperty(value = "备注")
    private String litigantDesc;


    // 自然人

    @ApiModelProperty(value = "当事人民族")
    private String nation;

    @ApiModelProperty(value = "出生日期")
    private LocalDateTime birth;

    @ApiModelProperty(value = "当事人户籍地址")
    private String idAddress;

    @ApiModelProperty(value = "当事人联系地址")
    private String contactAddress;


    // 法人

    @ApiModelProperty(value = "法定代表人姓名")
    private String legalAgentName;

    @ApiModelProperty(value = "是否民营企业")
    private String isPrivateEnterprise;

    @ApiModelProperty(value = "注册地址")
    private String registerAddress;

    @ApiModelProperty(value = "经营地址")
    private String businessAddress;


    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "mbti")
    private String mbti;

    @ApiModelProperty(value = "mbtib报告")
    private String mbtiReport;

    @ApiModelProperty(value = "答辩预测")
    private String guessReply;

    @ApiModelProperty(value = "调解话术")
    private String verbalTrick;

}
