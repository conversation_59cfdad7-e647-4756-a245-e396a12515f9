package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.mdt.entity.MdtCaseLitigant;
import com.sct.tiaojie.service.mdt.bo.MdtContactRepairBo;
import com.sct.tiaojie.service.mdt.bo.NeedRepairContactBO;
import com.sct.tiaojie.service.mdt.dto.CaseSurgeWarningDTO;
import com.sct.tiaojie.service.mdt.dto.MdtCaseLitigantDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MdtCaseLitigantMapper extends BaseMapper<MdtCaseLitigant> {

    List<MdtCaseLitigant> getByPhoneLike(@Param("phone") String phone);

    List<CaseSurgeWarningDTO> getCaseStatisticsByCaseId(@Param("caseIds") List<Long> caseIds);

    /**
     * 获取需要修复联系信息
     * @param mdtContactRepairBo 失联修复bo
     */
    List<NeedRepairContactBO> getNeedRepairContactListByIdList(@Param("mdtContactRepairBo") MdtContactRepairBo mdtContactRepairBo);

    /**
     * 获取需要修复的当事人信息
     * @param mdtContactRepairBo
     * @return
     */
    List<NeedRepairContactBO> getNeedRepairLitigant(@Param("mdtContactRepairBo")MdtContactRepairBo mdtContactRepairBo);
}
