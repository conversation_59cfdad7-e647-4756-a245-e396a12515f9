package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysLoginLog;
import com.sct.tiaojie.service.sys.bo.SysLoginLogBO;
import com.sct.tiaojie.service.sys.dto.SysLoginLogDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SysLoginLogMapper extends BaseMapper<SysLoginLog> {
    /**
     * 分页查询登录日志
     * @param page
     * @param param
     * @return
     */
    Page<SysLoginLogDTO> pageList(Page<SysLoginLogDTO> page, @Param("param") SysLoginLogBO param);
}
