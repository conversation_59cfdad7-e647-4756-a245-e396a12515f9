package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtCaseDistributeRecord;
import com.sct.tiaojie.service.mdt.bo.CaseDistributeLogBo;
import com.sct.tiaojie.service.mdt.dto.CaseDistributeLogDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 案件分配记录Mapper
 *
 * <AUTHOR>
 * @Date 2024/5/31
 * @Version 1.0
 */
@Mapper
public interface MdtCaseDistributeRecordMapper extends BaseMapper<MdtCaseDistributeRecord> {

    Page<CaseDistributeLogDto> getPageDistributeRecordLog(Page<CaseDistributeLogDto> page, @Param("param") CaseDistributeLogBo param);

    List<CaseDistributeLogDto> getDistributeRecordListByCaseId(@Param("caseId") String caseId);

    List<CaseDistributeLogDto> getLastDistributeRecordList(@Param("caseIdList") List<Long> caseIdList);

    List<CaseDistributeLogDto> selectDistributeLogList(@Param("param") CaseDistributeLogBo caseDistributeLogBo);
}
