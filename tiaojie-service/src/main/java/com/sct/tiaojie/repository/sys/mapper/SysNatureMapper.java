package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysNature;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/***********************************************************************************************************************
 * <p>
 *   案由 Mapper 接口
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-13
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface SysNatureMapper extends BaseMapper<SysNature> {

    @Select("select COALESCE(MAX(n.sort), 0) from sys_nature n where n.parent_id=#{parentId}")
    int findMaxSort(@Param("parentId") Long parentId);

    @Select("select n.* from sys_nature n where n.parent_id is null order by n.sort asc")
    List<SysNature> findRootAll();

    @Select("select n.* from sys_nature n where n.parent_id is null and n.selectable=#{selectable} order by n.sort asc")
    List<SysNature> findAllBySelectable(@Param("selectable") Boolean selectable);

    @Update("update sys_nature set sort=#{moveDownSort} where nature_id=#{moveUpNatureId}")
    void moveUpdate(@Param("moveUpNatureId") Long moveUpNatureId, @Param("moveDownSort") Integer moveDownSort);
}
