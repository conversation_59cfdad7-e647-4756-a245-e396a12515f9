package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.auth.entity.AuthTenUserAgentRela;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 */
public interface AuthTenUserAgentRelaMapper extends BaseMapper<AuthTenUserAgentRela> {

    /**
     * 清除指定坐席绑定关系
     * @param agentId
     */
    @Update("UPDATE auth_ten_user_agent_rela SET agent_id = NULL WHERE agent_id = #{agentId}")
    void clearByAgent(@Param("agentId") String agentId);
}
