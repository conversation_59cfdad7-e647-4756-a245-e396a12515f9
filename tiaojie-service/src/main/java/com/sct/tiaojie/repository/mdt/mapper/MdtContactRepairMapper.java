package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtContactRepair;
import com.sct.tiaojie.service.mdt.bo.MdtContactRepairPageBo;
import com.sct.tiaojie.service.mdt.dto.MdtContactRepairRecordDto;
import com.sct.tiaojie.service.mdt.dto.MdtSmsLogDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 失联修复业务表(MdtContactRepair)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-10 18:23:33
 */
@Mapper
public interface MdtContactRepairMapper extends BaseMapper<MdtContactRepair> {

    /**
     * 通过ID查询单条数据
     *
     * @param repairId 主键
     * @return 实例对象
     */
    MdtContactRepair queryById(Long repairId);

    /**
     * 统计总行数
     *
     * @param mdtContactRepair 查询条件
     * @return 总行数
     */
    long count(MdtContactRepair mdtContactRepair);

    /**
     * 新增数据
     *
     * @param mdtContactRepair 实例对象
     * @return 影响行数
     */
    int insert(MdtContactRepair mdtContactRepair);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<MdtContactRepair> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<MdtContactRepair> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<MdtContactRepair> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<MdtContactRepair> entities);

    /**
     * 修改数据
     *
     * @param mdtContactRepair 实例对象
     * @return 影响行数
     */
    int update(MdtContactRepair mdtContactRepair);

    /**
     * 通过主键删除数据
     *
     * @param repairId 主键
     * @return 影响行数
     */
    int deleteById(Long repairId);

    /**
     * 分页查询
     *
     * @param page  分页数据
     * @param param 分页查询参数
     */
    Page<MdtContactRepairRecordDto> pageRepairLog(@Param("page") Page<MdtContactRepairRecordDto> page, @Param("param") MdtContactRepairPageBo param);

    /**
     * 案件详情-修复记录
     *
     * @param caseId 案件id
     */
    List<MdtContactRepairRecordDto> repairLogList(@Param("caseId") Long caseId);

    /**
     * 查询失联修复记录
     * @param mdtContactRepairPageBo 查询bo条件
     */
    List<MdtContactRepairRecordDto> selectRepairLogListByBo(@Param("param") MdtContactRepairPageBo mdtContactRepairPageBo);
}

