package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/5/12 9:48
 * @Version 1.0
 */
@Data
@ApiModel(value = "短信发送业务实体")
public class MdtSms {
    @TableId(value = "sms_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long smsId;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "案件id")
    private Long caseId;

    @ApiModelProperty(value = "短信发送表id")
    private Long smsSendId;

    @ApiModelProperty(value = "接收者姓名")
    private String contactName;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "关联ID")
    private Long optId;
}
