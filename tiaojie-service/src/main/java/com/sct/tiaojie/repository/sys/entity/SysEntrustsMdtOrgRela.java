package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@ApiModel("委托方和调解组织关联表")
@Data
@TableName("sys_entrusts_mdt_org_rela")
public class SysEntrustsMdtOrgRela implements Serializable {

    @TableId(value = "rela_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("关联记录ID")
    private Long relaId;

    @ApiModelProperty(value = "调解组织ID", example = "1")
    private Long orgId;

    @ApiModelProperty("委托方ID")
    private Long entrustsId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty("部门id")
    private Long deptId;


}
