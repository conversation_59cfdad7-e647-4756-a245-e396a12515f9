package com.sct.tiaojie.repository.auth.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AuthTenAgent {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "坐席ID")
    private String agentId;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty(value = "管理调解员ID")
    private Long accountId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty(value = "调解组织ID")
    private Long orgId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("委托方ID")
    private Long entrustsId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("平台方公司ID")
    private Long plantFormId;

    @ApiModelProperty(value = "企业类型")
    private String companyType;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "坐席电话")
    private String agentPhone;

}
