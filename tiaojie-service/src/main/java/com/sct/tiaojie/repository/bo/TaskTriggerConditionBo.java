package com.sct.tiaojie.repository.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TaskTriggerConditionBo {
    @ApiModelProperty(value = "条件项ID", example = "1")
    private Long conditionId;

    @ApiModelProperty(value = "组ID", required = true, example = "1001")
    private Long groupId;

    @ApiModelProperty(value = "模板模块ID", required = true, example = "1")
    private Integer tmplModuleId;

    @ApiModelProperty(value = "模板字段ID", required = true, example = "1")
    private Integer tmplModuleFieldId;

    @ApiModelProperty(value = "运算符(1:有值 2:等于 3:包含)", required = true, example = "1")
    private Integer operator;

    @ApiModelProperty(value = "目标值", example = "合同纠纷")
    private String targetValue;

    @ApiModelProperty(value = "行间逻辑运算符(1:AND 2:OR)", example = "1")
    private Integer logicOperator;
}
