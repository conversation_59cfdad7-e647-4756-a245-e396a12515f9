package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/12 9:48
 * @Version 1.0
 */
@Data
@ApiModel(value = "公司自定义配置")
@TableName(autoResultMap = true)
public class MdtCompanySetting {

    @TableId(value = "setting_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long settingId;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "ai功能开关")
    private Boolean aiSwitch;

    @ApiModelProperty(value = "ip白名单")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<IpWhitelist> ipWhitelistList;

    @ApiModelProperty(value = "白名单位置")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<WhiteLocation> whiteLocationList;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @Data
    public static class WhiteLocation {
        private String name;
        private String longitude;
        private String latitude;
    }
    @Data
    public static class IpWhitelist {
        private String name;
        private String ip;
    }
}




