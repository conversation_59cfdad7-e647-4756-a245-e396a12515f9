package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 校验票据信息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2021-06-05
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "校验票据信息实体")
public class SysValidateTicket implements Serializable {

    @ApiModelProperty(value = "票据ID", example = "1")
    @TableId(value = "validate_ticket_id", type = IdType.ASSIGN_ID)
    private Long validateTicketId;

    @ApiModelProperty(value = "票据,最大长度为128")
    @Length(min = 0, max = 128, message = "票据格式错误,最大长度为128")
    private String ticket;

    @ApiModelProperty(value = "手机号,最大长度为32")
    @Length(min = 0, max = 32, message = "手机号格式错误,最大长度为32")
    private String mobile;

    @ApiModelProperty(value = "验证码,最大长度为20")
    @Length(min = 0, max = 20, message = "验证码格式错误,最大长度为20")
    private String validateCode;

    @ApiModelProperty(value = "姓名,最大长度为255")
    @Length(min = 0, max = 255, message = "姓名格式错误,最大长度为255")
    private String personName;

    @ApiModelProperty(value = "证据类型", example = "1")
    private Integer cardType;

    @ApiModelProperty(value = "证据号码,最大长度为32")
    @Length(min = 0, max = 32, message = "证据号码格式错误,最大长度为32")
    private String cardNo;

    @ApiModelProperty(value = "是否使用，最后的步骤设置为使用")
    @TableField("used")
    private Boolean used;

    @ApiModelProperty(value = "票据类型，枚举", example = "1")
    private Integer ticketType;

    @ApiModelProperty(value = "失效时间，秒", example = "1")
    private Integer expireInSeconds;

    @ApiModelProperty(value = "下一步,最大长度为64")
    @Length(min = 0, max = 64, message = "下一步格式错误,最大长度为64")
    private String nextStep;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

}