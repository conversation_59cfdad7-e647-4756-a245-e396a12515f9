package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.auth.entity.AuthOperationStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AuthOperationStatisticsMapper extends BaseMapper<AuthOperationStatistics> {

    /**
     * 获取作业情况统计
     * @param orgIdList
     * @param dateStart
     * @param dateEnd
     * @return
     */
    List<AuthOperationStatistics> getMediateWorkSituation(@Param("orgIdList") List<Long> orgIdList, @Param("dateStart") LocalDateTime dateStart, @Param("dateEnd") LocalDateTime dateEnd);
}
