package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SysScheduleMgr {

    /**
     * 运行中
     */
    public static Integer RUNNING = 1;
    /**
     * 未运行
     */
    public static Integer UN_RUN = 0;

    @ApiModelProperty(value = "ID", example = "1")
    @TableId(value = "schedule_id", type = IdType.ASSIGN_ID)
    private Long scheduleId;

    @ApiModelProperty(value = "定时任务名称", example = "1")
    private String scheduleName;

    @ApiModelProperty(value = "定时任务状态:1 运行中;0 未运行", example = "1")
    private Integer scheduleStatus;

    private LocalDateTime lastRunTime;
}
