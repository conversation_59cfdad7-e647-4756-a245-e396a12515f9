package com.sct.tiaojie.repository.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.tmpl.entity.TmplCaseModuleField;
import com.sct.tiaojie.service.tmpl.dto.TmplDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
@Mapper
public interface TmplCaseModuleFieldMapper extends BaseMapper<TmplCaseModuleField> {

    /**
     * 查询模板字段详情
     * @param tmplId
     * @return
     */
    TmplDetail moduleFieldList(@Param("tmplId") Integer tmplId);

    /**
     * 查询最大的序号
     * @param tmplId
     * @param tmplModuleId
     * @return
     */
    @Select("SELECT MAX(field_sn) FROM tmpl_case_module_field WHERE tmpl_id = #{tmplId} AND tmpl_module_id = #{tmplModuleId}")
    Integer selectMaxSn(@Param("tmplId") Integer tmplId, @Param("tmplModuleId") Integer tmplModuleId);
}
