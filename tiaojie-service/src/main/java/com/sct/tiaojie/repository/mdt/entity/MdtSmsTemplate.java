package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 短信模板实体
 * <AUTHOR>
 * @Date 2022/5/7 9:26
 * @Version 1.0
 */
@Data
@ApiModel(value = "短信模板实体")
public class MdtSmsTemplate {
    @TableId(value = "sms_template_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long smsTemplateId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "模板id，由腾讯生成")
    private String txTemplateId;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板内容")
    private String templateContent;

    @ApiModelProperty(value = "状态：1通过 0审核中 ")
    private Integer templateStatus;

    @ApiModelProperty(value = "用途")
    private String applyto;

    @ApiModelProperty(value = "说明")
    private String description;

    @ApiModelProperty(value = "参数")
    private String templateParams;

    @ApiModelProperty(value = "模板类型")
    private String templateType;

    @ApiModelProperty("短信签名")
    private String smsSign;

    @ApiModelProperty("所属场景")
    private String scene;

    @ApiModelProperty("所属渠道")
    private String channel;

    @ApiModelProperty("可见部门")
    private String deptIdList;

    @ApiModelProperty("可见角色")
    private String roleNameList;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;
}
