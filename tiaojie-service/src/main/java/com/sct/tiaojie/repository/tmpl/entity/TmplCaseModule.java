package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("模板模块表")
@TableName("tmpl_case_module")
public class TmplCaseModule {

    @TableId(value = "tmpl_module_id", type = IdType.AUTO)
    @ApiModelProperty("模板模块ID")
    private Integer tmplModuleId;

    @ApiModelProperty("模板ID")
    private Integer tmplId;

    @ApiModelProperty("基础模块ID")
    private Integer basisModuleId;

    @ApiModelProperty("模块标题")
    private String moduleTitle;

    @ApiModelProperty("模块描述")
    private String moduleDesc;

    @ApiModelProperty("模块类型")
    private String moduleType;

    @ApiModelProperty("模块对应表名")
    private String tableName;

    @ApiModelProperty("模块序号")
    private Integer moduleSn;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改者")
    private Long updaterId;

    @ApiModelProperty("是否允许多条记录")
    private Integer allowMultipleRecord;
}
