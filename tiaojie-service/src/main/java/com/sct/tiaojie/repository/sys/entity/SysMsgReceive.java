package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 消息接收 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR> Panda
 * @date          2020-06-15
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "消息接收实体")
public class SysMsgReceive implements Serializable {

    @ApiModelProperty(value = "消息ID", example = "1")
    private Long msgId;

    @ApiModelProperty(value = "消息发送人", example = "1")
    private Long fromUserId;

    @ApiModelProperty(value = "消息接收人", example = "1")
    private Long receiveUserId;

    @ApiModelProperty(value = "是否已读")
    @TableField("readed")
    private Boolean readed;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTm;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

}