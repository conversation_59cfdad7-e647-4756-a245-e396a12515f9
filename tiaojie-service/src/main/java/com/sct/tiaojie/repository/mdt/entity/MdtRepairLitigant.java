package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel(value = "失联修复当事人表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mdt_repair_litigant")
public class MdtRepairLitigant {

    @ApiModelProperty(value = "ID")
    @TableId(value = "repair_litigant_id",type = IdType.ASSIGN_ID)
    private Long repairLitigantId;

    @ApiModelProperty("批次Id")
    private Long repairId;

    @ApiModelProperty("案件Id")
    private Long caseId;

    @ApiModelProperty("当事人ID")
    private Long litigantId;

    @ApiModelProperty("当事人名称")
    private String litigantName;

    @ApiModelProperty("当事人曾用电话")
    private String phoneNumber;

    @ApiModelProperty("当事人身份证")
    private String idCard;

    @ApiModelProperty("当事人身份证md5加密")
    private String idCardMd;

    @ApiModelProperty("当事人身份证sha256加密")
    private String idCardSha;

    @ApiModelProperty("当事人类型")
    private String litigantType;

    @ApiModelProperty("当事人身份")
    private String identityType;

    @ApiModelProperty("修复状态")
    private String repairStatus;

    @ApiModelProperty("修复结果")
    private String repairResult;

    @ApiModelProperty("修复成功渠道")
    private String repairSuccessNet;

    @ApiModelProperty("修复成功时间")
    private LocalDateTime repairSuccessTime;

    @ApiModelProperty("发起人Id")
    private Long accountId;

    @ApiModelProperty("发起人名称")
    private String accountName;

    @ApiModelProperty("发起组织Id")
    private Long companyId;

    @ApiModelProperty("发起组织名称")
    private String companyName;
}
