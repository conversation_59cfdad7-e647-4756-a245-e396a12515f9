package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 文件信息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR> Panda
 * @date          2021-05-26
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "文件信息实体")
public class SysAttachment implements Serializable {

    @ApiModelProperty(value = "附件ID", example = "1")
    @TableId(value = "attachment_id", type = IdType.ASSIGN_ID)
    private Long attachmentId;

    @ApiModelProperty(value = "附件路径,最大长度为200")
    @Length(min = 0, max = 200, message = "附件路径格式错误,最大长度为200")
    private String fileFolder;

    @ApiModelProperty(value = "附件名称,最大长度为50")
    @Length(min = 0, max = 50, message = "附件名称格式错误,最大长度为50")
    private String fileName;

    @ApiModelProperty(value = "文件大小", example = "1")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型，如pdf,docx等,最大长度为16")
    @Length(min = 0, max = 16, message = "文件类型，如pdf,docx等格式错误,最大长度为16")
    private String fileType;

    @ApiModelProperty(value = "文件服务器路径,最大长度为200")
    @Length(min = 0, max = 200, message = "文件服务器路径格式错误,最大长度为200")
    private String storage;

    @ApiModelProperty(value = "文件服务器名称,最大长度为100")
    @Length(min = 0, max = 100, message = "文件服务器名称格式错误,最大长度为100")
    private String serverName;

    @ApiModelProperty(value = "0 正常， -1 删除 ，-2 覆盖", example = "1")
    private Integer operationFlag;

    @ApiModelProperty(value = "文件标签,最大长度为1000")
    @Length(min = 0, max = 1000, message = "文件标签格式错误,最大长度为1000")
    private String fileTags;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "最近修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "最近修改用户", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "文件存储方式")
    private String fileSysType;

}