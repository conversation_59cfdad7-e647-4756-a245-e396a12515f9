package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.page.bo.OrderParam;
import com.sct.tiaojie.repository.mdt.entity.MdtCase;
import com.sct.tiaojie.service.mdt.bo.*;
import com.sct.tiaojie.service.mdt.dto.*;
import com.sct.tiaojie.service.sys.dto.SysPhoneCheckRecordDto;
import com.sct.tiaojie.service.tmpl.dto.TmplDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/***********************************************************************************************************************
 * <p>
 *   调节案件信息 Mapper 接口
 * </p>
 * @copyright 2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2022-01-17
 * @version V1.0
 **********************************************************************************************************************/
@Mapper
public interface MdtCaseMapper extends BaseMapper<MdtCase> {

    /**
     * 查询当事人案件
     * @param loginName
     * @param mobile
     * @return
     */
    List<MdtCaseFrontDTO> frontList(@Param("loginName") String loginName, @Param("mobile") String mobile, @Param("idCardNo") String idCardNo);

    /**
     * 通过ID查询当事人案件
     * @param caseId
     * @return
     */
    MdtCaseFrontDTO getOneFront(@Param("caseId") Long caseId);

    /**
     * 获取盖章参数
     * @param caseId
     * @return
     */
    MdtBatchSignParam getSignParam(@Param("caseId") Long caseId);

    /**
     * 获取案件id列表
     * @param params
     * @param companyId
     * @return
     */
    List<Long> getCaseIds(@Param("params") MdtCasePageBO params, @Param("companyId") Long companyId);

    List<Map<String, Object>> selectFilePath(@Param("caseIds") List<Long> caseIds, @Param("params") MdtCasePageBO param, @Param("companyId") Long companyId);

    /**
     * 获取系统字段对应所有值
     * @param caseId
     * @return
     */
    Map<String, Object> getSysTempCase(@Param("caseId") Long caseId, @Param("meetingRecordId") Long meetingRecordId);

    /**
     * 分页查询案件列表新
     * @param page
     * @param param
     * @return
     */
    Page<MediateCasePageDTO> newPageList(Page<MediateCasePageDTO> page, @Param("param") MediateCasePageBO param, @Param("orderParams")List<OrderParam> orderParams);

    /**
     * 查询对应的模板ID
     * @param caseIds
     * @return
     */
    List<Integer> selectTmplCaseIds(@Param("caseIds") List<Long> caseIds);

    /**
     * 查询案件详情
     * @param caseId
     * @return
     */
    CaseDetail selectDetailCase(@Param("caseId") Long caseId);

    /**
     * 查询案件详情-批量查询
     * @param caseIdList
     */
    List<CaseDetail> selectDetailCaseList(@Param("caseIdList") List<Long> caseIdList,@Param("tmplId") Integer tmplId);

    /**
     * 获取案件接收日志分页数据
     */
    Page<MdtCaseCollectionRecordReceiveLogDto> getCaseCollectionRecordReviewLogPageList(Page<MdtCaseCollectionRecordReceiveLogDto> page, @Param("param") MdtCaseCollectionRecordReceiveLogBo param);

    /**
     * 案件详情----获取案件接收记录
     * @param caseId 案件id
     * @return
     */
    List<MdtCaseCollectionRecordReceiveLogDto> getCaseCollectionRecordReceiveLogListByCaseId(@Param("caseId") Long caseId);

    /**
     * 查询案件联系人号码等信息
     * @param caseIdList 案件id集合
     * @param identityTypeList 当事人身份
     * @param litigantTypeList 当事人类型
     *
     */
    List<SysPhoneCheckRecordDto> getUserConcatPhoneList(@Param("caseIdList") List<Long> caseIdList,
                                                        @Param("identityTypeList") List<String> identityTypeList,
                                                        @Param("litigantTypeList") List<String> litigantTypeList);

    /**
     * 获取案件联系人信息，联系人号码
     */
    List<MdtCaseBatchSmsDto> getCaseConcatPhone(@Param("param") MdtCaseBatchSmsBo mdtCaseBatchSmsBo);

    /**
     * 按案件id批量查询案件列表
     */
    List<MdtCase> listByCaseIds(@Param("caseIds") List<Long> caseIds);

    /**
     * 获取调解成功率统计
     * @param caseIds
     * @param groupCondition
     * @return
     */
    List<MediationSuccessRateDTO> getMediationSuccessRate(@Param("caseIds") List<Long> caseIds, @Param("groupCondition") Integer groupCondition);

    /**
     * 获取诉调分流率统计
     * @param caseIds
     * @param groupCondition
     * @return
     */
    List<DiversionRateDTO> getDiversionRate(@Param("caseIds") List<Long> caseIds, @Param("groupCondition") Integer groupCondition);

    List<CaseCloseRateDTO> getCaseCloseRate(@Param("caseIds") List<Long> caseIds, @Param("groupCondition") Integer groupCondition);

    List<CaseReachRateDTO> getCaseReachRate(@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime, @Param("groupCondition") Integer groupCondition);

    /**
     * 根据field values 定义的中间表及字段信息查询数据
     * @param tableName 查询目标表
     * @param keyFieldName 条件字段
     * @param valueFieldName 返回字段
     * @param value 查询值
     */
    List<String> getFieldDataByFieldValue(@Param("tableName") String tableName, @Param("keyFieldName") String keyFieldName,
                                          @Param("valueFieldName") String valueFieldName,@Param("value") String value);

    /**
     * 根据field values 定义的中间表及字段信息查询数据
     * @param tableName 查询目标表
     * @param keyFieldName 条件字段
     * @param valueFieldName 返回字段
     * @param values 查询值
     */
    List<String> getFieldDataByFieldValueList(@Param("tableName") String tableName, @Param("keyFieldName") String keyFieldName,
                                          @Param("valueFieldName") String valueFieldName,@Param("values") List values);

    /**
     * 查询需要导出的案件接收记录
     * @param mdtCaseCollectionRecordReceiveLogBo 查询条件
     */
    List<MdtCaseCollectionRecordReceiveLogDto> selectCollectionReceiveLog(@Param("param") MdtCaseCollectionRecordReceiveLogBo mdtCaseCollectionRecordReceiveLogBo);

    /**
     * 查询所有案件id
     * @param mediateCasePageBO 查询条件
     */
    List<Long> getAllListId(@Param("param") MediateCasePageBO mediateCasePageBO);

    /**
     * 根据模板id查询需要导出的案件id
     * @param caseIdList 案件id
     * @param tmplId 模板id
     */
    List<Long> selectCaseIdList(@Param("caseIdList") List<Long> caseIdList,@Param("tmplId") Integer tmplId);

    /**
    * @author: yiliang
    * @date: 2024/10/9 15:00
    * description: 获取自己权限内的案件模板
    */
    List<TmplDTO> tmplList(@Param("param") MediateCasePageBO bo);

    List<Long> selectCaseIdListByParam(@Param("param") MediateCasePageBO mediateCasePageBO ,@Param("tmplId") Integer tmplId);

    List<CaseDetail> selectCaseDetail(@Param("caseIdList") List<Long> caseIdList,@Param("tmplId") Integer tmplId);

    List<MdtCaseLitigantDTO> selectLitigantDetail(@Param("caseIdList") List<Long> caseIdList,@Param("tmplId") Integer tmplId);

    List<MdtCaseCollectRecordDTO> selectRecordDetail(@Param("caseIdList") List<Long> caseIdList,@Param("tmplId") Integer tmplId);

    List<MdtCaseCustomModuleDTO> selectCustomDetail(@Param("caseIdList") List<Long> caseIdList,@Param("tmplId") Integer tmplId);

    /**
     * 查询所有可见的案件
     * @param mediateCasePageBO
     * @return
     */
    List<MdtCase> getAllMdtCase(@Param("param") MediateCasePageBO mediateCasePageBO);

    List<MdtCase> getCreatorIdAndEntrustsDeptIds(@Param("param")MediateCasePageBO mediateCasePageBO);

    List<TmplDTO> tmplList2(@Param("param") MediateCasePageBO bo);

    List<MediateCasePageDTO> selectCaseLitigantInfo(@Param("caseIdList")Set<Long> caseIdList);
}
