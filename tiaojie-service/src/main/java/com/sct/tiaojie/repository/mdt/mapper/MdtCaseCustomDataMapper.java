package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.mdt.entity.MdtCaseCustomData;
import com.sct.tiaojie.service.mdt.dto.MdtCaseCustomModuleDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MdtCaseCustomDataMapper extends BaseMapper<MdtCaseCustomData> {


    //查询某个模块下的所有数据
    List<MdtCaseCustomData> getOneByModuleTitle(Long caseId,String moduleTitle);

}
