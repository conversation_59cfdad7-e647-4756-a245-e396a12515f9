package com.sct.tiaojie.repository.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.tmpl.entity.DeputyTmpl;
import com.sct.tiaojie.service.tmpl.bo.DeputyTmplParam;
import com.sct.tiaojie.service.tmpl.dto.DeputyTmplDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DeputyTmplMapper extends BaseMapper<DeputyTmpl> {

    /**
     * 查询副模板列表
     * @param param
     * @return
     */
    List<DeputyTmplDTO> deputyTmplList(@Param("param") DeputyTmplParam param);
}
