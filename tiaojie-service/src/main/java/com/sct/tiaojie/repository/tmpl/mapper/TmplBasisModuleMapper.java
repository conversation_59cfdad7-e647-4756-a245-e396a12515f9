package com.sct.tiaojie.repository.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.tmpl.entity.TmplBasisModule;
import com.sct.tiaojie.service.tmpl.dto.TmplBasisDetail;
import com.sct.tiaojie.service.tmpl.dto.TmplBasisModuleDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TmplBasisModuleMapper extends BaseMapper<TmplBasisModule> {

    /**
     * 查询基础模块字段列表
     * @return
     */
    List<TmplBasisModuleDetail> basisModuleFieldList();

}
