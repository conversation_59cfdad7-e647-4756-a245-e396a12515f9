package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysEntrusts;
import com.sct.tiaojie.service.sys.bo.SysEntrustsBO;
import com.sct.tiaojie.service.sys.bo.SysEntrustsPageBO;
import com.sct.tiaojie.service.sys.dto.SysEntrustsDTO;
import com.sct.tiaojie.service.sys.dto.SysEntrustsOrgRelaAccountDTO;
import com.sct.tiaojie.service.sys.dto.SysEntrustsPageDTO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SysEntrustsMapper extends BaseMapper<SysEntrusts> {

    /**
     * 分页查询委托方
     *
     * @param page
     * @param param
     */
    Page<SysEntrustsPageDTO> pageList(Page<SysEntrustsPageDTO> page, @Param("param") SysEntrustsPageBO param);

    /**
     * 案源方简单查询
     * @param param
     * @return
     */
    List<SysEntrustsDTO> getEntrusts(@Param("param") SysEntrustsBO param);

    /**
     * 根据委托方id查询委托方关联的账号
     * @param entrustId
     * @return
     */
    @Select("SELECT accu.account_id as accountId, accu.login_name as name, false as entrusts " +
            "FROM sys_entrusts ent " +
            "LEFT JOIN sys_entrusts_mdt_org_rela org on org.entrusts_id = ent.entrusts_id " +
            "LEFT JOIN sys_mdt_org mdtOrg on mdtOrg.org_id = org.org_id " +
            "LEFT JOIN auth_account accu on accu.company_id = mdtOrg.company_id " +
            "WHERE ent.entrusts_id = #{entrustId} and NOT ISNULL(accu.account_id) and NOT ISNULL(accu.login_name)")
    List<SysEntrustsOrgRelaAccountDTO> selectOrgUsers(@Param("entrustId") Long entrustId);

    /**
     * 获取所有案源方关联的账号
     * @return
     */
    @Select("SELECT accu.account_id as accountId, accu.login_name as name, false as entrusts " +
            "FROM sys_entrusts ent " +
            "LEFT JOIN sys_entrusts_mdt_org_rela org on org.entrusts_id = ent.entrusts_id " +
            "LEFT JOIN sys_mdt_org mdtOrg on mdtOrg.org_id = org.org_id " +
            "LEFT JOIN auth_account accu on accu.company_id = mdtOrg.company_id " +
            "WHERE NOT ISNULL(accu.account_id) and NOT ISNULL(accu.login_name)")
    List<SysEntrustsOrgRelaAccountDTO> getAllOrgUsers();

    /**
     * 通过案源方组织下的用户id获取案源方id
     * @param list
     * @return
     */
    List<Long> selectEntrustIdsByOrgUserIds(@Param("accountIds") Collection<Long> list);
}
