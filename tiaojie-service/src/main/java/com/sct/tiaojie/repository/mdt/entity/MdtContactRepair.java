package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 失联修复业务表;
 *
 * <AUTHOR> laolinjie
 * @date : 2024-7-11
 */
@ApiModel(value = "失联修复业务", description = "失联修复业务")
@TableName("mdt_contact_repair")
@Data
public class MdtContactRepair implements Serializable {
    /**
     * 修复id
     */
    @ApiModelProperty("修复id")
    @TableId
    private Long repairId;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    private String batchNo;
    /**
     * 操作调解员id
     */
    @ApiModelProperty("操作调解员id")
    private Long userId;
    /**
     * 操作调解员
     */
    @ApiModelProperty("操作调解员")
    private String userName;
    /**
     * 公司id
     */
    @ApiModelProperty("公司id")
    private Long companyId;
    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String companyName;
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String departmentName;
    /**
     * 上传失联修复数据原样记录
     */
    @ApiModelProperty("上传失联修复数据原样记录")
    private String uploadRequestBody;
    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private Date operationTime;
    /**
     * 批次的数量
     */
    @ApiModelProperty("批次的数量")
    private Integer batchCount;
    /**
     * 修复成功数
     */
    @ApiModelProperty("修复成功数")
    private Integer batchSuccessCount;
    /**
     * 批次修复成功时间
     */
    @ApiModelProperty("批次修复成功时间")
    private Date batchSuccessTime;
    /**
     * 批次有效期
     */
    @ApiModelProperty("批次有效期")
    private Date batchExpireDate;
    /**
     * 批次修复状态
     */
    @ApiModelProperty("批次修复状态")
    private String batchStatus;
    /**
     * 失败编码
     */
    @ApiModelProperty("失败编码")
    private String failCode;
    /**
     * 失败编码对应描述
     */
    @ApiModelProperty("失败编码对应描述")
    private String failMsg;
    /**
     * 匹配结果原样记录
     */
    @ApiModelProperty("匹配结果原样记录")
    private String matchResult;
    /**
     * 修复时间
     */
    @ApiModelProperty("修复时间")
    private Date repairTime;
    /**
     * 修复网络号(1.全网2.异网)
     */
    @ApiModelProperty("修复网络号(1.全网2.异网)")
    private Integer repairNetStatus;
}