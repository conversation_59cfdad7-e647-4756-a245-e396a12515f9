package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-08-03 17:24:15
 */
@Data
@TableName("sys_dict_data")
public class SysDictData implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Integer dictDataId;

	/**
	 * 父级ID
	 */
	private Integer parentId;

	/**
	 * 关联 sys_dict 主键
	 */
	private Integer dictId;
	/**
	 * 字典key
	 */
	private Integer dictKey;
	/**
	 * 字典值
	 */
	private String dictTag;
	/**
	 * 是否启用
	 */
	private Boolean enableFlag;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 创建者
	 */
	private Long creatorId;
	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;
	/**
	 * 更新者
	 */
	private Long updaterId;

	/**
	 * 是否系统标志
	 */
	private Integer sysFlag;

	private String businessType;

}
