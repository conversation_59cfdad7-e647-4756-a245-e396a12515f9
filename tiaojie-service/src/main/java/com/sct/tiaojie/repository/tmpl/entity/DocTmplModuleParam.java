package com.sct.tiaojie.repository.tmpl.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sct.tiaojie.service.tmpl.dto.TmplModuleFieldDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("文件模板映射参数")
public class DocTmplModuleParam {

    @ApiModelProperty("模板模块ID")
    private Integer tmplModuleId;

    @ApiModelProperty("模块标题")
    private String moduleTitle;

    @ApiModelProperty("模块对应表名")
    private String tableName;

    @ApiModelProperty("模板模块字段ID")
    private Integer tmplModuleFieldId;

    @ApiModelProperty("对应数据表字段名")
    private String fieldName;

    @ApiModelProperty("字段标题")
    private String fieldTitle;

    @ApiModelProperty("字段数据类型")
    private String fieldDataType;

    @ApiModelProperty("字段取值")
    private String fieldValues;

    @ApiModelProperty("字段原始值")
    private String originalName;

}
