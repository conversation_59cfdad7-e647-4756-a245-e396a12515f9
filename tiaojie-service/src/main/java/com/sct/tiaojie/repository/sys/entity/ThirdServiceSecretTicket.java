package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ThirdServiceSecretTicket {

    @ApiModelProperty(value = "ID", example = "1")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "服务名称")
    private String title;

    @ApiModelProperty(value = "厂商")
    private String vendor;

    @ApiModelProperty(value = "处理器")
    private String handler;

    @ApiModelProperty(value = "api提供商公开的服务文档地址")
    private String docUrl;

    @ApiModelProperty(value = "api提供者")
    private String apiProvider;

    @ApiModelProperty(value = "调用服务实际的url")
    private String apiUrl;

    @ApiModelProperty(value = "json数据,由处理器决定")
    private String secretTicket;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;
}
