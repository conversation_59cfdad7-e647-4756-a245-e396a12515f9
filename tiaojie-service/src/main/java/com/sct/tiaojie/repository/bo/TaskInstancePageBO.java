package com.sct.tiaojie.repository.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务实例分页查询条件
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "任务实例分页查询条件")
public class TaskInstancePageBO implements Serializable {

    @ApiModelProperty(value = "实例ID", example = "1")
    private Long instanceId;

    @ApiModelProperty(value = "案件ID", example = "1001")
    private Long caseId;

    //案件列表
    @ApiModelProperty(value = "案件ID列表")
    private List<Long> caseIdList;

    @ApiModelProperty(value = "流程ID", example = "2001")
    private Long workflowId;

    @ApiModelProperty(value = "配置ID", example = "3001")
    private Long configId;

    @ApiModelProperty(value = "任务类型", example = "1")
    private Integer taskType;

    @ApiModelProperty(value = "当前状态(1:未开始 2:进行中 3:已完成 4:异常)", example = "1")
    private Integer taskStatus;

    @ApiModelProperty(value = "发起人id", example = "10001")
    private Long assignAccountId;

    @ApiModelProperty(value = "完成人id", example = "10002")
    private Long finishAccountId;

    @ApiModelProperty(value = "发起时间开始")
    private LocalDateTime assignTimeStart;

    @ApiModelProperty(value = "发起时间结束")
    private LocalDateTime assignTimeEnd;

    @ApiModelProperty(value = "完成时间开始")
    private LocalDateTime finishTimeStart;

    @ApiModelProperty(value = "完成时间结束")
    private LocalDateTime finishTimeEnd;

    @ApiModelProperty(value = "创建时间开始")
    private LocalDateTime createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    private LocalDateTime createTimeEnd;
}
