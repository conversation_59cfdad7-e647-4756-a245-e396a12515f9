package com.sct.tiaojie.repository.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务办理业务对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "任务办理业务对象")
public class TaskInstanceProcessBo implements Serializable {

    @ApiModelProperty(value = "实例ID", required = true, example = "1")
    @NotNull(message = "实例ID不能为空")
    private Long instanceId;

    @ApiModelProperty(value = "任务状态", required = true, example = "2", 
                     notes = "必填：2-进行中，3-已完成，4-已完成（异常）")
    @NotNull(message = "任务状态不能为空")
    private Integer taskStatus;

    @ApiModelProperty(value = "备注", example = "任务处理说明",
                     notes = "当状态为'已完成（异常）'时必填")
    @Length(max = 500, message = "备注最大长度为500字符")
    private String remark;

//    @ApiModelProperty(value = "完成人ID", example = "10002",
//                     notes = "当状态为完成状态时必填")
//    private Long finishAccountId;
//
//    @ApiModelProperty(value = "完成时间", example = "2024-01-16T15:20:00",
//                     notes = "当状态为完成状态时必填")
//    private LocalDateTime finishTime;

    /**
     * 验证业务规则
     */
    public void validateBusinessRules() {
        // 验证异常完成时备注必填
        if (taskStatus != null && taskStatus == 4) {
            if (remark == null || remark.trim().isEmpty()) {
                throw new IllegalArgumentException("任务状态为'已完成（异常）'时，备注不能为空");
            }
        }

        // 验证完成状态时完成人和完成时间必填
//        if (taskStatus != null && (taskStatus == 3 || taskStatus == 4)) {
//            if (finishAccountId == null) {
//                throw new IllegalArgumentException("任务完成时，完成人不能为空");
//            }
//            if (finishTime == null) {
//                throw new IllegalArgumentException("任务完成时，完成时间不能为空");
//            }
//        }
    }
}
