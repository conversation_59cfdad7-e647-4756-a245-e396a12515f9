package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("盖章记录实体")
public class MdtSignRecord {

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "record_id",type = IdType.ASSIGN_ID)
    private Long recordId;

    @ApiModelProperty(value = "案件ID")
    private Long caseId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "仲裁委名称")
    private String commissionName;

    @ApiModelProperty(value = "盖章结果")
    private String result;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;
}
