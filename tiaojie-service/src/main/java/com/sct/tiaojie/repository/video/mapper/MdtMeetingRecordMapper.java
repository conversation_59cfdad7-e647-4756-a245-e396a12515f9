package com.sct.tiaojie.repository.video.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.video.entity.MdtMeetingRecord;
import com.sct.tiaojie.service.video.bo.MdtMeetingPageBO;
import com.sct.tiaojie.service.video.bo.MdtMeetingRecordPageBO;
import com.sct.tiaojie.service.video.dto.MdtMeetingDTO;
import com.sct.tiaojie.service.video.dto.MdtMeetingRecordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MdtMeetingRecordMapper extends BaseMapper<MdtMeetingRecord> {

    /**
     * 查询调解室
     * @param mediationId
     * @param meetingId
     * @return
     */
    @Select("select * from mdt_meeting_record where mediation_id = #{mediationId} and meeting_id = #{meetingId} and delete_flag = '0'")
    MdtMeetingRecord getMeeting(@Param("mediationId") String mediationId, @Param("meetingId") String meetingId);

    List<MdtMeetingDTO> meetingList(@Param("caseId") Long caseId);

    /**
     * 分页查询会议记录
     */
    Page<MdtMeetingRecordDTO> pageList(Page<MdtMeetingRecordDTO> page, @Param("param") MdtMeetingPageBO param);

    /**
     * 查询会议记录
     * @param mdtMeetingPageBO 查询条件
     */
    List<MdtMeetingRecordDTO> selectMeetingRecord(@Param("param") MdtMeetingPageBO mdtMeetingPageBO);

}
