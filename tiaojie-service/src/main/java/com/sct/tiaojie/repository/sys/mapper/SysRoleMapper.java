package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysRole;
import com.sct.tiaojie.service.sys.dto.SysRoleDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * <p>
 *   角色 Mapper 接口
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 通过用户ID获取所有角色
     * @param employeeId
     * @return
     */
    List<SysRoleDTO> getRoles(@Param("employeeId") Long employeeId);

    /**
     * 分页查询角色信息
     * @param page
     * @param param
     * @param companyId
     * @return
     */
    Page<SysRoleDTO> pageList(Page<SysRoleDTO> page, @Param("param") SysRoleDTO param, @Param("companyId") Long companyId);
}
