package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 调节案件信息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-17
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "调节案件信息实体")
@TableName(value = "mdt_case", autoResultMap = true)
@NoArgsConstructor
public class MdtCase extends AIHandle implements Serializable {

    @ApiModelProperty(value = "案件ID", example = "1")
    @TableId(value = "case_id", type = IdType.ASSIGN_ID)
    private Long caseId;

    @ApiModelProperty(value = "组织ID")
    private Long companyId;

    @ApiModelProperty(value = "案件批次ID", example = "1")
    private Long caseBatchId;

    @ApiModelProperty(value = "案件号,最大长度为50")
    @Length(min = 0, max = 50, message = "案件号格式错误,最大长度为50")
    private String caseNo;

    @ApiModelProperty(value = "案由,最大长度为255")
    @Length(min = 0, max = 255, message = "案由格式错误,最大长度为255")
    private String caseNatureContent;

    @ApiModelProperty(value = "案件标的")
    private BigDecimal caseSubjectMatter;

    @ApiModelProperty(value = "当前仲调员ID/调解员ID", example = "1")
    private Long currentMediatorId;

    @ApiModelProperty(value = "当前仲调员,最大长度为50")
    @Length(min = 0, max = 50, message = "当前仲调员格式错误,最大长度为50")
    private String currentMediatorName;

    @ApiModelProperty(value = "逻辑删除标志")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

    @ApiModelProperty(value = "调解办结发起时间(原结案时间)")
    private LocalDateTime closeTime;


    //new

    @ApiModelProperty(value = "调解状态")
    private String mediateStatus;

    @ApiModelProperty(value = "调解结果")
    private String mediateResult;

    @ApiModelProperty(value = "委托方ID（案源方）")
    private Long entrustsId;

    @ApiModelProperty(value = "委托方部门id")
    private Long entrustsDeptId;

    @ApiModelProperty(value = "调解组织ID")
    private Long orgId;

    @ApiModelProperty(value = "调解团队（部门）ID")
    private Long deptId;

    @ApiModelProperty(value = "案件状态新", example = "1")
    private String mdtCaseStatus;

    @ApiModelProperty(value = "新模板ID")
    private Integer tmplCaseId;

    @ApiModelProperty(value = "案件到期时间")
    private LocalDateTime expirationTime;

    @ApiModelProperty(value = "扩展字段1")
    private String extOne;

    @ApiModelProperty(value = "扩展字段2")
    private String extTwo;

    @ApiModelProperty(value = "扩展字段3")
    private String extThree;

    @ApiModelProperty(value = "终止原因")
    private String closeReason;

    @ApiModelProperty(value = "成功原因")
    private String successReason;

    @ApiModelProperty(value = "中止原因")
    private String suspendReason;


    @ApiModelProperty(value = "案管办结原因")
    private String caseManageCloseReason;

    @ApiModelProperty(value = "案管办结发起时间")
    private LocalDateTime caseManageCloseTime;

    @ApiModelProperty(value = "繁简分流打分")
    private Integer score;

    @ApiModelProperty(value = "案件名称")
    private String caseName;

    @ApiModelProperty(value = "案件已触达")
    private String caseTouched;

    @ApiModelProperty(value = "案件申请时间")
    private LocalDateTime caseApplyTime;

    @ApiModelProperty(value = "调解开始时间")
    private LocalDateTime mediateBeginTime;

    @ApiModelProperty(value = "办案审结")
    private Integer completeReview;

    @ApiModelProperty(value = "调解完成时间")
    private LocalDateTime mediateDoneTime;

    @ApiModelProperty(value = "案件关闭时间（案管/调解办结审核通过时间）")
    private LocalDateTime mediateCloseTime;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "调解类型")
    private String caseMediateType;

    @ApiModelProperty(value = "业务来源")
    private String businessSource;

    @ApiModelProperty(value = "调查提纲")
    private String mediateOutline;

    @ApiModelProperty(value = "案情梳理")
    private String caseReview;
}