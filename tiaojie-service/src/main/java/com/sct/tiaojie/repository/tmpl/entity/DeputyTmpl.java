package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("副模板表")
@TableName("deputy_tmpl")
public class DeputyTmpl {

    @TableId(value = "deputy_tmpl_id", type = IdType.AUTO)
    @ApiModelProperty(value = "副模板ID", required = true)
    @NotNull(message = "副模板ID不能为空")
    private Integer deputyTmplId;

    @ApiModelProperty("模板ID")
    private Integer tmplId;

    @ApiModelProperty("模板标题")
    private String tmplTitle;

    @ApiModelProperty("模板类型")
    private String tmplType;

    @ApiModelProperty("模板描述")
    private String tmplDesc;

    @ApiModelProperty("模板状态")
    private Integer tmplStatus;

    @ApiModelProperty("删除标识")
    private String isDelete;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建者")
    private Long creatorId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改者")
    private Long updaterId;
}
