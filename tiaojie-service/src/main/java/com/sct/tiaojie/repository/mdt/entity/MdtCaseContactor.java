package com.sct.tiaojie.repository.mdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;


/***********************************************************************************************************************
 * <p>
 * 调解案件联系人信息 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-24
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "调解案件联系人信息实体")
public class MdtCaseContactor implements Serializable {

    @ApiModelProperty(value = "主键", example = "1")
    @TableId(type = IdType.ASSIGN_ID)
    private Long contactId;

    @ApiModelProperty(value = "案件ID", example = "1")
    private Long caseId;

    @ApiModelProperty(value = "联系用户,最大长度为50")
    @Length(min = 0, max = 50, message = "联系用户格式错误,最大长度为50")
    private String contactorUser;

    @ApiModelProperty(value = "联系电话,最大长度为100")
    @Length(min = 0, max = 100, message = "联系电话格式错误,最大长度为100")
    private String contactPhone;

    @ApiModelProperty(value = "电话类型", example = "1")
    private Integer phoneType;

    @ApiModelProperty(value = "联系用户关系", example = "1")
    private Integer contactRela;

    @ApiModelProperty(value = "联系状态", example = "1")
    private Integer contactStatus;

    @ApiModelProperty(value = "备注,最大长度为1000")
    @Length(min = 0, max = 1000, message = "备注格式错误,最大长度为1000")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTm;

    @ApiModelProperty(value = "创建用户", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "最近修改时间")
    private LocalDateTime updateTm;

    @ApiModelProperty(value = "最近修改用户", example = "1")
    private Long updaterId;

}