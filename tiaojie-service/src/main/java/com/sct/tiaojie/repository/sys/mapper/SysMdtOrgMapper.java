package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.sys.entity.SysEntrusts;
import com.sct.tiaojie.repository.sys.entity.SysMdtOrg;
import com.sct.tiaojie.service.sys.bo.SysEntrustsPageBO;
import com.sct.tiaojie.service.sys.bo.SysMdtOrgBO;
import com.sct.tiaojie.service.sys.bo.SysMdtOrgPageBO;
import com.sct.tiaojie.service.sys.dto.SysEntrustsPageDTO;
import com.sct.tiaojie.service.sys.dto.SysMdtOrgDTO;
import com.sct.tiaojie.service.sys.dto.SysMdtOrgPageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SysMdtOrgMapper extends BaseMapper<SysMdtOrg> {
    /**
     * 分页查询调解组织
     *
     * @param page
     * @param param
     */
    Page<SysMdtOrgPageDTO> pageList(Page<SysMdtOrgPageDTO> page, @Param("param") SysMdtOrgPageBO param);


    /**
     * 调解组织简单查询
     * @param param
     * @return
     */
    List<SysMdtOrgDTO> getOrg(@Param("param") SysMdtOrgBO param);
}
