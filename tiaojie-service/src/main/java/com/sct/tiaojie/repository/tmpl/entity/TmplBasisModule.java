package com.sct.tiaojie.repository.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("基础模块表")
@TableName("tmpl_basis_module")
public class TmplBasisModule {

    @TableId(value = "basis_module_id", type = IdType.AUTO)
    @ApiModelProperty("基础模块ID")
    private Integer basisModuleId;

    @ApiModelProperty("模块标题")
    private String moduleTitle;

    @ApiModelProperty("模块描述")
    private String moduleDesc;

    @ApiModelProperty("模块类型")
    private String moduleType;

    @ApiModelProperty("模块对应表名")
    private String tableName;

    @ApiModelProperty("模块序号")
    private Integer moduleSn;

    @ApiModelProperty("是否必选")
    private Integer isFixed;

    @ApiModelProperty("是否允许多条记录")
    private Integer allowMultipleRecord;

}
