package com.sct.tiaojie.repository.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.auth.entity.AuthCompany;
import com.sct.tiaojie.service.auth.bo.CompanyBO;
import com.sct.tiaojie.service.auth.dto.AuthCompanyDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * <p>
 *   公司 Mapper 接口
 * </p>
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-09
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface AuthCompanyMapper extends BaseMapper<AuthCompany> {

    /**
     * 简单查询企业列表
     * @param param
     * @return
     */
    List<AuthCompanyDTO> getCompany(@Param("param") CompanyBO param);
}
