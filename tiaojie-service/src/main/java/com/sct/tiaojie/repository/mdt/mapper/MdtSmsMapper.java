package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtSms;
import com.sct.tiaojie.service.mdt.bo.MdtSmsPageBO;
import com.sct.tiaojie.service.mdt.dto.MdtSmsPageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/12 9:53
 * @Version 1.0
 */
public interface MdtSmsMapper extends BaseMapper<MdtSms> {
    /**
     * 分页查询短信发送记录
     */
    Page<MdtSmsPageDTO> pageList(Page<MdtSmsPageDTO> page, @Param("param") MdtSmsPageBO param, @Param("companyId") Long companyId);

    /**
     * 导出短信发送记录
     * @param smsIds
     * @param param
     * @param companyId
     * @return
     */
    List<MdtSmsPageDTO> export(@Param("smsIds") List<String> smsIds, @Param("param") MdtSmsPageBO param, @Param("companyId") Long companyId);
}
