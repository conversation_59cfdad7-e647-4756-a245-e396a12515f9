package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysAttachment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/***********************************************************************************************************************
 * <p>
 *   文件信息 Mapper 接口
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR> Panda
 * @date          2021-05-26
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface SysAttachmentMapper extends BaseMapper<SysAttachment> {

    @Update("update sys_attachment a set a.operation_flag =-1, updater_id = #{accountId} where a.file_folder=#{folder}")
    void updateFolderFileToDelete(@Param("folder") String folder, @Param("accountId") Long accountId);
}
