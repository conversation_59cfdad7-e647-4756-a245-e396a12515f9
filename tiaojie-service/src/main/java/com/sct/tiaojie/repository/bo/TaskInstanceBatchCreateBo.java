package com.sct.tiaojie.repository.bo;

import com.sct.tiaojie.service.mdt.bo.ChooseAllDataBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务登记/分派业务对象
 *
 * <AUTHOR>
 */

@Data
@ApiModel(value = "任务登记/分派业务对象")
public class TaskInstanceBatchCreateBo extends ChooseAllDataBo {

    /**
     * 案件id集合
     */
    @ApiModelProperty(value = "案件id集合")
    private List<Long> caseIdList;

    @ApiModelProperty(value = "任务类型", required = true, example = "1", notes = "必填项，单项下拉选择，从任务类型字典选择")
    @NotNull(message = "任务类型不能为空")
    private Integer taskType;

    @ApiModelProperty(value = "任务时限(小时)", example = "24", notes = "非必填项，限制正整数，单位：时")
    @Min(value = 1, message = "任务时限必须为正整数")
    private Integer timeLimit;

    @ApiModelProperty(value = "任务负责人ID集合", required = true, notes = "必填项，多选，来源于案源方账号")
    @NotNull(message = "任务负责人不能为空")
    @Size(min = 1, message = "至少需要指定一个任务负责人")
    private List<Long> managerIdList;

    //任务发起人id
    @ApiModelProperty(value = "任务发起人id", required = true, example = "10001")
    @NotNull
    private Long assignAccountId;

    //任务发起时间
    @NotNull
    private LocalDateTime assignTime;

}
