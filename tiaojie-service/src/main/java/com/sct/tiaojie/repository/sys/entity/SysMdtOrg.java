package com.sct.tiaojie.repository.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * maheng
 * 调解组织
 */
@Data
@ApiModel(value = "调解组织")
@TableName("sys_mdt_org")
public class SysMdtOrg implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "调解组织ID", example = "1")
    private Long orgId;

    @ApiModelProperty(value = "调解组织名称")
    private String orgName;

    @ApiModelProperty(value = "公司ID", example = "1")
    private Long companyId;

    @ApiModelProperty(value = "调解组织类型")
    private String orgType;

    @ApiModelProperty(value = "设立区域")
    private String createArea;

    @ApiModelProperty(value = "成立时间")
    private LocalDateTime buildTime;

    @ApiModelProperty(value = "指导机构")
    private String leadBody;

    @ApiModelProperty(value = "调解范围")
    private String mdtScope;

    @ApiModelProperty(value = "简介")
    private String intro;

    @ApiModelProperty(value = "负责人")
    private String principal;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "联系地址")
    private String address;

    @ApiModelProperty(value = "社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty(value = "设立机构类型")
    private String buildOrgType;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人", example = "1")
    private Long creatorId;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人", example = "1")
    private Long updaterId;

}