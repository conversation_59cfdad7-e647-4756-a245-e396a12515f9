package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysBusinessType;
import com.sct.tiaojie.repository.sys.entity.SysDict;
import com.sct.tiaojie.service.sys.dto.SysBusinessTypeDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SysBusinessTypeMapper extends BaseMapper<SysBusinessType> {
    /**
     * 查询所有业务类型
     * @return 业务类型列表
     */
    List<SysBusinessTypeDTO> selectAllBusinessTypes();
}
