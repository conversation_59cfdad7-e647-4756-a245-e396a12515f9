package com.sct.tiaojie.repository.mdt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sct.tiaojie.repository.mdt.entity.MdtCaseCollectRecord;
import com.sct.tiaojie.service.mdt.dto.MdtCaseMessagePushDTO;
import com.sct.tiaojie.service.mdt.bo.MdtCaseRecordPageBO;
import com.sct.tiaojie.service.mdt.dto.MdtCasePageDTO;
import com.sct.tiaojie.service.mdt.dto.MdtCaseRecordPageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/***********************************************************************************************************************
 * <p>
 *   调解案件催记信息 Mapper 接口
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          2022-01-24
 * @version       V1.0
 **********************************************************************************************************************/
@Mapper
public interface MdtCaseCollectRecordMapper extends BaseMapper<MdtCaseCollectRecord> {

    /**
     * 分页查询
     * @param page
     * @param param
     * @param companyId
     * @return
     */
    Page<MdtCaseRecordPageDTO> pageList(Page<MdtCaseRecordPageDTO> page, @Param("param") MdtCaseRecordPageBO param, @Param("companyId") Long companyId);

    List<MdtCaseRecordPageDTO> list(@Param("recordIds") List<Long> recordIds, @Param("param") MdtCaseRecordPageBO param, @Param("companyId") Long companyId);

    List<MdtCasePageDTO> getCaseToday(@Param("accountId") Long accountId, @Param("companyId") Long companyId);

    List<MdtCaseCollectRecord> selectMaxTimeRecords(Set<Long> caseIdList);

    List<MdtCaseMessagePushDTO> getCasePushToday();
}
