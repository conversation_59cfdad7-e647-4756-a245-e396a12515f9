package com.sct.tiaojie.repository.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.sys.entity.SysEntrustsMdtOrgRela;
import com.sct.tiaojie.repository.sys.entity.SysMdtOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SysEntrustsMdtOrgRelaMapper extends BaseMapper<SysEntrustsMdtOrgRela> {

    /**
     * 获取案源方关联的调解组织
     * @param entrustsId
     * @return
     */
    List<SysMdtOrg> getOrgList(@Param("entrustsId") Long entrustsId);

    List<SysMdtOrg> getOrgListByDeptId(@Param("deptId") Long deptId);
}
