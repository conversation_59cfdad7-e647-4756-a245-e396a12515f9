package com.sct.tiaojie.repository.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sct.tiaojie.repository.tmpl.entity.DeputyTmplMapping;
import com.sct.tiaojie.service.tmpl.dto.DeputyTmplDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface DeputyTmplMappingMapper extends BaseMapper<DeputyTmplMapping> {

    /**
     * 查询副模板映射字段
     * @param deputyTmplId
     * @return
     */
    DeputyTmplDetail mappingList(@Param("deputyTmplId") Integer deputyTmplId);

    /**
     * 查询模板映射字段
     * @param tmplModuleId 模板模块id
     */
    DeputyTmplDetail tmplList(@Param("tmplModuleId") Integer tmplModuleId);
}
