package com.sct.tiaojie.repository.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/***********************************************************************************************************************
 * <p>
 * 任务实例表 实体
 * </p>
 * @copyright     2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date          [CurrentDate]
 * @version       V1.0
 ***********************************************************************************************************************/
@Data
@ApiModel(value = "任务实例实体")
@TableName(value = "task_instance", autoResultMap = true)
@NoArgsConstructor
public class TaskInstance implements Serializable {

    @ApiModelProperty(value = "实例ID", example = "1")
    @TableId(value = "instance_id", type = IdType.AUTO)
    private Long instanceId;

    @ApiModelProperty(value = "案件ID", required = true, example = "1001")
    @TableField("case_id")
    private Long caseId;

    @ApiModelProperty(value = "流程ID", required = true, example = "2001")
    @TableField("workflow_id")
    private Long workflowId;

    @ApiModelProperty(value = "配置ID", required = true, example = "3001")
    @TableField("config_id")
    private Long configId;

    @ApiModelProperty(value = "任务类型", required = true, example = "1")
    @TableField("task_type")
    private Integer taskType;

    @ApiModelProperty(value = "任务时限(小时)", example = "24")
    @TableField("time_limit")
    private Integer timeLimit;

    //截至时间
    @TableField("deadline")
    private LocalDateTime deadline;

    @ApiModelProperty(value = "任务负责人account_id集合")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> managerIdList;

    @ApiModelProperty(value = "当前状态(1:未开始 2:进行中 3:已完成 4:异常)", example = "1")
    @TableField("task_status")
    private Integer taskStatus;

    @ApiModelProperty(value = "备注", example = "需要优先处理")
    @Length(min = 0, max = 500, message = "备注格式错误,最大长度为500")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "发起人id", required = true, example = "10001")
    @TableField("assign_account_id")
    private Long assignAccountId;

    @ApiModelProperty(value = "发起时间", required = true)
    @TableField("assign_time")
    private LocalDateTime assignTime;

    @ApiModelProperty(value = "完成人id", example = "10002")
    @TableField("finish_account_id")
    private Long finishAccountId;

    @ApiModelProperty(value = "完成时间")
    @TableField("finish_time")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "创建人", required = true, example = "10001")
    @TableField("creator_id")
    private Long creatorId;

    @ApiModelProperty(value = "更新人", required = true, example = "10002")
    @TableField("updater_id")
    private Long updaterId;

    @ApiModelProperty(value = "创建时间", required = true)
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", required = true)
    @TableField("update_time")
    private LocalDateTime updateTime;
}
