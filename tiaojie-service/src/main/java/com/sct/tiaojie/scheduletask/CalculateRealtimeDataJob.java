package com.sct.tiaojie.scheduletask;

import com.sct.tiaojie.common.enums.ScheduleEnum;
import com.sct.tiaojie.common.util.DistributedLockUtil;
import com.sct.tiaojie.repository.sys.entity.SysLog;
import com.sct.tiaojie.service.auth.AuthOperationStatisticsService;
import com.sct.tiaojie.service.call.CallStatisticService;
import com.sct.tiaojie.service.sys.SysLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description: 定时计算呼出各项实时数据
 * @Author: llz
 * @Date: 2024/7/15
 */
@Slf4j
@Component
public class CalculateRealtimeDataJob {

    @Resource
    private CallStatisticService callStatisticService;
    @Resource
    private AuthOperationStatisticsService authOperationStatisticsService;
    @Resource
    private DistributedLockUtil distributedLockUtil;

    //每天凌晨1点执行
    @Scheduled(cron = "0 0 1 * * ?")
    //测试
//    @Scheduled(cron = "0 0/1 * * * ?")
    public void calculateCallData() {
        log.info("【定时任务】计算昨天通话数据，开启....");
        distributedLockUtil.distributedMultipleDataSourcesLock(ScheduleEnum.CALL_RECORD_CALCULATE, callStatisticService::GenerateCallStatistic);
//        callStatisticService.GenerateCallStatistic();
        log.info("【定时任务】计算昨天通话数据，结束....");
    }


    //每天凌晨1点执行
    @Scheduled(cron = "0 0 1 * * ?")
    //测试
//    @Scheduled(cron = "0 0/1 * * * ?")
    public void calculateAuthOperationData() {
        log.info("【定时任务】用户昨天在线时长统计，开启....");
        distributedLockUtil.distributedMultipleDataSourcesLock(ScheduleEnum.AUTH_OPERATION_STATISTIC, authOperationStatisticsService::GenerateAuthOperationStatistic);
        log.info("【定时任务】用户昨天在线时长统计，结束....");
    }

}
