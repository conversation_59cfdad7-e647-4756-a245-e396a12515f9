package com.sct.tiaojie.scheduletask;

import com.sct.tiaojie.service.sys.SysMsgPushService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MessagePushTask {

    @Resource
    private  RedissonClient redissonClient;
    // 锁配置参数
    private static final long LOCK_WAIT = 0;       // 获取锁最大等待时间（秒）
    private static final long LOCK_LEASE = 300;     // 锁自动释放时间（秒）
    // 锁标识
    private static final String NEXT_DO_TASK_LOCK = "next_do_task_lock";


    @Resource
    private SysMsgPushService sysMsgPushService;

    @Scheduled(cron = "0 0 2 * * ?")
    public void nextDoTimePush(){
        RLock lock = redissonClient.getLock(NEXT_DO_TASK_LOCK);
        try {
            boolean locked = lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
            if (locked) {
                log.info("【调解记录下次跟进时间定时任务】开始执行，时间：{}", LocalDateTime.now());
                sysMsgPushService.nextDoTimeChanged();
                log.info("【消息提醒】调解时限倒计时1天消息提醒定时任务开始执行，时间：{}", LocalDateTime.now());
                sysMsgPushService.expirationTimePush();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【定时任务】执行异常", e);
        }

    }

}
