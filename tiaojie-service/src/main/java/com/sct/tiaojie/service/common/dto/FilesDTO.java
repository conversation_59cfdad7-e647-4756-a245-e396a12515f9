package com.sct.tiaojie.service.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FilesDTO {

    @ApiModelProperty(value = "文件ID")
    private Long fileId;

    @ApiModelProperty(value = "文件夹")
    private String fileFolder;

    @ApiModelProperty(value = "文件全路径")
    private String filePath;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "文件标签")
    private FileTagsDTO fileTags;

    @ApiModelProperty(value = "文件签名信息")
    private SignFileInfoDTO fileSignInfo;

    public FilesDTO() {
    }

    public FilesDTO(String fileName) {
        this.fileName = fileName;
    }


}
