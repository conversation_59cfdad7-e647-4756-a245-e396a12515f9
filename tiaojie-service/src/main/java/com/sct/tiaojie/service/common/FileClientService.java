package com.sct.tiaojie.service.common;


import com.sct.tiaojie.repository.sys.entity.SysAttachment;
import com.sct.tiaojie.service.common.dto.FileTagsDTO;
import com.sct.tiaojie.service.common.dto.FilesDTO;

import java.io.InputStream;
import java.util.List;

/**
 * 文件操作客户端服务类
 * 这里面将oss，minIo，本地文件操作功能封装到一起
 * <AUTHOR>
 */
public interface FileClientService {
    /**
     * 保存文件，bytes方式
     * @param bytes
     * @param filePath
     * @param userId
     */
    FilesDTO saveFile(String filePath, byte[] bytes, Long userId, String fileSysType);

    /**
     * 保存文件，inputStream方式
     * @param inputStream
     * @param fileSize
     * @param filePath
     * @param userId
     */
    FilesDTO saveFile(String filePath, InputStream inputStream, Long fileSize, Long userId, String fileSysType);

    /**
     * 保存文件，byte[]方式，增加文件标签
     * @param filePath 文件约定路径
     * @param bytes  文件内容
     * @param userId 帐号ID
     * @param fileTags 文件标签
     * @return
     */
    FilesDTO saveFile(String filePath, byte[] bytes, Long userId, FileTagsDTO fileTags, String fileSysType);

    /**
     * 保存文件，增加标签
     * @param filePath
     * @param inputStream
     * @param fileSize
     * @param userId
     * @param fileTags
     * @return
     */
    FilesDTO saveFile(String filePath, InputStream inputStream, Long fileSize, Long userId, FileTagsDTO fileTags, String fileSysType);

    /**
     * 下载文件到固定路径 absolutePath
     * @param filePath
     * @param absolutePath
     */
    void downloadFile(String filePath, String absolutePath);
    /**
     * 下载文件，返回byte数组
     * @param filePath
     * @return
     */
    byte[] downloadBytes(String filePath);
    /**
     * 下载文件，返回byte数组
     * @param fileId
     * @return
     */
    byte[] downloadBytesById(Long fileId);

    /**
     * 打开文件流
     * @param filePath
     * @return
     */
    InputStream openInputStream(String filePath);

    /**
     * 返回文件夹下的所有文件
     * @param folder
     * @return
     */
    List<FilesDTO> listFolderFiles(String folder);

    /**
     * 文件是否存在
     * @param filePath
     * @return
     */
    Boolean existsFile(String filePath);

    /**
     * 删除文件
     * @param path
     * @param userId
     */
    void deleteFile(String path, Long userId);


    /**
     *  OpenAPi专用
     * 删除文件通过文件ID
     * @param fileId 文件ID
     * @param userId 账号ID
     */
    void deleteFileByFileId(Long fileId, Long userId);

    /**
     * 复制文件到新的路径下
     * @param srcFilePath
     * @param destFilePath
     * @param userId
     */
    FilesDTO copyFileToNewFilePath(String srcFilePath, String destFilePath, Long userId);

    /**
     * 把文件夹下的文件（不包括子文件夹）复制到新的文件夹下
     * @param srcFolder
     * @param destFolder
     * @param userId
     */
    List<FilesDTO> copyFolderFilesToNewFolder(String srcFolder, String destFolder, Long userId);

    /**
     * 通过filePath查找文件，注意自己判断非空
     * @param filePath
     * @return 有文件返回文件数据，无返回null
     */
    FilesDTO findByFilePath(String filePath);
    /**
     * 通过filePath查找文件，注意自己判断非空
     * @param fullPath
     * @return 有文件返回文件数据，无返回null
     */
    SysAttachment findByFullPath(String fullPath);
    /**
     * 通过fileId查找文件，注意自己判断非空
     * @param fileId
     * @return 有文件返回文件数据，无返回null
     */
    FilesDTO findByFileId(Long fileId);

    FilesDTO addStampToFilePath(String filePath, FileTagsDTO fileTagsDTO);

    /**
     * 添加文件标签
     * @param filesDTO 文件信息
     * @param fileTags 文件标签
     * @return
     */
    FilesDTO addTags(FilesDTO filesDTO, FileTagsDTO fileTags);

    /**
     * 通过文件夹来删除
     * @param folder
     * @param userId
     */
    void deleteFileByFolder(String folder, Long userId);


    /***
     * 列出文件夹下所有文件（实体）
     * @param folder
     * @return
     */
    List<SysAttachment> listFolderFilesByEntity(String folder);

    FilesDTO saveSignedFile(String filePath, byte[] signBytes, Long userId, String fileSysType);

    /**
     * 保存文件信息
     * @param filePath
     * @param userId
     * @param fileSysType
     * @return
     */
    FilesDTO saveFileInfo(String filePath, Long userId, String fileSysType);
}
