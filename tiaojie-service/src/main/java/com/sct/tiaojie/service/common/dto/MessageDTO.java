package com.sct.tiaojie.service.common.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 消息实体
 * @author: Ma<PERSON><PERSON>
 * @date: 2020-8-28
 */
@Data
@Builder
public class MessageDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private T content;
    private String mqType;

    public MessageDTO(T content, String mqType) {
        this.content = content;
        this.mqType = mqType;
    }

    public MessageDTO() {
    }
}

