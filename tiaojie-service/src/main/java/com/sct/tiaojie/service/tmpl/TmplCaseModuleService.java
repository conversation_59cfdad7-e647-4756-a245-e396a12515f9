package com.sct.tiaojie.service.tmpl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sct.tiaojie.repository.tmpl.entity.TmplCaseModule;
import com.sct.tiaojie.service.tmpl.bo.TmplModuleBO;
import com.sct.tiaojie.service.tmpl.dto.TmplSaveDTO;

/**
 * <AUTHOR>
 */
public interface TmplCaseModuleService extends IService<TmplCaseModule> {

    /**
     * 保存模块
     * @param param
     * @param accountId
     * @return
     */
    TmplSaveDTO saveModule(TmplModuleBO param, Long accountId);

    /**
     * 删除模块
     * @param tmplModuleId
     * @param accountId
     * @return
     */
    TmplSaveDTO delete(Integer tmplModuleId, Long accountId);

    /**
     * 模块上移
     * @param tmplModuleId
     * @param accountId
     */
    void moveUp(Integer tmplModuleId, Long accountId);

    /**
     * 模块下移
     * @param tmplModuleId
     * @param accountId
     */
    void moveDown(Integer tmplModuleId, Long accountId);
}
