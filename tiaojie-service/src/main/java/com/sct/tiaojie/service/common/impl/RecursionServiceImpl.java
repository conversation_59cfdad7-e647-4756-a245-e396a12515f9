package com.sct.tiaojie.service.common.impl;


import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.service.common.RecursionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RecursionServiceImpl implements RecursionService {

    private static final String TREE_ID = "menuId";

    private static final String TREE_PARENT_ID = "parentMenuId";

    private static final String TREE_CHILDREN = "children";

    @Override
    public <T> List<T> generateTree(List<T> treeList, String topPid){
        if(CollectionUtils.isEmpty(treeList)){
            return new ArrayList<>();
        }

        if(topPid == null){
            log.info("@topPid is null!!!");
            throw new IllegalArgumentException("topPid can not be null");
        }

        return getMenuTrees(treeList, topPid);

    }

    private <T> List<T> getMenuTrees(List<T> treeDTOList, String topId) {
        List<T> treeList = new ArrayList<>();

        treeDTOList.stream().filter(parent -> topId.equals(getValue(parent, TREE_PARENT_ID))).forEach(parent -> {
            setValue(parent, TREE_CHILDREN, getChildNodes(parent, treeDTOList));
            treeList.add(parent);
        });

        return treeList;
    }

    private <T> List<T> getChildNodes(T parent, List<T> treeDTOList) {

        List<T> rtList = new ArrayList<>();
        List<T> childTree = treeDTOList.stream().
                filter(treeParam -> getValue(parent, TREE_ID).equals(getValue(treeParam, TREE_PARENT_ID))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(childTree)) {
            return rtList;
        }

        for (T child : childTree) {
            List<T> children = getChildNodes(child, treeDTOList);
            setValue(child, TREE_CHILDREN, children);
            rtList.add(child);
        }
        return rtList;
    }

    private void setValue(Object object, String key, Object value){
        try {
            Class c = object.getClass();
            Field field = c.getDeclaredField(key);
            if(field == null){
                throw new ValidateBusinessException("[没有属性:{}]", key);
            }
            field.setAccessible(true);
            field.set(object, value);
        } catch (Exception e) {
            log.error("[设置属性失败:{}]", key, e);
            throw new ValidateBusinessException("[设置属性失败:{}]", key);
        }
    }

    private String getValue(Object object, String key){
        try {
            Class c = object.getClass();
            Field field = c.getDeclaredField(key);
            if(field == null){
                throw new ValidateBusinessException("[没有属性:{}]", key);
            }
            field.setAccessible(true);

            Object fieldValue = field.get(object);

            if(fieldValue != null){
                return fieldValue.toString();
            }
            return "";

        } catch (Exception e) {
            log.error("[获取属性失败:{}]", key, e);
            throw new ValidateBusinessException("[获取属性失败:{}]", key);
        }
    }

}
