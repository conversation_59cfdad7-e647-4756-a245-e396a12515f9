package com.sct.tiaojie.service.video;

import com.sct.tiaojie.service.video.bo.MdtMeetingLitigantBO;
import com.sct.tiaojie.service.video.bo.MeetingRecordFileBO;
import com.sct.tiaojie.service.video.dto.MdtMeetingLitigantDTO;

/**
 * <AUTHOR>
 */
public interface MdtMeetingService {

    /**
     * 加入调解室，获取链接
     * @param caseId
     * @param accountId
     * @return
     */
    String joinMediation(Long caseId, Long accountId);

    /**
     * 获取当事人信息
     * @param param
     * @return
     */
    MdtMeetingLitigantDTO getLitigantInfo(MdtMeetingLitigantBO param);

    /**
     * 获取视频文件下载地址
     * @param param
     * @return
     */
    String getMeetingRecordFilePath(MeetingRecordFileBO param);

    /**
     * 下载调解视频文件
     * @param downloadMeetingVideoPath 文件地址
     */
    byte[] downloadMeetingVideo(String downloadMeetingVideoPath);

}
