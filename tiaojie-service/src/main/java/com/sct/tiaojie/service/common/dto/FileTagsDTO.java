package com.sct.tiaojie.service.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * 文件标签，可以自行添加属性，返回给前端是json
 * {'stamped':''.....}
 */
@Data
public class FileTagsDTO {

    /**
     * 是否盖章
     */
    @ApiModelProperty(value = "是否盖章")
    private Boolean stamped;
    /**
     * 是否签名
     */
    @ApiModelProperty(value = "是否签名")
    private Boolean signed;
    /**
     * 是否转成的PDF
     */
    @ApiModelProperty(value = "是否转成的PDF")
    private Boolean changed;

    /**
     * 是否申请盖章
     */
    @ApiModelProperty(value = "是否申请盖章")
    private Boolean applyStamp;


    /**
     * 是否是系统生成
     */
    @ApiModelProperty(value = "是否是系统生成")
    private Boolean isSystemGenerate;

    /**
     * 生成时设置的盖章类型
     */
    @ApiModelProperty(value = "设置的盖章类型")
    private String stampType;

}
