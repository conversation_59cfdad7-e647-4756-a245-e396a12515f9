package com.sct.tiaojie.service.common;

import com.sct.tiaojie.service.common.dto.FilesDTO;

/**
 * <AUTHOR>
 * @Date 2022/4/24 12:55
 * @Version 1.0
 */
public interface ConvertToPdfService {
    /**
     * docx 转PDF
     * @param filePath
     * @param accountId
     * @return
     */
    FilesDTO covertDocxToPdf(String filePath, Long accountId, Boolean isPreview);

    /**
     * 转PDF，传入要转的文件 filePath
     * @param filePath
     * @return
     */
    byte[] convertToPdf(String filePath);

    /**
     * 转PDF，传入要转的文件byte[],返回成功的PDF byte[]
     * @param fileBytes
     * @param fileName
     * @return
     */
    byte[] convertToPdf(byte[] fileBytes, String fileName);
}
