package com.sct.tiaojie.service.video.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class MdtMeetingRecordDTO {

    @ApiModelProperty("ID")
    @ExcelIgnore
    private String recordId;

    @ApiModelProperty("案件ID")
    @ExcelIgnore
    private String caseId;

    @ApiModelProperty("案件号")
    @ExcelProperty("案件号")
    private String caseNo;

    @ApiModelProperty("案源方")
    @ExcelProperty("案件号")
    private String entrustsName;

    @ApiModelProperty("会议名称")
    @ExcelProperty("会议名称")
    private String meetingName;

    @ApiModelProperty("会议发起人")
    @ExcelProperty("会议发起人")
    private String mediatorName;

    @ApiModelProperty("会议时间")
    @ExcelProperty("会议时间")
    private LocalDateTime mediationTime;

    @ApiModelProperty("所属组织")
    @ExcelProperty("所属组织")
    private String companyName;

    @ApiModelProperty("参会人")
    @ExcelProperty("参会人")
    private String participant;

    @ApiModelProperty("参会人身份")
    @ExcelProperty("参会人身份")
    private String participantType;

    @ApiModelProperty("会议状态")
    @ExcelIgnore
    private String meetingStatus;

    /**
     * 会议状态-转换-导出
     */
    @ApiModelProperty(value = "会议状态",hidden = true)
    @ExcelProperty("会议状态")
    private String meetingStatusReplace;

    @ApiModelProperty("视频文件地址")
    @ExcelProperty("视频文件地址")
    private String filePath;

}
