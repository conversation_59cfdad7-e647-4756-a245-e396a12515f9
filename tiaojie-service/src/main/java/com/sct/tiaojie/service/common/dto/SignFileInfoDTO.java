package com.sct.tiaojie.service.common.dto;

import com.sct.tiaojie.service.sign.dto.SignFileRecordDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SignFileInfoDTO {

    @ApiModelProperty(value = "签名二维码图片路径")
    private String fileSignQrcodeFilePath;

    @ApiModelProperty(value = "状态：1签名中，2待签名，3已完成, 4已作废")
    private Integer status;

    @ApiModelProperty(value = "是否可以签名")
    private Boolean canSignFlag;

    @ApiModelProperty(value = "签名完成时间")
    private LocalDateTime signCompleteTime;

    @ApiModelProperty(value = "签名记录")
    private List<SignFileRecordDTO> signFileRecords;

}
