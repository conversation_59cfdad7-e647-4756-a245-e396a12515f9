package com.sct.tiaojie.service.common.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sct.tiaojie.common.FileContentTypeEnum;
import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.common.contants.OperationFlagEnum;
import com.sct.tiaojie.common.contants.SctConstants;
import com.sct.tiaojie.common.contants.ServerNameEnum;
import com.sct.tiaojie.common.copy.CglibMapper;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.common.util.ValidateUtils;
import com.sct.tiaojie.config.EnvConfig;
import com.sct.tiaojie.repository.sign.entity.SignFileInfo;
import com.sct.tiaojie.repository.sign.entity.SignFileRecord;
import com.sct.tiaojie.repository.sys.entity.SysAttachment;
import com.sct.tiaojie.service.common.FileClientService;
import com.sct.tiaojie.service.common.FileService;
import com.sct.tiaojie.service.common.dto.FileTagsDTO;
import com.sct.tiaojie.service.common.dto.FilesDTO;
import com.sct.tiaojie.service.common.dto.SignFileInfoDTO;
import com.sct.tiaojie.service.sign.SignFileInfoService;
import com.sct.tiaojie.service.sign.SignFileRecordService;
import com.sct.tiaojie.service.sign.dto.SignFileRecordDTO;
import com.sct.tiaojie.service.sys.SysAttachmentService;
import com.sct.tiaojie.util.BaseDirUtils;
import com.sct.tiaojie.util.FilePathUtils;
import com.sct.tiaojie.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileClientServiceImpl implements FileClientService {

    private static final String CASE_PATH_FLAG = "/case-";

    @Resource
    private SysAttachmentService attachmentService;
    @Resource
    private EnvConfig envConfig;
    @Resource(name = "signFileInfoService")
    private SignFileInfoService signFileInfoService;
    @Resource
    private SignFileRecordService signFileRecordService;
    @Value("${sct.oss.bucketName}")
    private String bucketName;
    @Resource
    private FileContext fileContext;

    private FileService fileService;

    @PostConstruct
    public void init() {
        fileService = fileContext.getFileService();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FilesDTO saveFile(String filePath, byte[] bytes, Long userId, String fileSysType) {
        return saveFile(filePath, bytes, userId, null, fileSysType);
    }

    private FilesDTO buildFilesDTO(SysAttachment entity) {
        FilesDTO filesDTO = new FilesDTO();
        filesDTO.setFileId(entity.getAttachmentId());
        filesDTO.setFilePath(entity.getFileFolder() + entity.getFileName());
        filesDTO.setFileName(entity.getFileName());
        filesDTO.setFileFolder(entity.getFileFolder());
        FileTagsDTO fileTagsDTO = tagToFileTagsDTO(entity.getFileTags());
        filesDTO.setFileTags(fileTagsDTO);
        SignFileInfoDTO signInfo = getSignInfo(filesDTO.getFilePath());
        filesDTO.setFileSignInfo(signInfo);
        return filesDTO;
    }

    private SignFileInfoDTO getSignInfo(String filePath) {
        SignFileInfo availableFileSignInfo = signFileInfoService.getAvailableFileSignInfo(filePath);
        if (availableFileSignInfo == null) {
            return null;
        }
        List<SignFileRecord> records = signFileRecordService.list(new QueryWrapper<SignFileRecord>().lambda()
                .eq(SignFileRecord::getFileSignInfoId, availableFileSignInfo.getFileSignInfoId()));
        SignFileInfoDTO r = CglibMapper.copy(availableFileSignInfo, SignFileInfoDTO.class);
        r.setSignFileRecords(CglibMapper.copyList(records, SignFileRecordDTO.class));
        return r;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FilesDTO saveFile(String filePath, InputStream inputStream, Long fileSize, Long userId, String fileSysType) {
        return saveFile(filePath, inputStream, fileSize, userId, null, fileSysType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FilesDTO saveFile(String filePath, byte[] bytes, Long userId, FileTagsDTO fileTags, String fileSysType) {
        if (null == bytes) {
            throw new ValidateBusinessException("文件为空");
        }

        filePath = FilePathUtils.formatFilePath(filePath);
        String storage = getServerStorage(filePath);

        try {
            fileService.saveFile(storage, bytes);
        } catch (Exception e) {
            handleFileException("保存文件出错", e);
        }

        String tagStr = tagToJsonStr(fileTags);
        int length = bytes.length;
        SysAttachment entity = saveFileInfo(filePath, storage, userId, tagStr, Long.parseLong(String.valueOf(length)));
        return buildFilesDTO(entity);
    }

    @Override
    public FilesDTO saveSignedFile(String filePath, byte[] bytes, Long userId, String fileSysType) {
        ValidateUtils.notNull(bytes, "文件为空");
        filePath = FilePathUtils.formatFilePath(filePath);
        String storage = getServerStorage(filePath);

        try {
            fileService.saveFile(storage, bytes);
        } catch (Exception e) {
            handleFileException("保存文件出错", e);
        }

        int length = bytes.length;
        updateSignedFileInfo(filePath, userId, OperationFlagEnum.OVERRIDE.getCode());
        SysAttachment entity = createFileInfo(filePath, storage, Long.parseLong(String.valueOf(length)), userId,
                OperationFlagEnum.NORMAL.getCode(), null);
        return buildFilesDTO(entity);
    }

    @Override
    public FilesDTO saveFileInfo(String filePath, Long userId, String fileSysType) {
        String storage = filePath;
        if (FileSysType.OSS.equals(fileSysType)) {
            storage = bucketName + "/" + storage;
        }
        SysAttachment entity = saveFileInfo(filePath, storage, userId, null, fileSysType);
        return buildFilesDTO(entity);
    }

    @Override
    public FilesDTO saveFile(String filePath, InputStream inputStream,
                             Long fileSize, Long userId, FileTagsDTO fileTags, String fileSysType) {
        ValidateUtils.notNull(inputStream, "文件流为空");
        filePath = FilePathUtils.formatFilePath(filePath);
        String storage = getServerStorage(filePath);
        try {
            fileService.saveFile(storage, inputStream, getFileContentType(filePath));
        } catch (Exception e) {
            handleFileException("保存文件出错", e);
        }

        String tagStr = tagToJsonStr(fileTags);
        SysAttachment entity = saveFileInfo(filePath, storage, userId, tagStr, fileSize);
        return buildFilesDTO(entity);
    }

    private String tagToJsonStr(FileTagsDTO fileTagsDTO) {
        if (fileTagsDTO == null) {
            return null;
        }
        return JacksonUtils.writeValueAsString(fileTagsDTO);
    }

    private FileTagsDTO tagToFileTagsDTO(String tags) {
        if (tags == null) {
            return new FileTagsDTO();
        }
        try {
            return JacksonUtils.readValue(tags, FileTagsDTO.class);
        } catch (Exception e) {
            log.error("文件标签转JSON失败:" + tags, e);
        }
        return new FileTagsDTO();
    }

    @Override
    public void downloadFile(String filePath, String absolutePath) {
        if (ObjectUtils.isEmpty(absolutePath)) {
            throw new ValidateBusinessException("下载路径不能为空");
        }

        filePath = FilePathUtils.formatFilePath(filePath);

        String storage = getStorageByFilePath(filePath);
        if (storage == null) {
            throw new ValidateBusinessException("下载文件不存在：filePath:" + filePath);
        }

        try {
            fileService.downloadFile(storage,absolutePath);
        } catch (Exception e) {
            handleFileException("下载文件到本地失败", e);
        }

    }

    @Override
    public byte[] downloadBytes(String filePath) {
        filePath = FilePathUtils.formatFilePath(filePath);

        String storage = getStorageByFilePath(filePath);
        if (storage == null) {
            throw new ValidateBusinessException("下载文件不存在：filePath:" + filePath);
        }
        try {
            return fileService.downloadFile(storage);
        } catch (Exception e) {
            handleFileException("下载文件失败", e);
        }
        return null;
    }

    @Override
    public byte[] downloadBytesById(Long fileId) {
        String storage = getStorageByFileId(fileId);
        if (storage == null) {
            throw new ValidateBusinessException("下载文件不存在");
        }

        try {
            return fileService.downloadFile(storage);
        } catch (Exception e) {
            handleFileException("下载文件失败", e);
        }
        return null;
    }

    @Override
    public InputStream openInputStream(String filePath) {
        filePath = FilePathUtils.formatFilePath(filePath);

        String storage = getStorageByFilePath(filePath);
        if (storage == null) {
            throw new ValidateBusinessException("下载文件不存在：filePath:" + filePath);
        }

        try {
            return fileService.openInputStream(storage);
        } catch (Exception e) {
            handleFileException("下载文件失败", e);
        }

        return null;
    }

    @Override
    public List<FilesDTO> listFolderFiles(String folder) {
        List<FilesDTO> fileList = new ArrayList<>();
        if (ObjectUtils.isEmpty(folder)) {
            return fileList;
        }
        List<SysAttachment> SysAttachmentList = findByFolder(folder);
        if (CollectionUtils.isEmpty(SysAttachmentList)) {
            return fileList;
        }
        SysAttachmentList.forEach(SysAttachment -> {
            FilesDTO filesDTO = buildFilesDTO(SysAttachment);
            fileList.add(filesDTO);
        });
        return fileList;
    }

    @Override
    public Boolean existsFile(String filePath) {
        if (ObjectUtils.isEmpty(filePath)) {
            return false;
        }

        filePath = FilePathUtils.formatFilePath(filePath);

        SysAttachment SysAttachment = findOneByFilePath(filePath);
        return SysAttachment != null;
    }

    @Override
    public void deleteFile(String filePath, Long userId) {
        if (ObjectUtils.isEmpty(filePath)) {
            throw new ValidateBusinessException("删除文件路径为空,filePath:" + filePath);
        }
        filePath = FilePathUtils.formatFilePath(filePath);
        updateFileInfo(filePath, userId, OperationFlagEnum.DELETE.getCode());
    }

    @Override
    public void deleteFileByFileId(Long fileId, Long userId) {
        FilesDTO filesDTO = findByFileId(fileId);
        if (filesDTO == null) {
            throw new ValidateBusinessException("文件不存在FileKey:" + fileId);
        }
        String filePath = FilePathUtils.formatFilePath(filesDTO.getFilePath());
        updateFileInfo(filePath, userId, OperationFlagEnum.DELETE.getCode());
    }

    private List<SysAttachment> findByFolder(String folder) {
        folder = FilePathUtils.formatFolder(folder);

        List<SysAttachment> list = attachmentService.list(new QueryWrapper<SysAttachment>().lambda()
                .eq(SysAttachment::getFileFolder, folder)
                .eq(SysAttachment::getOperationFlag, OperationFlagEnum.NORMAL.getCode()));
        list.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        return list;
    }

    /**
     * 覆盖文件
     */
    private void updateFileInfo(String filePath, Long userId, int operationFlag) {
        List<SysAttachment> attachmentEntities = findListByFilePath(filePath);
        String pdfFolder = FilenameUtils.getPath(filePath) + SctConstants.PDF_HIDE_PATH;
        List<SysAttachment> pdfAttachments = findByFolder(pdfFolder);
        if (!pdfAttachments.isEmpty()) {
            attachmentEntities.addAll(pdfAttachments);
        }
        for (SysAttachment entity : attachmentEntities) {
            entity.setUpdaterId(userId);
            entity.setOperationFlag(operationFlag);
            attachmentService.saveOrUpdate(entity);
            //覆盖时也将原电子签名文件记录设为无效
            signFileInfoService.obsoleteFileSignInfoByPath(filePath, userId);
        }

        if (OperationFlagEnum.DELETE.getCode() == operationFlag) {
            for (SysAttachment entity : attachmentEntities) {
                String storage = entity.getStorage();
                //是否删除成功不理，数据库已经标识为删除，前端无法获取
                fileService.deleteFile(storage);
            }
        }
    }

    private void updateSignedFileInfo(String filePath, Long userId, Integer operationFlag) {
        List<SysAttachment> attachmentEntities = findListByFilePath(filePath);
        for (SysAttachment entity : attachmentEntities) {
            entity.setUpdaterId(userId);
            entity.setOperationFlag(operationFlag);
            attachmentService.saveOrUpdate(entity);
        }

        if (OperationFlagEnum.DELETE.getCode().equals(operationFlag)) {
            for (SysAttachment entity : attachmentEntities) {
                String storage = entity.getStorage();
                //是否删除成功不理，数据库已经标识为删除，前端无法获取
                fileService.deleteFile(storage);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FilesDTO copyFileToNewFilePath(String srcFilePath, String destFilePath, Long userId) {

        srcFilePath = FilePathUtils.formatFilePath(srcFilePath);

        SysAttachment sysAttachment = findOneByFilePath(srcFilePath);
        if (sysAttachment == null) {
            throw new ValidateBusinessException("该路径下没有文件：srcFilePath:" + srcFilePath);
        }

        String srcStorage = sysAttachment.getStorage();

        destFilePath = FilePathUtils.formatFilePath(destFilePath);
        String destStorage = getServerStorage(destFilePath);

        try {
            fileService.downloadFile(srcStorage, destStorage);
        } catch (Exception e) {
            handleFileException("复制文件失败", e);
        }

        SysAttachment entity = saveFileInfo(destFilePath, destStorage, userId, sysAttachment.getFileTags(), sysAttachment.getFileSize());
        return buildFilesDTO(entity);
    }

    @Override
    public List<FilesDTO> copyFolderFilesToNewFolder(String srcFolder, String destFolder, Long userId) {
        if (srcFolder.contains(".") || destFolder.contains(".")) {
            throw new ValidateBusinessException("只允许文件夹的复制");
        }
        List<SysAttachment> SysAttachmentList = findByFolder(srcFolder);
        List<FilesDTO> filesDTOS = new ArrayList<>();

        if (CollectionUtils.isEmpty(SysAttachmentList)) {
            return filesDTOS;
//            throw new ValidateBusinessException("原文件夹为空");
        }
        destFolder = FilePathUtils.formatFolder(destFolder);

        for (SysAttachment entity : SysAttachmentList) {
            String destFile = destFolder + entity.getFileName();
            SysAttachment sysAttachment =
                    saveFileInfo(destFile, entity.getStorage(), userId, entity.getFileTags(), entity.getFileSize());
            filesDTOS.add(buildFilesDTO(sysAttachment));
        }
        return filesDTOS;

    }

    @Override
    public FilesDTO findByFilePath(String filePath) {
        filePath = FilePathUtils.formatFilePath(filePath);
        SysAttachment SysAttachment = findOneByFilePath(filePath);
        if (SysAttachment == null) {
            return null;
        }
        return buildFilesDTO(SysAttachment);
    }

    @Override
    public SysAttachment findByFullPath(String fullPath) {
        fullPath = FilePathUtils.formatFilePath(fullPath);
        return findOneByFilePath(fullPath);
    }

    @Override
    public FilesDTO findByFileId(Long fileId) {
        SysAttachment sysAttachment = findOneByFileId(fileId);
        if (sysAttachment == null) {
            return null;
        }
        return buildFilesDTO(sysAttachment);
    }

    @Override
    public FilesDTO addStampToFilePath(String filePath, FileTagsDTO fileTagsDTO) {
        String tags = tagToJsonStr(fileTagsDTO);
        SysAttachment sysAttachment = findOneByFilePath(filePath);
        sysAttachment.setFileTags(tags);
        attachmentService.save(sysAttachment);
        return buildFilesDTO(sysAttachment);
    }

    @Override
    public FilesDTO addTags(FilesDTO filesDTO, FileTagsDTO newFileTags) {
        String tags = tagToJsonStr(newFileTags);
        SysAttachment sysAttachment = findOneByFileId(filesDTO.getFileId());
        sysAttachment.setFileTags(tags);
        attachmentService.updateById(sysAttachment);
        return buildFilesDTO(sysAttachment);
    }

    @Override
    public void deleteFileByFolder(String folder, Long userId) {
        List<FilesDTO> filesDTOS = this.listFolderFiles(folder);
        for (FilesDTO filesDTO : filesDTOS) {
            this.deleteFile(filesDTO.getFilePath(), userId);
        }
        attachmentService.updateFolderFileToDelete(folder, userId);
//        for(SysAttachment SysAttachment : list){
//            SysAttachment.setOperationFlag(OperationFlagEnum.DELETE.getCode());
//            attachmentRepository.save(SysAttachment)
//        }
    }

    private SysAttachment saveFileInfo(String filePath, String storage, Long userId,
                                       String fileTags, Long fileSize) {
        updateFileInfo(filePath, userId, OperationFlagEnum.OVERRIDE.getCode());
        return createFileInfo(filePath, storage, fileSize, userId, OperationFlagEnum.NORMAL.getCode(), fileTags);
    }

    private SysAttachment saveFileInfo(String filePath, String storage, Long userId,
                                       String fileTags, String fileSysType) {
        updateFileInfo(filePath, userId, OperationFlagEnum.OVERRIDE.getCode());
        return createFileInfo(filePath, storage, fileSysType, userId, OperationFlagEnum.NORMAL.getCode(), fileTags);
    }

    private SysAttachment createFileInfo(String filePath, String storage, Long fileSize,
                                         Long userId, int operationFlag, String fileTags) {
        String fileName = FilenameUtils.getName(filePath);
        String fileType = FilenameUtils.getExtension(fileName);
        String folder = FilenameUtils.getFullPath(filePath);

        SysAttachment sysAttachment = new SysAttachment();
        sysAttachment.setFileName(fileName);
        sysAttachment.setFileType(fileType);
        sysAttachment.setFileFolder(folder);
        sysAttachment.setFileSize(fileSize);
        sysAttachment.setServerName(ServerNameEnum.GLUSTERFS.name());
        sysAttachment.setOperationFlag(operationFlag);
        sysAttachment.setFileTags(fileTags);
        sysAttachment.setStorage(storage);
        sysAttachment.setUpdaterId(userId);
        sysAttachment.setCreatorId(userId);
        attachmentService.saveOrUpdate(sysAttachment);
        return sysAttachment;
    }

    private SysAttachment createFileInfo(String filePath, String storage, String fileSysType,
                                         Long userId, int operationFlag, String fileTags) {
        String fileName = FilenameUtils.getName(filePath);
        String fileType = FilenameUtils.getExtension(fileName);
        String folder = FilenameUtils.getFullPath(filePath);

        SysAttachment sysAttachment = new SysAttachment();
        sysAttachment.setFileName(fileName);
        sysAttachment.setFileType(fileType);
        sysAttachment.setFileFolder(folder);
        sysAttachment.setServerName(ServerNameEnum.GLUSTERFS.name());
        sysAttachment.setOperationFlag(operationFlag);
        sysAttachment.setFileTags(fileTags);
        sysAttachment.setStorage(storage);
        sysAttachment.setUpdaterId(userId);
        sysAttachment.setCreatorId(userId);
        sysAttachment.setFileSysType(fileSysType);
        attachmentService.save(sysAttachment);
        return sysAttachment;
    }

    private void handleFileException(String msg, Exception e) {
        log.error(msg, e);
        throw new ValidateBusinessException(msg, e);
    }

    private String getServerStorage(String filePath) {
        String pathPre = getBaseDir() == null ? "" : getBaseDir();

        if (checkIsCaseFile(filePath)) {
            String caseId = getCaseIdFromPath(filePath);
            String caseFolder = getCaseFolder(caseId);
            filePath = caseFolder + filePath;
        }
        if (filePath.startsWith("/")) {
            return pathPre + filePath.substring(1);
        }
        return pathPre + filePath;
    }

    private String getCaseFolder(String caseId) {
        long caseIdLong;
        try {
            caseIdLong = Long.parseLong(caseId, 16);
        } catch (Exception e) {
            caseIdLong = Math.abs((long) caseId.hashCode());
        }

        return fileService.getRandomFolder(caseIdLong);
    }

    private String getBaseDir() {
        return fileService.getBaseDir();
    }

    private List<SysAttachment> findListByFilePath(String filePath) {
        String fileName = FilenameUtils.getName(filePath);
        String folder = FilenameUtils.getFullPath(filePath);

        return attachmentService.list(new QueryWrapper<SysAttachment>().lambda()
                .eq(SysAttachment::getFileFolder, folder)
                .eq(SysAttachment::getFileName, fileName)
                .eq(SysAttachment::getOperationFlag, OperationFlagEnum.NORMAL.getCode()));
    }

    private SysAttachment findOneByFilePath(String filePath) {
        String fileName = FilenameUtils.getName(filePath);
        String folder = FilenameUtils.getFullPath(filePath);

        return attachmentService.getOne(new QueryWrapper<SysAttachment>().lambda()
                .eq(SysAttachment::getFileFolder, folder)
                .eq(SysAttachment::getFileName, fileName)
                .eq(SysAttachment::getOperationFlag, OperationFlagEnum.NORMAL.getCode()));
    }

    private SysAttachment findOneByFileId(Long fileId) {
        return attachmentService.getById(fileId);
    }


    private String getStorageByFilePath(String filePath) {
        SysAttachment sysAttachment = findOneByFilePath(filePath);
        if (sysAttachment == null) {
            return null;
        }

        String storage = sysAttachment.getStorage();
        return formatStorage(storage);

    }

    private String getStorageByFileId(Long fileId) {
        SysAttachment sysAttachment = findOneByFileId(fileId);
        if (sysAttachment == null) {
            return null;
        }
        return sysAttachment.getStorage();

    }

    private String formatStorage(String storage) {
        String env = envConfig.getEnv();
        if (SctConstants.DEV_ENV.equals(env)) {
            if (storage.startsWith("d:")) {
                return storage;
            }
            return "d:/" + storage.replace("/data/", "");
        } else if (SctConstants.TEST_ENV.equals(env)) {
            if (!storage.startsWith("Z:")) {
                return storage;
            }
            return "/data/" + storage.replace("d:/data/", "");
        }
        log.info("storage->" + storage);
        return storage;
    }

    private Boolean checkIsCaseFile(String filePath) {
        return null != filePath && filePath.contains(CASE_PATH_FLAG);
    }

    private String getCaseIdFromPath(String filePath) {
        if (null == filePath || !filePath.contains(CASE_PATH_FLAG)) {
            return null;
        }

        String reg = "/case-[a-zA-Z0-9]+/";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(filePath);
        if (matcher.find()) {
            String caseFileStr = matcher.group(0);
            return caseFileStr.replaceAll("/", "").replace("case-", "");
        }

        return null;
    }

    private String getOrgId(Long platfromId) {
        // todo 目前只考虑单仲裁机构部署，所以默认返回1，后续若考虑云部署接纳多个司法部，这里可以考虑换成司法部机构ID
//        List<SysCommission> baseCommissionInfoEntities = commissionService.list();
//        if(CollectionUtils.isEmpty(baseCommissionInfoEntities)){
//            throw new ValidateBusinessException("机构信息为空");
//        }
        // fixme
        return String.valueOf(platfromId);

    }

    @Override
    public List<SysAttachment> listFolderFilesByEntity(String folder) {
        List<SysAttachment> sysAttachmentList = new ArrayList<>();

        if (ObjectUtils.isEmpty(folder)) {
            return sysAttachmentList;
        }

        sysAttachmentList = findByFolder(folder);

        CollectionUtils.isEmpty(sysAttachmentList);

        return sysAttachmentList;
    }

    private static String getFileContentType(String fileName) {
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        return FileContentTypeEnum.getFileContentType(fileType);
    }


}
