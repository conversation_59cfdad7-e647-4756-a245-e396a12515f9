package com.sct.tiaojie.service.common.impl;

import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.service.common.FileService;
import com.sct.tiaojie.util.GlusterFsStore;
import io.minio.*;
import io.minio.messages.Bucket;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Service(FileSysType.MINIO)
@Slf4j
public class MinIoFileServiceImpl implements FileService {

    @Resource
    private GlusterFsStore glusterFsStore;
    @Value("${sct.minio.url}")
    private String url;
    @Value("${sct.minio.accessKey}")
    private String accessKey;
    @Value("${sct.minio.secretKey}")
    private String secretKey;
    @Value("${sct.minio.bucketName}")
    private String bucketName;

    private static MinioClient minioClient;

    @Override
    public void saveFile(String filePath, InputStream inputStream) {
        try {
            PutObjectArgs objectArgs = PutObjectArgs.builder().bucket(bucketName)
                    .object(filePath).stream(inputStream, inputStream.available(), -1).build();
            minioClient.putObject(objectArgs);
        } catch (Exception e) {
            handleFileException("保存文件出错", e);
        }
    }

    @Override
    public void saveFile(String filePath, byte[] bytes) {
        saveFile(filePath, new ByteArrayInputStream(bytes));
    }

    @Override
    public void saveFile(String filePath, InputStream inputStream, String fileType) {
        try {
            PutObjectArgs objectArgs = PutObjectArgs.builder().bucket(bucketName)
                    .object(filePath).stream(inputStream, inputStream.available(), -1).contentType(fileType).build();
            minioClient.putObject(objectArgs);
        } catch (Exception e) {
            handleFileException("保存文件出错", e);
        }
    }

    @Override
    public byte[] downloadFile(String filePath) {
        filePath = getFilePath(filePath);
        try {
            InputStream object = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName)
                    .object(filePath).build());
            byte[] bytes;
            try (InputStream inputStream = object) {
                bytes = IOUtils.toByteArray(inputStream);
            } catch (IOException e) {
                throw new ValidateBusinessException(e.getMessage());
            }
            return bytes;
        } catch (Exception e) {
            handleFileException("下载文件失败", e);
        }
        return null;
    }

    @Override
    public void downloadFile(String filePath, String absolutePath) {
        throw new ValidateBusinessException("暂不支持");
    }

    @Override
    public InputStream openInputStream(String filePath) {
        byte[] bytes = downloadFile(filePath);
        return new ByteArrayInputStream(bytes);
    }

    @Override
    public void deleteFile(String storage) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName)
                    .object(storage).build());
        } catch (Exception e) {
            handleFileException("删除文件失败", e);
        }
    }

    @Override
    public String getUploadCenterType() {
        return FileSysType.MINIO;
    }

    @Override
    public String getRandomFolder(Long num) {
        return glusterFsStore.getRandomFolder(num);
    }

    @Override
    public String getBaseDir() {
        return null;
    }

    /**
     * 判断 bucket是否存在
     *
     * @param bucketName: 桶名
     * @return: boolean
     * @date : 2020/8/16 20:53
     */
    @SneakyThrows(Exception.class)
    public static boolean bucketExists(String bucketName) {
        return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
    }


    /**
     * 获取全部bucket
     *
     * @param :
     * @return: java.util.List<io.minio.messages.Bucket>
     * @date : 2020/8/16 23:28
     */
    @SneakyThrows(Exception.class)
    public static List<Bucket> getAllBuckets() {
        return minioClient.listBuckets();
    }

    /**
     * 初始化minio配置
     *
     * @param :
     * @return: void
     * @date : 2020/8/16 20:56
     */
    @PostConstruct
    public void init() {
        try {
            minioClient = MinioClient.builder()
                    .endpoint(url)
                    .credentials(accessKey, secretKey)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("初始化minio配置异常: 【{}】", e.fillInStackTrace());
        }
    }

    private void handleFileException(String msg, Exception e) {
        log.error(msg, e);
        throw new ValidateBusinessException(msg, e);
    }

    private String getFilePath(String filePath) {
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }
        if (filePath.startsWith(bucketName)) {
            filePath = filePath.substring(bucketName.length());
        }
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }
        return filePath;
    }
}
