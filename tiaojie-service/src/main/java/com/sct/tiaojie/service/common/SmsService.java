package com.sct.tiaojie.service.common;


import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponse;
import com.aliyun.dysmsapi20170525.models.SendBatchSmsResponse;
import com.sct.tiaojie.service.common.bo.MessageCallBackBO;
import com.sct.tiaojie.service.common.dto.MultiSenderResult;
import com.sct.tiaojie.service.common.dto.SingSenderResult;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;

import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * @copyright 2019 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2020-02-12 14:58
 * @version V1.0
 * @description 短信发送服务
 **********************************************************************************************************************/
public interface SmsService {
    /**
     * 根据模板单发短信
     * @param mobile 接收手机号
     * @param templateId 腾讯生成的模板ID
     * @param params 模板中的参数
     * @return
     */
    SendBatchSmsResponse senderSingleSmsByTemplate(List<String> mobile, String templateId, List<Map<String, String>> params);

    /**
     * 查询阿里短信发送详情
     * @param bizNo
     * @param phone
     * @param sendDate
     * @return
     */
    QuerySendDetailsResponse getSmsDetail(String bizNo, String phone, String sendDate);

    /**
     * 单发短信(短信内容必须是已审核的模板)
     * @param currentUserId 发送短信的账户id 可以为null
     * @param bizId 业务id       可以为null
     * @param mobile 接收手机号
     * @param message 短信内容
     * @return
     */
    SingSenderResult sendSms(Long currentUserId, Long bizId, String mobile, String message);

}
