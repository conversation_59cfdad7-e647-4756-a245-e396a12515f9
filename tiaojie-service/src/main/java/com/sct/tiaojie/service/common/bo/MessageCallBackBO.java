package com.sct.tiaojie.service.common.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/***********************************************************************************************************************
 * @copyright 2019 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2020-02-11 9:44
 * @version V1.0
 * @description
 **********************************************************************************************************************/
@Data
@ApiModel("短信下发回调参数")
public class MessageCallBackBO {

    @ApiModelProperty("用户实际接收到短信的时间")
    private String userReceiveTime;

    @ApiModelProperty("国家（或地区）码")
    private String nationcode;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("实际是否收到短信接收状态，SUCCESS（成功）、FAIL（失败）")
    private String reportStatus;

    @ApiModelProperty("用户接收短信状态码错误信息")
    private String errmsg;

    @ApiModelProperty("用户接收短信状态描述")
    private String description;

    @ApiModelProperty("本次发送标识 ID")
    private String sid;

}
