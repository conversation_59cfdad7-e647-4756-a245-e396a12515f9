package com.sct.tiaojie.service.common.impl;


import com.sct.tiaojie.common.contants.AttachmentEnum;
import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.manage.threadlocal.ServiceThreadLocal;
import com.sct.tiaojie.repository.sys.entity.SysAttachment;
import com.sct.tiaojie.service.common.CommonFileService;
import com.sct.tiaojie.service.common.ConvertToPdfService;
import com.sct.tiaojie.service.common.FileClientService;
import com.sct.tiaojie.service.common.dto.FileTagsDTO;
import com.sct.tiaojie.service.common.dto.FilesDTO;
import com.sct.tiaojie.util.FilePathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommonFileServiceImpl implements CommonFileService {

    @Resource
    private FileClientService fileClientService;
    @Resource
    private ConvertToPdfService convertToPdfService;

    @Override
    public FilesDTO saveFile(String filePath, MultipartFile file, Long userId) {
        try {

            return fileClientService.saveFile(filePath, file.getInputStream(), file.getSize(), userId, FileSysType.LOCAL);
        } catch (IOException e) {
            log.error("保存文件失败", e);
            throw new ValidateBusinessException("保存文件失败", e);
        }
    }


    @Override
    public FilesDTO saveFile(String filePath, byte[] bytes, Long userId, String fileSysType) {
        return fileClientService.saveFile(filePath, bytes, userId, fileSysType);
    }

    @Override
    public FilesDTO saveFile(String filePath, InputStream inputStream, Long fileSize, Long userId, String fileSysType) {
        return fileClientService.saveFile(filePath, inputStream, fileSize, userId, fileSysType);
    }

    @Override
    public FilesDTO saveFile(String filePath, byte[] bytes, Long userId, FileTagsDTO fileTags, String fileSysType) {
        return fileClientService.saveFile(filePath, bytes, userId, fileTags, fileSysType);
    }

    @Override
    public FilesDTO saveFile(String filePath, InputStream inputStream, Long fileSize, Long userId, FileTagsDTO fileTags, String fileSysTyoe) {
        return fileClientService.saveFile(filePath, inputStream, fileSize, userId, fileTags, fileSysTyoe);
    }

    @Override
    public FilesDTO addTags(FilesDTO filesDTO, FileTagsDTO fileTags) {
        return fileClientService.addTags(filesDTO, fileTags);
    }

    @Override
    public void downloadFile(String filePath, String absolutePath) {
        fileClientService.downloadFile(filePath, absolutePath);
    }

    @Override
    public byte[] downloadBytes(String filePath) {
        return fileClientService.downloadBytes(filePath);
    }

    @Override
    public byte[] downloadBytesById(Long fileId) {
        return fileClientService.downloadBytesById(fileId);
    }

    @Override
    public InputStream openInputStream(String filePath) {
        return fileClientService.openInputStream(filePath);
    }

    @Override
    public List<FilesDTO> listFolderFiles(String folder) {

        List<FilesDTO> filesDTOS = fileClientService.listFolderFiles(folder);
        if (CollectionUtils.isEmpty(filesDTOS)) {
            return filesDTOS;
        }

        AttachmentEnum attachmentEnum = FilePathUtils.getAttachmentEnum(folder);
        if (attachmentEnum != null && attachmentEnum.getUniqueFlag()) {
            List<FilesDTO> one = new ArrayList<>();
            one.add(filesDTOS.get(0));
            return one;
        }

        return filesDTOS;
    }

    @Override
    public FilesDTO findLatestOneByFolder(String folder) {
        List<FilesDTO> filesDTOS = listFolderFiles(folder);
        if (CollectionUtils.isEmpty(filesDTOS)) {
            return null;
        }
        return filesDTOS.get(0);
    }

    @Override
    public Boolean existsFile(String filePath) {
        return fileClientService.existsFile(filePath);
    }

    @Override
    public void deleteFile(String path, Long userId) {
        fileClientService.deleteFile(path, userId);
    }

    @Override
    public void deleteByFolder(String folder, Long userId) {
        fileClientService.deleteFileByFolder(folder, userId);
    }

    @Override
    public void deleteFileByFileId(Long fileId, Long userId) {
        fileClientService.deleteFileByFileId(fileId, userId);
    }

    @Override
    public FilesDTO copyFileToNewFilePath(String srcFilePath, String destFilePath, Long userId) {
        return fileClientService.copyFileToNewFilePath(srcFilePath, destFilePath, userId);
    }

    @Override
    public List<FilesDTO> copyFolderFilesToNewFolder(String srcFolder, String destFolder, Long userId) {
        return fileClientService.copyFolderFilesToNewFolder(srcFolder, destFolder, userId);
    }

    @Override
    public FilesDTO findByFilePath(String filePath) {
        return fileClientService.findByFilePath(filePath);
    }

    @Override
    public FilesDTO findByFileId(Long fileId) {
        return fileClientService.findByFileId(fileId);
    }

    @Override
    public FilesDTO addStampToFilePath(String filePath, FileTagsDTO fileTagsDTO) {
        return fileClientService.addStampToFilePath(filePath, fileTagsDTO);
    }

    @Override
    public List<SysAttachment> listFolderFilesByEntity(String folder) {

        List<SysAttachment> filesDtos = fileClientService.listFolderFilesByEntity(folder);
        if (CollectionUtils.isEmpty(filesDtos)) {
            return filesDtos;
        }

        AttachmentEnum attachmentEnum = FilePathUtils.getAttachmentEnum(folder);
        if (attachmentEnum != null && attachmentEnum.getUniqueFlag()) {
            List<SysAttachment> one = new ArrayList<>();
            one.add(filesDtos.get(0));
            return one;
        }

        return filesDtos;
    }

    @Override
    public SysAttachment findByFullPath(String fullPath) {
        return fileClientService.findByFullPath(fullPath);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FilesDTO saveFileAndConvertPdf(String filePath, MultipartFile file, Long accountId) {
        FilesDTO filesDTO = saveFile(filePath, file, accountId);
        return convertToPdfService.covertDocxToPdf(filesDTO.getFilePath(), accountId, false);
    }

    @Override
    public FilesDTO saveFile(String filePath, byte[] bytes, Long userId, String fileSysType, FileTagsDTO fileTags) {
        return fileClientService.saveFile(filePath, bytes, userId, fileTags, fileSysType);
    }

    @Override
    public FilesDTO saveSignedFile(String filePath, byte[] signBytes, Long defaultSysId, String fileSysType) {
        return fileClientService.saveSignedFile(filePath, signBytes, ServiceThreadLocal.getCurrentAccountId(), fileSysType);
    }

    @Override
    public FilesDTO saveFileInfo(String filePath, Long userId, String fileSysType) {
        return fileClientService.saveFileInfo(filePath, userId, fileSysType);
    }

}
