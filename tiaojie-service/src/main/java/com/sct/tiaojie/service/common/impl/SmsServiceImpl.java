package com.sct.tiaojie.service.common.impl;

import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponse;
import com.aliyun.dysmsapi20170525.models.SendBatchSmsResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sct.tiaojie.common.contants.ParameterModuleCode;
import com.sct.tiaojie.common.contants.ParameterParamCode;
import com.sct.tiaojie.common.contants.SctConstants;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.config.CloudTencentSmsConfig;
import com.sct.tiaojie.manage.threadlocal.ServiceThreadLocal;
import com.sct.tiaojie.repository.sys.entity.SysSmsSend;
import com.sct.tiaojie.repository.sys.mapper.SysSmsSendMapper;
import com.sct.tiaojie.service.common.SmsService;
import com.sct.tiaojie.service.common.bo.MessageCallBackBO;
import com.sct.tiaojie.service.common.dto.MultiSenderResult;
import com.sct.tiaojie.service.common.dto.MultiSmsDetail;
import com.sct.tiaojie.service.common.dto.SingSenderResult;
import com.sct.tiaojie.service.sys.SysParameterService;
import com.sct.tiaojie.service.sys.dto.SysParameterDTO;
import com.sct.tiaojie.util.JacksonUtils;
import com.sct.tiaojie.util.MessageCloudTencentUtil;
import com.sct.tiaojie.util.SmsCloudTencentUtil;
import com.sct.tiaojie.util.SplitMobileAndParamsUtil;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/***********************************************************************************************************************
 * @copyright 2019 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2020-02-12 15:02
 * @version V1.0
 * @description 腾讯云短信接口实现
 **********************************************************************************************************************/
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class SmsServiceImpl implements SmsService {

    @Autowired
    private SysSmsSendMapper smsSendMapper;
    @Resource
    private SmsCloudTencentUtil smsCloudTencentUtil;
    @Resource
    private SysParameterService sysParameterService;

    public static final String SEND_SUCCESS_CODE = "0";

    private static final String NATION_CODE = "86";

    @Value("${spring.profiles.active}")
    private String profile;

    private SingSenderResult sendSingleSms(String mobile, String message) {
        SysParameterDTO parameter = sysParameterService.getSysParameterByModuleAndParam(ParameterModuleCode.CLIENT, ParameterParamCode.SMS_SIGN);
        String signName = parameter == null ? CloudTencentSmsConfig.sign : parameter.getParamValue();
        return MessageCloudTencentUtil.senderSms(mobile, message, profile, signName);
    }


    @Override
    public SendBatchSmsResponse senderSingleSmsByTemplate(List<String> mobile, String templateId, List<Map<String, String>> params){
        SplitMobileAndParamsUtil.splitMobileAndParams(mobile, params);
        return smsCloudTencentUtil.sendSmsByTemplate(templateId, params, mobile);
    }

    @Override
    public QuerySendDetailsResponse getSmsDetail(String bizNo, String phone, String sendDate){
        return smsCloudTencentUtil.getSmsDetail(bizNo, phone, sendDate);
    }

    @Override
    public SingSenderResult sendSms(Long currentUser, Long bizId, String mobile, String message) {
        // 处理空值用户id
        if (currentUser == null) {
            currentUser = SctConstants.DEFAULT_SYSTEM_USER;
        }
        SingSenderResult singSenderResult = sendSingleSms(mobile, message);
        if(!Objects.equals(SEND_SUCCESS_CODE, singSenderResult.getResult())){
            // 发送是失败
            log.error("@sms send error:{}", singSenderResult.getErrmsg());
            return singSenderResult;
        }
        // 做租户入库 短信记录
        SysSmsSend smsSend = new SysSmsSend();
        smsSend.setSmsId(singSenderResult.getSid());
        smsSend.setBizId(bizId);
        smsSend.setCreatorId(currentUser);
        smsSend.setReceivePhone(mobile);
        smsSend.setCompanyId(1L);
        //默认中国地区号码
        smsSend.setNationCode(NATION_CODE);
        smsSend.setSmsContent(message);
        smsSend.setStatusCode(singSenderResult.getResult());
        smsSendMapper.insert(smsSend);
        // 发送成功
        return singSenderResult;
    }

}
