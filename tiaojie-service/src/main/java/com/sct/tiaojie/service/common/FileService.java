package com.sct.tiaojie.service.common;

import com.sct.tiaojie.repository.sys.entity.SysAttachment;

import java.io.InputStream;

/**
 * 文件操作服务
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 保存文件
     * @param filePath
     * @param inputStream
     */
    void saveFile(String filePath, InputStream inputStream);

    /**
     * 保存文件
     * @param filePath
     * @param bytes
     */
    void saveFile(String filePath, byte[] bytes);

    /**
     * 保存文件
     * @param filePath
     * @param inputStream
     */
    void saveFile(String filePath, InputStream inputStream, String fileType);

    /**
     * 下载文件
     * @param filePath
     * @return
     */
    byte[] downloadFile(String filePath);

    /**
     * 下载文件到固定路径 absolutePath
     * @param filePath
     * @param absolutePath
     */
    void downloadFile(String filePath, String absolutePath);

    /**
     * 打开文件流
     * @param filePath
     * @return
     */
    InputStream openInputStream(String filePath);

    /**
     * 删除文件
     * @param storage
     */
    void deleteFile(String storage);

    /**
     * 获取存储文件服务器类型
     */
    String getUploadCenterType();

    /**
     * 获取随机文件夹
     */
    String getRandomFolder(Long num);

    /**
     * 获取根目录
     */
    String getBaseDir();
}
