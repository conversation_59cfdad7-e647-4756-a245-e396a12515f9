package com.sct.tiaojie.service.common.impl;

import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.common.contants.SystemConstants;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.common.resource.FileByteArrayResource;
import com.sct.tiaojie.service.common.CommonFileService;
import com.sct.tiaojie.service.common.ConvertToPdfService;
import com.sct.tiaojie.service.common.dto.FileTagsDTO;
import com.sct.tiaojie.service.common.dto.FilesDTO;
import com.sct.tiaojie.util.DataSourceUtils;
import com.sct.tiaojie.util.FilePathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/4/24 12:58
 * @Version 1.0
 */
@Slf4j
@Service
public class ConvertToPdfServiceImpl implements ConvertToPdfService {
    @Value("${pdf.convertToPdf}")
    private String PDF_URL;

    @Resource
    RestTemplate restTemplate;
    @Resource
    CommonFileService commonFileService;
    @Resource
    private DataSourceUtils dataSourceUtils;

    @Override
    public FilesDTO covertDocxToPdf(String filePath, Long accountId, Boolean isPreview) {

        String folder = FilenameUtils.getFullPath(filePath);
        folder = FilePathUtils.formatFolder(folder);
        if(isPreview){
            folder = folder + SystemConstants.PDF_PREVIEW_PATH;
        }
        String fileName = FilenameUtils.getName(filePath);
        String pdfFileName = FilenameUtils.getBaseName(fileName) + ".pdf";

        String fullPath = folder + pdfFileName;
        byte[] pdfBytes = convertToPdf(filePath);
        FileTagsDTO fileTagsDTO = new FileTagsDTO();
        fileTagsDTO.setChanged(true);
        //多线程切数据源
        FilesDTO filesDTO = commonFileService.saveFile(fullPath, pdfBytes, accountId, fileTagsDTO, FileSysType.LOCAL);
        if(!isPreview) {
            //把原DOC换个路径
            String anotherDocPath = folder + SystemConstants.DOC_HIDE_PATH + fileName;
            commonFileService.copyFileToNewFilePath(filePath, anotherDocPath, accountId);
            commonFileService.deleteFile(filePath, accountId);
        }
        return filesDTO;
    }

    @Override
    public byte[] convertToPdf(String filePath) {
        String fileName = FilenameUtils.getName(filePath);
        byte[] bytes = commonFileService.downloadBytes(filePath);
        return convertToPdf(bytes, fileName);
    }

    @Override
    public byte[] convertToPdf(byte[] fileBytes, String fileName) {
        try {
            if (null == fileBytes || fileBytes.length == 0) {
                throw new ValidateBusinessException("文件不能为空！");
            }
            log.info("PDF URL:" + PDF_URL);
            log.info("FILE_NAME:" + fileName);
            String fileExtension = FilenameUtils.getExtension(fileName);
            fileName = "test." + fileExtension;
            MultiValueMap<String, Object> pdfParam = new LinkedMultiValueMap<>();
            FileByteArrayResource byteArray = new FileByteArrayResource(fileName, fileBytes);
            pdfParam.add("file", byteArray);
            ResponseEntity<ByteArrayResource> turnPdfResult = restTemplate.postForEntity(PDF_URL, pdfParam, ByteArrayResource.class);
            if (turnPdfResult != null && turnPdfResult.getStatusCode().value() == 200) {
                byte[] pdfByte = turnPdfResult.getBody().getByteArray();
                return pdfByte;
            } else {
                throw new ValidateBusinessException("转PDF失败");
            }
        } catch (Exception e) {
            log.error("转PDF失败", e);
            throw new ValidateBusinessException("转PDF失败");
        }
    }
}
