package com.sct.tiaojie.service.video;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sct.tiaojie.page.bo.PagingParam;
import com.sct.tiaojie.page.dto.PageDTO;
import com.sct.tiaojie.repository.video.entity.MdtMeetingRecord;
import com.sct.tiaojie.service.video.bo.MdtMeetingPageBO;
import com.sct.tiaojie.service.video.bo.MeetingSaveBO;
import com.sct.tiaojie.service.video.dto.MdtMeetingDTO;
import com.sct.tiaojie.service.video.dto.MdtMeetingRecordDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface MdtMeetingRecordService extends IService<MdtMeetingRecord> {


    String saveMeeting(MeetingSaveBO param);

    /**
     * 查询案件下的视频调解记录
     * @param caseId
     * @return
     */
    List<MdtMeetingDTO> meetingList(Long caseId);

    PageDTO<MdtMeetingRecordDTO> pageList(PagingParam<MdtMeetingPageBO> param);

    /**
     * 导出会议记录
     * @param mdtMeetingPageBO 查询条件
     * @param response 返回文件流
     */
    void reportMeetingRecord(MdtMeetingPageBO mdtMeetingPageBO, HttpServletResponse response);
}
