package com.sct.tiaojie.service.common.dto;

import lombok.Data;
import lombok.ToString;

/***********************************************************************************************************************
 * @copyright 2019 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2020-02-10 17:56
 * @version V1.0
 * @description
 **********************************************************************************************************************/
@Data
@ToString
public class MultiSenderResult {
    /**
     * 错误码，0表示成功（计费依据），非0表示失败
     * 参考网址 https://cloud.tencent.com/document/product/382/3771
     */
    private String result;
    /**
     * 错误消息，result 非0时的具体错误信息
     */
    private String errmsg;
    /**
     * 用户的 session 内容
     */
    private String ext;
    /**
     * 具体每个手机的发送成功或失败信息
     */
    private MultiSmsDetail[] detail;
}


