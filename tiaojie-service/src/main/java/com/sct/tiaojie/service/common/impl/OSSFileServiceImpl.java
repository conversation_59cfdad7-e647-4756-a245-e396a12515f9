package com.sct.tiaojie.service.common.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.service.common.FileService;
import com.sct.tiaojie.util.GlusterFsStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Service(FileSysType.OSS)
@Slf4j
public class OSSFileServiceImpl implements FileService {

    @Value("${sct.oss.endpoint}")
    private String endpoint;

    @Value("${sct.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${sct.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${sct.oss.bucketName}")
    private String bucketName;

    @Resource
    private GlusterFsStore glusterFsStore;


    @Override
    public void saveFile(String filePath, InputStream inputStream) {
        filePath = getFilePath(filePath);
        try {
            String finalFilePath = filePath;
            opt(oss -> {
                oss.putObject(bucketName, finalFilePath, inputStream);
                return null;
            });
        }catch (Exception e){
            handleFileException("保存文件出错",e);
        }
    }

    @Override
    public void saveFile(String filePath, byte[] bytes) {
        saveFile(filePath, new ByteArrayInputStream(bytes));
    }

    @Override
    public void saveFile(String filePath, InputStream inputStream, String fileType) {
        saveFile(filePath, inputStream);
    }

    @Override
    public byte[] downloadFile(String filePath) {
        filePath = getFilePath(filePath);
        try {
            String finalFilePath = filePath;
            return opt(oss -> {
                OSSObject object = oss.getObject(bucketName, finalFilePath);
                byte[] bytes;
                try(InputStream inputStream = object.getObjectContent()) {
                    bytes = IOUtils.toByteArray(inputStream);
                } catch (IOException e) {
                    throw new ValidateBusinessException(e.getMessage());
                }
                return bytes;
            });
        }catch (Exception e){
            handleFileException("下载文件失败", e);
        }
        return null;
    }

    @Override
    public void downloadFile(String filePath, String absolutePath) {
        throw new ValidateBusinessException("暂不支持");
    }

    @Override
    public InputStream openInputStream(String filePath) {
        byte[] bytes = downloadFile(filePath);
        return new ByteArrayInputStream(bytes);
    }

    @Override
    public void deleteFile(String storage) {
        try {
            String finalFilePath = getFilePath(storage);
            opt(oss -> {
                oss.deleteObject(bucketName, finalFilePath);
                return null;
            });
        }catch (Exception e){
            handleFileException("删除文件失败", e);
        }
    }

    @Override
    public String getUploadCenterType() {
        return FileSysType.OSS;
    }

    @Override
    public String getRandomFolder(Long num) {
        return glusterFsStore.getRandomFolder(num);
    }

    @Override
    public String getBaseDir() {
        return null;
    }

    private void handleFileException(String msg, Exception e){
        log.error(msg, e);
        throw new ValidateBusinessException(msg, e);
    }

    private synchronized <T> T opt(Function<OSS, T> ossMethod){
        OSS oss = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            T apply = ossMethod.apply(oss);
            return apply;
        } finally {
            oss.shutdown();
        }
    }

    private String getFilePath(String filePath){
        if (filePath.startsWith("/")){
            filePath = filePath.substring(1);
        }
        if (filePath.startsWith(bucketName)){
            filePath = filePath.substring(bucketName.length());
        }
        if (filePath.startsWith("/")){
            filePath = filePath.substring(1);
        }
        return filePath;
    }
}
