package com.sct.tiaojie.service.common.impl;

import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.service.common.FileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class FileContext {

    @Resource
    private ApplicationContext applicationContext;

    @Value("${sct.fileType}")
    private String type;

    /**
     * 文件存储实现类集合
     * String 实现类的name
     */
    private Map<String, FileService> fileServiceMap = new HashMap<>();

    private String defaultType;

    @PostConstruct
    private void init(){
        fileServiceMap = applicationContext.getBeansOfType(FileService.class);
        defaultType = FileSysType.getFileType(type);
    }

    public FileService getFileService(String type){
        if (StringUtils.isBlank(type)){
            return fileServiceMap.get(defaultType);
        }
        FileService fileService = fileServiceMap.get(type);
        if (fileService == null){
            log.info("存储方式错误,使用默认存储方式[{}]", defaultType);
            return fileServiceMap.get(defaultType);
        }
        return fileService;
    }

    public FileService getFileService(){
        return getFileService(null);
    }

}
