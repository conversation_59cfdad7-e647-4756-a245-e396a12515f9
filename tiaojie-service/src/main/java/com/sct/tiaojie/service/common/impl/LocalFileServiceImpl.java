package com.sct.tiaojie.service.common.impl;

import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.service.common.FileService;
import com.sct.tiaojie.util.BaseDirUtils;
import com.sct.tiaojie.util.GlusterFsStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
@Service(FileSysType.LOCAL)
@Slf4j
public class LocalFileServiceImpl implements FileService {

    @Resource
    private GlusterFsStore glusterFsStore;

    @Override
    public void saveFile(String filePath, InputStream inputStream) {
        try {
            glusterFsStore.copyInputStreamToFile(inputStream, filePath);
        } catch (IOException e) {
            handleFileException("保存文件出错",e);
        }
    }

    @Override
    public void saveFile(String filePath, byte[] bytes) {
        try {
            glusterFsStore.writeBytesToFile(bytes, filePath);
        } catch (IOException e) {
            handleFileException("保存文件出错",e);
        }
    }

    @Override
    public void saveFile(String filePath, InputStream inputStream, String fileType) {
        saveFile(filePath,inputStream);
    }

    @Override
    public byte[] downloadFile(String filePath) {
        try {
            return glusterFsStore.getFileBytes(filePath);
        } catch (IOException e) {
            handleFileException("下载文件失败", e);
        }
        return null;
    }

    @Override
    public void downloadFile(String filePath, String absolutePath) {
        try {
            glusterFsStore.copyFile(filePath, absolutePath);
        } catch (IOException e) {
            handleFileException("下载文件到本地失败", e);
        }
    }

    @Override
    public InputStream openInputStream(String filePath) {
        try {
            return  glusterFsStore.getFileInputStream(filePath);
        } catch (IOException e) {
            handleFileException("下载文件失败", e);
        }
        return null;
    }

    @Override
    public void deleteFile(String storage) {
        //是否删除成功不理，数据库已经标识为删除，前端无法获取
        glusterFsStore.delete(storage);
    }

    @Override
    public String getUploadCenterType() {
        return FileSysType.LOCAL;
    }

    @Override
    public String getRandomFolder(Long num) {
        return glusterFsStore.getRandomFolder(num);
    }

    @Override
    public String getBaseDir() {
        String baseDir = BaseDirUtils.getBaseDir();
        if (!baseDir.endsWith("/")) {
            baseDir += "/";
        }
        return baseDir + "/";
    }

    private void handleFileException(String msg, Exception e){
        log.error(msg, e);
        throw new ValidateBusinessException(msg, e);
    }
}
