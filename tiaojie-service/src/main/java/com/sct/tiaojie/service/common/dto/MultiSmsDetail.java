package com.sct.tiaojie.service.common.dto;

import lombok.Data;
import lombok.ToString;

/***********************************************************************************************************************
 * @copyright 2019 www.otsdata.com Inc. All rights reserved. 
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2020-02-10 18:03
 * @version V1.0
 * @description 群发结果详情
 **********************************************************************************************************************/
@Data
@ToString
public class MultiSmsDetail {
    /**
     * 错误码，0表示成功（计费依据），非0表示失败
     * 参考网址 https://cloud.tencent.com/document/product/382/3771
     */
    private String result;
    /**
     * 错误消息，result 非0时的具体错误信息
     */
    private String errmsg;
    /**
     * 短信计费的条数
     */
    private String fee;
    /**
     * 国家（或地区）码
     */
    private String nationcode;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 唯一标识
     */
    private String sid;
}
