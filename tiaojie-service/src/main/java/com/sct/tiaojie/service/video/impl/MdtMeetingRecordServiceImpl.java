package com.sct.tiaojie.service.video.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sct.tiaojie.common.contants.AttachmentEnum;
import com.sct.tiaojie.common.contants.FileSysType;
import com.sct.tiaojie.common.copy.CglibMapper;
import com.sct.tiaojie.common.enums.LitigantIdentityType;
import com.sct.tiaojie.common.enums.MeetingStatusEnum;
import com.sct.tiaojie.common.enums.Whether;
import com.sct.tiaojie.common.util.ExcelExportUtil;
import com.sct.tiaojie.page.PageUtil;
import com.sct.tiaojie.page.bo.PagingParam;
import com.sct.tiaojie.page.dto.PageDTO;
import com.sct.tiaojie.repository.video.entity.MdtMeetingRecord;
import com.sct.tiaojie.repository.video.entity.MdtMeetingRecordLitigant;
import com.sct.tiaojie.repository.video.entity.MdtMeetingVideo;
import com.sct.tiaojie.repository.video.mapper.MdtMeetingRecordLitigantMapper;
import com.sct.tiaojie.repository.video.mapper.MdtMeetingRecordMapper;
import com.sct.tiaojie.service.common.CommonFileService;
import com.sct.tiaojie.service.common.dto.FilesDTO;
import com.sct.tiaojie.service.oss.ObjectFileContextService;
import com.sct.tiaojie.service.oss.OssFileService;
import com.sct.tiaojie.service.video.MdtMeetingRecordService;
import com.sct.tiaojie.service.video.MdtMeetingVideoService;
import com.sct.tiaojie.service.video.bo.MdtMeetingPageBO;
import com.sct.tiaojie.service.video.bo.MeetingSaveBO;
import com.sct.tiaojie.service.video.bo.Participant;
import com.sct.tiaojie.service.video.dto.MdtMeetingDTO;
import com.sct.tiaojie.service.video.dto.MdtMeetingRecordDTO;
import com.sct.tiaojie.util.BaseDirUtils;
import com.sct.tiaojie.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MdtMeetingRecordServiceImpl extends ServiceImpl<MdtMeetingRecordMapper, MdtMeetingRecord> implements MdtMeetingRecordService {

    private final String create = "create";
    private final String modify = "modify";
    private final String remove = "remove";
    private final String complete = "complete";

    @Resource
    private MdtMeetingRecordLitigantMapper mdtMeetingRecordLitigantMapper;
    @Resource
    private ObjectFileContextService objectFileContextService;
    private OssFileService ossFileService;
    @Resource
    private CommonFileService fileClientService;
    @Resource
    private MdtMeetingVideoService mdtMeetingVideoService;
    @Value("${log.max-rows}")
    public Integer MAX_ROWS;

    @PostConstruct
    private void init() {
        ossFileService = objectFileContextService.getFileService();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveMeeting(MeetingSaveBO param) {
        String msg = checkParam(param);
        if (msg != null){
            return msg;
        }
        String changeType = param.getChangeType();
        MdtMeetingRecord meeting = CglibMapper.copy(param, MdtMeetingRecord.class);
        meeting.setCaseId(meeting.getMediationId());
        meeting.setDeleteFlag(Whether.NO.getCode());
        meeting.setMediatorId(param.getUserId());
        meeting.setMediatorName(param.getUserName());
        if (!CollectionUtils.isEmpty(param.getTextContent())) {
            meeting.setTextContent(JacksonUtils.writeValueAsString(param.getTextContent()));
        }

        MdtMeetingRecord record = baseMapper.getMeeting(param.getMediationId(), param.getMeetingId());
        switch (changeType) {
            case create: {
                String s = checkSave(param);
                if (s != null) {
                    return s;
                }
                if (record != null) {
                    return "会议室已存在";
                }
                baseMapper.insert(meeting);
                break;
            }
            case modify: {
                String s = checkSave(param);
                if (s != null) {
                    return s;
                }
                if (record == null) {
                    return "会议室不存在";
                }
                meeting.setRecordId(record.getRecordId());
                baseMapper.updateById(meeting);
                break;
            }
            case remove:
                if (record == null) {
                    return "会议室不存在";
                }
                record.setDeleteFlag(Whether.YES.getCode());
                record.setUpdateTime(null);
                baseMapper.updateById(record);
                break;
            case complete:
//            String s = checkComplete(param);
//            if (s != null){
//                return s;
//            }
                if (record == null) {
                    return "会议室不存在";
                }
                meeting.setRecordId(record.getRecordId());
                meeting.setMeetingStatus(MeetingStatusEnum.ENDED.getCode());
                baseMapper.updateById(meeting);
                mdtMeetingRecordLitigantMapper.delete(new QueryWrapper<MdtMeetingRecordLitigant>().lambda()
                        .eq(MdtMeetingRecordLitigant::getRecordId, meeting.getRecordId()));
                saveParticipants(param.getParticipant(), meeting);
                String videoUrl = param.getVideoUrl();
                List<MdtMeetingVideo> list = mdtMeetingVideoService.list(new QueryWrapper<MdtMeetingVideo>().lambda()
                        .eq(MdtMeetingVideo::getMediationId, param.getMediationId())
                        .eq(MdtMeetingVideo::getMeetingId, param.getMeetingId()));
                if (CollectionUtils.isEmpty(list)){
                    String path;
                    try {
                        path = downLoadVideo(videoUrl, meeting.getMediationId(), meeting.getMeetingId());
                    } catch (Exception e) {
                        log.error("下载视频失败", e);
                        return null;
                    }
                    saveVideoRecord(path, meeting);
                } else {
                    if (StringUtils.isNotBlank(videoUrl) && !videoUrl.equals(record.getVideoUrl())) {
                        for (MdtMeetingVideo video : list) {
                            fileClientService.deleteFile(video.getFilePath(), 0L);
                            mdtMeetingVideoService.removeById(video.getVideoId());
                        }
                        String path;
                        try {
                            path = downLoadVideo(videoUrl, meeting.getMediationId(), meeting.getMeetingId());
                        } catch (Exception e) {
                            log.error("下载视频失败", e);
                            return null;
                        }
                        saveVideoRecord(path, meeting);
                    }
                }
                break;
            default:
                return String.format("未知的变更类型[%s]", changeType);
        }

        return null;
    }

    private void saveParticipants(List<Participant> participants, MdtMeetingRecord meeting){
        if (!CollectionUtils.isEmpty(participants)) {
            // 去重
            Map<String, List<Participant>> collect = participants.stream().collect(Collectors.groupingBy(Participant::getLitigantId));
            for (String key : collect.keySet()) {
                Participant participant = collect.get(key).get(0);
                MdtMeetingRecordLitigant litigant = new MdtMeetingRecordLitigant();
                litigant.setCaseId(meeting.getMediationId());
                litigant.setLitigantId(participant.getLitigantId());
                litigant.setLitigantName(participant.getLitigantName());
                litigant.setRecordId(meeting.getRecordId());
                mdtMeetingRecordLitigantMapper.insert(litigant);
            }
        }
    }

    private void saveVideoRecord(String path, MdtMeetingRecord meeting) {
        MdtMeetingVideo video = new MdtMeetingVideo();
        video.setMediationId(meeting.getMediationId());
        video.setFilePath(path);
        video.setMeetingId(meeting.getMeetingId());
        video.setRecordId(meeting.getRecordId());
        mdtMeetingVideoService.save(video);
    }

    private String downLoadVideo(String videoUrl, String mediationId, String meetingId) {
        String path = AttachmentEnum.MEETING_VIDEO_FILE.getAgreePath()
                .replace("{date}", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .replace("{mediationId}", mediationId)
                .replace("{meetingId}", meetingId);
        path += AttachmentEnum.getFileKey("video.mp4");
        String baseDir = BaseDirUtils.getBaseDir();
        if (!baseDir.endsWith("/")){
            baseDir += "/";
        }
        path = baseDir + "0" + path;
        if (path.startsWith("/")){
            path = path.substring(1);
        }
        ossFileService.multipartUpload(videoUrl, path);
        FilesDTO filesDTO = fileClientService.saveFileInfo(path, 0L, FileSysType.OSS);
        return filesDTO.getFilePath();
    }

    @Override
    public List<MdtMeetingDTO> meetingList(Long caseId) {
        return baseMapper.meetingList(caseId);
    }

    @Override
    public PageDTO<MdtMeetingRecordDTO> pageList(PagingParam<MdtMeetingPageBO> param) {
        Page<MdtMeetingRecordDTO> page = PageUtil.getPage(param, MdtMeetingRecordDTO.class);
        baseMapper.pageList(page, param.getParam());
        for (MdtMeetingRecordDTO record : page.getRecords()) {
            if (StringUtils.isNotBlank(record.getParticipantType())) {
                String[] split = record.getParticipantType().split("/");
                List<String> list = Arrays.asList(split);
                String collect = list.stream().map(LitigantIdentityType.DEFENDANT::getTitleByCode).collect(Collectors.joining("/"));
                record.setParticipantType(collect);
            }
        }
        return PageDTO.getInstance(page);
    }

    @Override
    public void reportMeetingRecord(MdtMeetingPageBO mdtMeetingPageBO, HttpServletResponse response) {
        //1.查询需要导出的数据，最大50w条
        List<MdtMeetingRecordDTO> mdtMeetingRecordDTOList = baseMapper.selectMeetingRecord(mdtMeetingPageBO);
        for (MdtMeetingRecordDTO mdtMeetingRecordDTO : mdtMeetingRecordDTOList) {
            mdtMeetingRecordDTO.setMeetingStatusReplace(MeetingStatusEnum.getTitleByCode(mdtMeetingRecordDTO.getMeetingStatus()));
        }
        ExcelExportUtil<MdtMeetingRecordDTO> excelExportUtil = new ExcelExportUtil<>(MdtMeetingRecordDTO.class);
        excelExportUtil.exportRecordToExcel(response, mdtMeetingRecordDTOList, "导出会议记录.xlsx", "会议记录", MAX_ROWS);
    }

    private String checkComplete(MeetingSaveBO param) {
        if (CollectionUtils.isEmpty(param.getParticipant())){
            return "参会人不能为空";
        }
        if (StringUtils.isBlank(param.getVideoUrl())){
            return "视频文件下载地址不能为空";
        }
        return null;
    }

    private String checkSave(MeetingSaveBO param) {
        if (StringUtils.isBlank(param.getMeetingName())){
            return "会议名不能为空";
        }
        if (param.getMediationTime() == null){
            return "会议时间不能为空";
        }
        if (StringUtils.isBlank(param.getMeetingStatus())){
            return "会议状态不能为空";
        }
        if (StringUtils.isBlank(param.getPassword())){
            return "会议密码不能为空";
        }
        if (StringUtils.isBlank(param.getUserId())){
            return "调解员ID不能为空";
        }
        if (StringUtils.isBlank(param.getUserName())){
            return "调解员姓名不能为空";
        }
        if (StringUtils.isBlank(param.getMeetingUrl())){
            return "会议链接不能为空";
        }
        return null;
    }

    private String checkParam(MeetingSaveBO param) {
        if (param == null){
            return "参数不能为空";
        }
        if (StringUtils.isBlank(param.getMediationId())){
            return "调解ID不能为空";
        }
        if (StringUtils.isBlank(param.getMeetingId())){
            return "调解室不能为空";
        }
        if (StringUtils.isBlank(param.getChangeType())){
            return "变更类型不能为空";
        }
        return null;
    }
}
