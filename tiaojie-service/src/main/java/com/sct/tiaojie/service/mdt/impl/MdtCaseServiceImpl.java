package com.sct.tiaojie.service.mdt.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.aliyun.dysmsapi20170525.models.SendBatchSmsResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sct.tiaojie.common.contants.*;
import com.sct.tiaojie.common.copy.CglibMapper;
import com.sct.tiaojie.common.enums.*;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.common.util.*;
import com.sct.tiaojie.manage.dto.token.TokenUser;
import com.sct.tiaojie.manage.threadlocal.ServiceThreadLocal;
import com.sct.tiaojie.page.PageUtil;
import com.sct.tiaojie.page.bo.PagingParam;
import com.sct.tiaojie.page.dto.PageDTO;
import com.sct.tiaojie.repository.auth.entity.AuthDept;
import com.sct.tiaojie.repository.auth.entity.AuthEmployee;
import com.sct.tiaojie.repository.auth.mapper.AuthRealNameOrderMapper;
import com.sct.tiaojie.repository.bo.TaskInstancePageBO;
import com.sct.tiaojie.repository.dto.TaskInstanceDTO;
import com.sct.tiaojie.repository.mdt.entity.*;
import com.sct.tiaojie.repository.mdt.mapper.MdtCaseMapper;
import com.sct.tiaojie.repository.mdt.mapper.MdtRepairPhoneMapper;
import com.sct.tiaojie.repository.sys.entity.SysDictData;
import com.sct.tiaojie.repository.sys.entity.SysEntrusts;
import com.sct.tiaojie.repository.tmpl.entity.TmplCase;
import com.sct.tiaojie.repository.tmpl.entity.TmplCaseModuleField;
import com.sct.tiaojie.repository.tmpl.mapper.TmplCaseModuleFieldMapper;
import com.sct.tiaojie.service.sys.dto.SysBusinessTypeDTO;
import com.sct.tiaojie.service.task.TaskInstanceService;
import com.sct.tiaojie.service.ai.AIContentService;
import com.sct.tiaojie.service.auth.AuthDeptService;
import com.sct.tiaojie.service.auth.AuthEmployeeService;
import com.sct.tiaojie.service.auth.bo.RealNamePageBO;
import com.sct.tiaojie.service.auth.dto.RealNamePageDTO;
import com.sct.tiaojie.service.auth.dto.UserInfoDTO;
import com.sct.tiaojie.service.common.CommonFileService;
import com.sct.tiaojie.service.mdt.*;
import com.sct.tiaojie.service.mdt.bean.FieldEnumeration;
import com.sct.tiaojie.service.mdt.bean.FieldValues;
import com.sct.tiaojie.service.mdt.bo.*;
import com.sct.tiaojie.service.mdt.dto.*;
import com.sct.tiaojie.service.sys.*;
import com.sct.tiaojie.service.sys.bo.EntrustsOrgBO;
import com.sct.tiaojie.service.sys.dto.SysParameterDTO;
import com.sct.tiaojie.service.tmpl.*;
import com.sct.tiaojie.service.tmpl.bo.DeputyTmplParam;
import com.sct.tiaojie.service.tmpl.dto.*;
import com.sct.tiaojie.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * <p>
 *   调节案件信息 服务实现类
 * </p>
 * @copyright 2019 www.otsdata.com Inc. All rights reserved.
 *                注意：本内容仅限于公司内部传阅，禁止外泄以及用于其他的商业目
 * <AUTHOR>
 * @date 2022-01-17
 * @version V1.0
 **********************************************************************************************************************/
@Service
@Slf4j
public class MdtCaseServiceImpl extends ServiceImpl<MdtCaseMapper, MdtCase> implements MdtCaseService {

    @Resource
    private AuthEmployeeService authEmployeeService;
    @Resource
    private MdtCaseCollectRecordService mdtCaseCollectRecordService;
    @Resource
    private SysParameterService sysParameterService;
    @Resource
    private CommonFileService fileService;
    @Resource
    private MdtCaseEventService mdtCaseEventService;
    @Resource
    private AuthRealNameOrderMapper authRealNameOrderMapper;
    @Resource
    private DeputyTmplService deputyTmplService;
    @Resource
    private DeputyTmplMappingService deputyTmplMappingService;
    @Resource
    private MdtCaseCustomDataService mdtCaseCustomDataService;
    @Resource
    private MdtCaseLitigantService mdtCaseLitigantService;
    @Resource
    private MdtSmsTemplateService mdtSmsTemplateService;
    @Resource
    private MdtCaseDistributeRecordService mdtCaseDistributeRecordService;
    @Resource
    private SmsCloudTencentUtil smsCloudTencentUtil;
    @Resource
    private MdtSmsSendServiceImpl mdtSmsSendService;
    @Resource
    private MdtContactRepairRecordService mdtContactRepairRecordService;
    @Resource
    private SysEntrustsService sysEntrustsService;
    @Resource
    private MdtCaseCollectionRecordService mdtCaseCollectionRecordService;
    @Resource
    private AuthDeptService authDeptService;
    @Resource
    private SysDictDataService sysDictDataService;
    @Resource
    private SysDictService sysDictService;
    @Resource
    private TmplCaseModuleFieldMapper tmplCaseModuleFieldMapper;
    @Resource
    private MdtRepairPhoneMapper mdtRepairPhoneMapper;
    private String[] chineseNum = new String[]{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"};
    @Value("${log.max-rows}")
    public Integer MAX_ROWS;
    @Resource
    private SysMdtOrgService sysMdtOrgService;
    @Resource
    private TmplCaseService tmplCaseService;
    @Resource
    private MdtCaseApprovalService mdtCaseApprovalService;

    @Resource
    private AIContentService aiContentService;

    @Resource
    private TaskInstanceService taskInstanceService;

    @Resource
    private SysBusinessTypeService sysBusinessTypeService;

    private final int totalPages = 2; //页数（导出最大循环数）

    private ThreadLocal<Map<String, Map<String, String>>> dictDataMapForDataKey = ThreadLocal.withInitial(ConcurrentHashMap::new);

    @Override
    public String applyCaseNumberPrivacy(String caseNo) {
        if (StrUtil.isBlank(caseNo)) {
            return caseNo;
        }
        String result = caseNo;
        // 应用所有脱敏规则
        for (PrivacyRuleDTO rule : this.listPrivacyNoRuleMap()) {
            String key = rule.getKey();
            String value = rule.getValue();
            //这里用非null，因为允许为空字符串
            if (key!=null && value!=null ) {
                // 如果案件编号中包含该key，则替换为对应的value
                if (result.contains(key)) {
                    result = result.replace(key, value);
                }
            }
        }
        return result;
    }

    @Cacheable(value = RedisKeyConstant.SYSTEM_PARAMETER,key="#root.method")
    public List<PrivacyRuleDTO> listPrivacyNoRuleMap() {
        List list = null;
        try {
            String privacyRule = sysParameterService.getValueSysParameterByModuleAndParam(
                    ParameterModuleCode.CASE,
                    ParameterParamCode.CASE_NUMBER_PRIVACY_RULE
            );
            // 解析隐私规则JSON
            list = JSONUtil.toList(privacyRule,PrivacyRuleDTO.class);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public MyCaseDetailDTO getCaseDetail() {
        Long accountId = ServiceThreadLocal.getCurrentAccountId();
        AuthEmployee employee = authEmployeeService.getById(ServiceThreadLocal.getCurrentUser().getEmployeeId());
        Boolean mediatorFlag = employee.getMediatorFlag();
        BigDecimal decimal = new BigDecimal("10000");
        List<MdtCase> mdtCases;
        Long companyId = ServiceThreadLocal.getCompanyId();
        LambdaQueryWrapper<MdtCase> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MdtCase::getCompanyId, companyId);
        if (mediatorFlag) {
            wrapper.eq(MdtCase::getCurrentMediatorId, accountId);
        }
        mdtCases = baseMapper.selectList(wrapper);
        MyCaseDetailDTO myCaseDetailDTO = new MyCaseDetailDTO();
        BigDecimal repaidAmount = new BigDecimal("0");
        myCaseDetailDTO.setRepaidAmount(repaidAmount.divide(decimal));
        long endCase = mdtCases.stream().filter(mdtCase -> mdtCase.getMdtCaseStatus() != null && mdtCase.getMdtCaseStatus().equals(CaseStatus.CLOSE.getCode())).count();
        myCaseDetailDTO.setRepaidNumber((int) endCase);
        List<MdtCase> ongoingCase = mdtCases.stream().filter(mdtCase -> mdtCase.getMdtCaseStatus() != null && mdtCase.getMdtCaseStatus().equals(CaseStatus.PROCESS_ING.getCode())).collect(Collectors.toList());
        List<Long> caseIds = ongoingCase.stream().map(MdtCase::getCaseId).collect(Collectors.toList());

        myCaseDetailDTO.setOngoingNumber(ongoingCase.size());
        myCaseDetailDTO.setOngoingAmount(new BigDecimal("0"));
        myCaseDetailDTO.setYesterdayActive(0);
        myCaseDetailDTO.setThreeDayNotActive(0);
        if (!CollectionUtils.isEmpty(caseIds)) {
            setActive(caseIds, mediatorFlag, accountId, myCaseDetailDTO);
        }

        List<PropMap<String, BigDecimal>> list = new ArrayList<>();
        LocalDate now = LocalDate.now();
        LocalDate first = LocalDate.of(now.getYear(), now.getMonth(), 1);
        for (int i = 5; i >= 0; i--) {
            BigDecimal reduce = new BigDecimal("0");
            list.add(new PropMap<>(getChineseNumber(first.minusMonths(i).getMonthValue()) + "月", reduce.divide(decimal)));
        }
        myCaseDetailDTO.setLatelyStatistics(list);

        BigDecimal repaidAmountMonth = new BigDecimal("0");
        BigDecimal ongoingAmountMonth = new BigDecimal("0");
        myCaseDetailDTO.setRepaidAmountMonth(repaidAmountMonth.divide(decimal));
        myCaseDetailDTO.setOngoingAmountMonth(ongoingAmountMonth.divide(decimal));
        return myCaseDetailDTO;
    }

    @Override
    public MdtCaseFontAllDTO frontList() {
        MdtCaseFontAllDTO result = new MdtCaseFontAllDTO();
        TokenUser user = ServiceThreadLocal.getCurrentUser();
        String mobile = user.getMobile();
        String idCardNo = user.getIdCardNo();
        if (mobile != null && idCardNo != null) {
            mobile = null;
        }
        //当事人姓名+当事人电话  的登陆方式
        List<MdtCaseFrontDTO> mdtCaseFrontDTOS = baseMapper.frontList(user.getLoginName(), mobile, idCardNo);
        MdtCommissionDTO commission = getCommissionMsg();
        for (MdtCaseFrontDTO mdtCaseFrontDTO : mdtCaseFrontDTOS) {
            mdtCaseFrontDTO.setAgreementFlag(checkFileSign(mdtCaseFrontDTO.getMediationAgreement()));
            mdtCaseFrontDTO.setNoticeFlag(checkFileSign(mdtCaseFrontDTO.getMediationNotice()));
        }
        if (mdtCaseFrontDTOS.size() > 0) {
            result.setMyCase(mdtCaseFrontDTOS);
            result.setRespondFlag(true);
            result.setMsg("与" + user.getLoginName() + "的相关案件有：");
            return handleResult(result, commission);
        }
        if (idCardNo != null) {
            throw new ValidateBusinessException("找不到案件");
        }


        throw new ValidateBusinessException("找不到案件");
    }

    private MdtCaseFontAllDTO handleResult(MdtCaseFontAllDTO result, MdtCommissionDTO commissionDTO) {
        TokenUser user = ServiceThreadLocal.getCurrentUser();
        Boolean respondFlag = user.getRespondFlag();
        if (respondFlag != null && !respondFlag) {
            result.setRespondFlag(false);
            for (MdtCaseFrontDTO mdtCaseFrontDTO : result.getMyCase()) {
                mdtCaseFrontDTO.setNoticeNeedSignFlag(false);
            }
        }
        result.getMyCase().forEach(a -> a.setCommission(commissionDTO));
        return result;
    }

    @Override
    public List<Long> getCaseIds(MdtCasePageBO param) {
        return baseMapper.getCaseIds(param, ServiceThreadLocal.getCompanyId());
    }

    @Override
    public void exportFileAsZip(MdtCaseExportBO param, HttpServletResponse response) {
        if (!CollectionUtils.isEmpty(param.getCaseIds())) {
            param.setParam(null);
        } else {
            MdtCasePageBO bo = param.getParam();
            if (bo == null) {
                bo = new MdtCasePageBO();
            }
            bo.setSignFlag(true);
            param.setParam(bo);
        }
        Long companyId = ServiceThreadLocal.getCompanyId();
        List<Map<String, Object>> list = baseMapper.selectFilePath(param.getCaseIds(), param.getParam(), companyId);
        if (CollectionUtils.isEmpty(list)) {
            throw new ValidateBusinessException("未选择有效案件");
        }
        Map<String, InputStream> inputMap = new HashMap<>();
        for (Map<String, Object> map : list) {
            String filePath = map.get("filePath").toString();
            String respondName = map.get("respondName").toString();
            InputStream inputStream = fileService.openInputStream(filePath);
            inputMap.put(respondName + "-权利义务告知书.pdf", inputStream);
        }
        try {
            byte[] bytes = FileIOUtils.toZipBytes(inputMap);
            String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
            String downloadFilename = String.format("%s%s.zip", "已签署权利义务告知书", format);
            HttpResponseUtils.setResponseForDownloadFile(response, downloadFilename);
            IOUtils.copyLarge(new ByteArrayInputStream(bytes), response.getOutputStream());
        } catch (IOException e) {
            log.error("下载压缩包失败", e);
            throw new ValidateBusinessException("下载失败");
        }
    }

    @Override
    public PageDTO<RealNamePageDTO> realNamePageList(PagingParam<RealNamePageBO> param) {
        Page<RealNamePageDTO> page = PageUtil.getPage(param, RealNamePageDTO.class);
        Long companyId = ServiceThreadLocal.getCompanyId();
        authRealNameOrderMapper.pageList(page, param.getParam(), companyId);
        return PageDTO.getInstance(page);
    }

    @Override
    public void exportFaceImg(MdtBatchFaceImgBO param, HttpServletResponse response) {
        if (!CollectionUtils.isEmpty(param.getOrderIds())) {
            param.setParams(null);
        }
        Long companyId = ServiceThreadLocal.getCompanyId();
        List<FaceImgDTO> faceImg = authRealNameOrderMapper.getFaceImg(param.getOrderIds(), param.getParams(), companyId);
        Map<String, InputStream> inputMap = new HashMap<>();
        for (FaceImgDTO img : faceImg) {
            String photo = img.getPhoto();
            if (StringUtils.isBlank(photo)) {
                continue;
            }
            byte[] file = Base64.getDecoder().decode(photo);
            if (file.length < 1) {
                continue;
            }
            String name = img.getName();
            String time = img.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            inputMap.put(time + "-" + name + ".jpg", new ByteArrayInputStream(file));
        }
        if (CollectionUtils.isEmpty(inputMap)) {
            throw new ValidateBusinessException("未选择有效记录");
        }
        try {
            byte[] bytes = FileIOUtils.toZipBytes(inputMap);
            String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
            String downloadFilename = String.format("%s%s.zip", "刷脸记录图片", format);
            HttpResponseUtils.setResponseForDownloadFile(response, downloadFilename);
            IOUtils.copyLarge(new ByteArrayInputStream(bytes), response.getOutputStream());
        } catch (IOException e) {
            log.error("下载压缩包失败", e);
            throw new ValidateBusinessException("下载失败");
        }
    }

    @Override
    public Map<String, Object> getSysTempCase(Long caseId, Long meetingRecordId) {
        return baseMapper.getSysTempCase(caseId, meetingRecordId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCase(MdtDeleteBO param) {
        List<Long> caseIds = param.getCaseIds();
        if (CollectionUtils.isEmpty(caseIds)) {
            throw new ValidateBusinessException("找不到案件");
        }
        Long accountId = ServiceThreadLocal.getCurrentAccountId();
        Long companyId = ServiceThreadLocal.getCompanyId();
        for (Long caseId : caseIds) {
            QueryWrapper wrapper = new QueryWrapper<>().eq("case_id", caseId);
            MdtCase mdtCase = baseMapper.selectById(caseId);
            ValidateUtils.validate(mdtCase == null || !mdtCase.getCompanyId().equals(companyId), "案件不存在");


            mdtCaseCollectRecordService.remove(wrapper);
            mdtCaseEventService.remove(wrapper);
            baseMapper.deleteById(caseId);
        }
    }

    @Override
    public PageDTO<MediateCasePageDTO> newPageList(PagingParam<MediateCasePageBO> param) {
        MediateCasePageBO bo = param.getParam();
        List<EntrustsOrgBO> entrustsOrgBOList = bo.getEntrustsOrgBOList();
        if (entrustsOrgBOList != null && !entrustsOrgBOList.isEmpty()){

            List<Long> entrustsIds = new ArrayList<>();
            List<Long> entrustsDeptIds = new ArrayList<>();
            List<Long> entrustsJudgeIds = new ArrayList<>();

            for (EntrustsOrgBO entrustsOrgBO : entrustsOrgBOList){
                if ("1".equals(entrustsOrgBO.getType())){
                    entrustsIds.add(entrustsOrgBO.getOrgId());
                } else if ("2".equals(entrustsOrgBO.getType())){
                    entrustsDeptIds.add(entrustsOrgBO.getOrgId());
                } else if ("3".equals(entrustsOrgBO.getType())){
                    entrustsJudgeIds.add(entrustsOrgBO.getOrgId());
                }
            }

            bo.setEntrustsIds(entrustsIds);
            bo.setEntrustsDeptIds(entrustsDeptIds);
            bo.setEntrustsJudgeIds(entrustsJudgeIds);
        }
        Long expirationTimeStart = bo.getExpirationTimeStart();
        Long expirationTimeEnd = bo.getExpirationTimeEnd();
        if (expirationTimeStart != null && expirationTimeEnd == null) {
            bo.setExpirationTimeEnd(expirationTimeStart);
        } else if (expirationTimeStart == null && expirationTimeEnd != null) {
            bo.setExpirationTimeStart(expirationTimeEnd);
        } else if (expirationTimeStart != null && expirationTimeEnd != null) {
            if (expirationTimeStart > expirationTimeEnd) {
                bo.setExpirationTimeStart(expirationTimeEnd);
                bo.setExpirationTimeEnd(expirationTimeStart);
                bo.setOrderType("desc");
            } else if (expirationTimeEnd > expirationTimeStart) {
                bo.setOrderType("asc");
            }
        }
        bo.setCloseReasons(getTitle(bo.getCloseReasons(), CloseReason.ADMINISTRATION_HANDLE));
        bo.setLitigantTypes(getTitle(bo.getLitigantTypes(), LitigantType.NATURAL));
        bo.setMediateStatuses(getTitle(bo.getMediateStatuses(), MediateStatus.CASE_CLOSE));
        bo.setLitigantIdentityTypes(getTitle(bo.getLitigantIdentityTypes(), LitigantIdentityType.PLAINTIFF));
        bo.setLitigantPhoneStatuses(getTitle(bo.getLitigantPhoneStatuses(), PhoneType.ABSENT));
        bo.setMdtCaseStatuses(getTitle(bo.getMdtCaseStatuses(), CaseStatus.CLOSE));
        Page<MediateCasePageDTO> page = PageUtil.getPage(param, MediateCasePageDTO.class);
        // 禁用自动count优化，避免JSQLParser解析复杂SQL时出现异常
        page.setOptimizeCountSql(false);

        Page<MediateCasePageDTO> mediateCasePageDTOPage = baseMapper.newPageList(page, param.getParam(),param.getPageInfo().getOrderParams());
        List<MediateCasePageDTO> records = mediateCasePageDTOPage.getRecords();
        Set<Long> caseIdList = records.stream().map(MediateCasePageDTO::getCaseId).collect(Collectors.toSet());
        List<Long> entrustsDeptIds = records.stream().map(MediateCasePageDTO::getEntrustsDeptId).collect(Collectors.toList());
        if (entrustsDeptIds.size() < 1) {
            return PageDTO.getInstance(page);
        }
        Map<Long,MediateCasePageDTO> map = new HashMap<>();
        if(caseIdList != null && caseIdList.size()>0){
            List<MediateCasePageDTO> list = baseMapper.selectCaseLitigantInfo(caseIdList);
            map = list.stream()
                .collect(Collectors.toMap(
                        MediateCasePageDTO::getCaseId,
                        dto -> dto
                ));
        }
        List<AuthDept> authDepts = authDeptService.listByIds(entrustsDeptIds);
        Map<Long, AuthDept> authDeptMap = authDepts.stream().collect(Collectors.toMap(AuthDept::getDeptId, item -> item));
        List<MdtCaseCollectRecord> mdtCaseCollectRecordList = mdtCaseCollectRecordService.selectMaxTimeRecords(caseIdList);
        Map<Long, MdtCaseCollectRecord> mdtCaseCollectRecordMap = mdtCaseCollectRecordList.stream().collect(Collectors.toMap(MdtCaseCollectRecord::getCaseId, item -> item));
        for (MediateCasePageDTO record : records) {
            AuthDept authDept = authDeptMap.get(record.getEntrustsDeptId());
            MdtCaseCollectRecord mdtCaseCollectRecord = mdtCaseCollectRecordMap.get(record.getCaseId());
            if (authDept != null) {
                record.setEntrustsDeptName(authDept.getDeptName());
            }
            if (mdtCaseCollectRecord != null) {
                record.setOtherDemand(mdtCaseCollectRecord.getOtherDemand());
                record.setNextDoTime(mdtCaseCollectRecord.getNextDoTime());
            }
            MediateCasePageDTO caseLitigant = map.get(record.getCaseId());
            if(caseLitigant != null){
                // 设置申请人和被申请人
                record.setDefendAntLitigantNames(caseLitigant.getDefendAntLitigantNames());
                record.setPlainTiffLitigantNames(caseLitigant.getPlainTiffLitigantNames());
            }
        }
        return PageDTO.getInstance(page);
    }

    private List<String> getTitle(List<String> code, BaseEnum baseEnum) {
        if (CollectionUtils.isEmpty(code)) {
            return null;
        }
        code.addAll(code.stream().map(baseEnum::getTitleByCode).collect(Collectors.toList()));
        // 去重
        Set<String> set = new HashSet<>(code);
        return new ArrayList<>(set);
    }

    /**
     * 获取案件最新的申请办理数据map
     *
     * @param mdtCaseApprovalList 案件申请办理数据
     * @return 返回一个按照案件id分组后的最新办理数据
     */
    private Map<Long, MdtCaseApproval> getMdtCaseApprovalMap(List<MdtCaseApproval> mdtCaseApprovalList) {
        return mdtCaseApprovalList.stream().collect(Collectors.groupingBy(
                MdtCaseApproval::getCaseId,
                Collectors.collectingAndThen(
                        Collectors.maxBy(Comparator.comparing(MdtCaseApproval::getStartTime)),
                        optionalEvent -> optionalEvent.map(Collections::singletonList).orElse(Collections.emptyList())
                )
        )).values().stream().flatMap(List::stream).collect(Collectors.toList()).stream().collect(Collectors.toMap(MdtCaseApproval::getCaseId, item -> item));

    }

    @Override
    public List<DeputyTmplDTO> getExportTmpl(List<Long> caseIds) {
        List<Integer> tmplIds = baseMapper.selectTmplCaseIds(caseIds);
        DeputyTmplParam param = new DeputyTmplParam();
        param.setTmplStatus(StatusConstants.ENABLE);
        param.setTmplType(TmplType.EXPORT_TMPL.getCode());
        param.setTmplIds(tmplIds);
        return deputyTmplService.deputyTmplList(param);
    }

    @Override
    public void exportCase(ExportCaseBO param, HttpServletResponse response) {
        log.info("导出 开始:" + LocalDateTime.now());
        long currentTimeMillis = System.currentTimeMillis();
        log.info("开始导出 开始查询模板" + LocalDateTime.now());
        List<DeputyTmplDetail> tmplList = new ArrayList<>();
        for (Integer deputyTmplId : param.getDeputyTmplIds()) {
            DeputyTmplDetail deputyTmplDetail = deputyTmplMappingService.mappingList(deputyTmplId);
            tmplList.add(deputyTmplDetail);
        }
        long currentTimeMillis1 = System.currentTimeMillis();
        log.info("开始导出 查询模板结束");
        log.info("时间差" + (currentTimeMillis1 - currentTimeMillis));
        Set<Integer> tmplIdSet = tmplList.stream().map(DeputyTmplDetail::getTmplId).collect(Collectors.toSet());
        Map<Integer, List<DeputyTmplDetail>> deputyTmplDetailMap = tmplList.stream().collect(Collectors.groupingBy(DeputyTmplDetail::getTmplId));
        Map<String, InputStream> streamMap = new HashMap<>();
        //构建缓存
        Map<String,List<String>> fieldValueMap = new ConcurrentHashMap<>();

        //获取系统字典
        this.dictDataMapForDataKey.set(sysDictService.getAllDictDataMapForDataKey(true));

        for (Integer tmplId : tmplIdSet) {
            //查询模板信息
            TmplCase tmplCase = tmplCaseService.getById(tmplId);
            long currentTimeMillis2 = System.currentTimeMillis();
            log.info("开始导出 开始查询案件" + LocalDateTime.now());
            List<Long> needToExportCaseIdList = new ArrayList<>();
            //查询需要导出的案件数据id
            long currentTimeMillis14 = System.currentTimeMillis();
            log.info("查询CaseIdList开始" + LocalDateTime.now());
            if (param.getIfChooseAll()){
                setParam(param.getMediateCasePageBO(), null);
                needToExportCaseIdList = baseMapper.selectCaseIdListByParam(param.getMediateCasePageBO(),tmplId).stream().distinct().collect(Collectors.toList());
            } else {
                needToExportCaseIdList = baseMapper.selectCaseIdList(param.getCaseIds(), tmplId).stream().distinct().collect(Collectors.toList());
            }
            long currentTimeMillis13 = System.currentTimeMillis();
            log.info("查询CaseIdList结束" + LocalDateTime.now());
            log.info("查询CaseIdList时间差" + (currentTimeMillis13 - currentTimeMillis14));
            int pageSize = (int) Math.ceil((double) needToExportCaseIdList.size() / totalPages); //页大小
            int lvTotalPages = totalPages;
            //如果数据量低于500就一次性全拿出来,不进行分页查询
            if (needToExportCaseIdList.size() < 500) {
                pageSize = needToExportCaseIdList.size();
                lvTotalPages = 1;
            }
            int totalItems = needToExportCaseIdList.size(); //总数
            // 创建一个 Vector 用于存放 InputStream
            Map<String, List<InputStream>> inputStreamsMap = new HashMap<>();
            boolean ifNeedMerge = false;
            for (int page = 0; page < lvTotalPages; page++) {
                int fromIndex = page * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, totalItems);
                //获取需要分页查询的caseId数据
                List<Long> needToExportCaseIdListPage = needToExportCaseIdList.subList(fromIndex, toIndex);
                //查询需要导出的案件及其模板的对应关系
                if (needToExportCaseIdListPage.isEmpty()) {
                    log.info("起始下标:" + fromIndex);
                    log.info("结束下标:" + toIndex);
                    log.info("导出案件id:" + needToExportCaseIdList.toString());
                    log.info("分页数:" + lvTotalPages);
                    log.info("模板名称:" + tmplCase.getTmplTitle());
                    continue;
                }
                long currentTimeMillis12 = System.currentTimeMillis();
                log.info("查询DetailCaseList开始" + LocalDateTime.now());
                List<CaseDetail> caseDetailList = baseMapper.selectCaseDetail(needToExportCaseIdListPage, tmplId);
                List<MdtCaseLitigantDTO> litigantList = baseMapper.selectLitigantDetail(needToExportCaseIdListPage, tmplId);
                Map<Long, List<MdtCaseLitigantDTO>> litigantListMap = litigantList.stream().collect(Collectors.groupingBy(MdtCaseLitigant::getCaseId));
                List<MdtCaseCollectRecordDTO> collectRecordList = baseMapper.selectRecordDetail(needToExportCaseIdListPage, tmplId);
                Map<Long, List<MdtCaseCollectRecordDTO>> collectRecordListMap = collectRecordList.stream().collect(Collectors.groupingBy(MdtCaseCollectRecordDTO::getCaseId));

                TaskInstancePageBO taskInstancePageBO = new TaskInstancePageBO();
                taskInstancePageBO.setCaseIdList(needToExportCaseIdListPage);
                List<TaskInstanceDTO> taskInstanceDTOList = taskInstanceService.list(taskInstancePageBO);

                Map<Long, List<TaskInstanceDTO>> taskInstanceListMap = taskInstanceDTOList.stream().collect(Collectors.groupingBy(TaskInstanceDTO::getCaseId));

                List<MdtCaseCustomModuleDTO> customDataList = baseMapper.selectCustomDetail(needToExportCaseIdListPage, tmplId);
                Map<Long, List<MdtCaseCustomModuleDTO>> customDataListMap = customDataList.stream().collect(Collectors.groupingBy(MdtCaseCustomModuleDTO::getCaseId));

                //审批记录
                List<MdtCaseApprovalDTO> approvalRecordList =mdtCaseApprovalService.listByCaseIds(needToExportCaseIdListPage);
                Map<Long, List<MdtCaseApprovalDTO>> approvalRecordListMap = approvalRecordList.stream().collect(Collectors.groupingBy(MdtCaseApprovalDTO::getCaseId));

                for (CaseDetail caseDetail : caseDetailList){
                    caseDetail.setLitigants(litigantListMap.get(caseDetail.getCaseId()) != null ? litigantListMap.get(caseDetail.getCaseId()) : new ArrayList<>());
                    caseDetail.setCollectRecords(collectRecordListMap.get(caseDetail.getCaseId()) != null ? collectRecordListMap.get(caseDetail.getCaseId()) : new ArrayList<>());
                    caseDetail.setTaskInstances(taskInstanceListMap.get(caseDetail.getCaseId()) != null ? taskInstanceListMap.get(caseDetail.getCaseId()) : new ArrayList<>());
                    caseDetail.setCustomData(customDataListMap.get(caseDetail.getCaseId()) != null ? customDataListMap.get(caseDetail.getCaseId()) : new ArrayList<>());
                    caseDetail.setCaseApprovals(approvalRecordListMap.getOrDefault(caseDetail.getCaseId(), new ArrayList<>()));
                }
                List<Long> caseIdList = caseDetailList.stream()
                        .sorted(Comparator.comparing(CaseDetail::getCreateTime).reversed())
                        .map(CaseDetail::getCaseId)
                        .collect(Collectors.toList());
                long currentTimeMillis3 = System.currentTimeMillis();
                log.info("查询DetailCaseList结束" + LocalDateTime.now());
                log.info("查询DetailCaseList时间差" + (currentTimeMillis3 - currentTimeMillis12));
                Map<Long, CaseDetail> caseDetailMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(caseDetailList)) {
                    caseDetailMap = caseDetailList.stream().collect(Collectors.toMap(CaseDetail::getCaseId, item -> item));
                }
                Map<Integer, List<CaseDetail>> caseMap = new HashMap<>();
                for (Long caseId : caseIdList) {
                    CaseDetail caseDetail = caseDetailMap.get(caseId);
                    ValidateUtils.notNull(caseDetail, "案件不存在caseId[{}]", caseId);
                    if (!CollectionUtils.isEmpty(caseDetail.getCustomData())) {
                        Map<Integer, List<MdtCaseCustomModuleDTO>> collect = caseDetail.getCustomData().stream()
                                .filter(mdtCaseCustomModuleDTO -> !CollectionLocalUtils.areAllFieldsEmpty(mdtCaseCustomModuleDTO))
                                .collect(Collectors.groupingBy(MdtCaseCustomModuleDTO::getTmplModuleId));
                        caseDetail.setCustomDataMap(collect);
                    }
                    ValidateUtils.notNull(caseDetail, "案件不存在-[caseId:{}]", caseId);
                    List<CaseDetail> caseDetails = caseMap.computeIfAbsent(caseDetail.getTmplCaseId(), a -> new ArrayList<>());
                    caseDetails.add(caseDetail);
                }
                long currentTimeMillis4 = System.currentTimeMillis();
                log.info("开始导出 开始写入sheet表数据" + LocalDateTime.now());
                for (DeputyTmplDetail deputyTmplDetail : deputyTmplDetailMap.get(tmplId)) {
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    String deputyTmplTitle = deputyTmplDetail.getTmplTitle();
                    ExcelWriter writer = EasyExcel.write(out).build();
                    List<CaseDetail> caseDetails = caseMap.get(deputyTmplDetail.getTmplId());
                    AtomicReference<Integer> i = new AtomicReference<>(1);

                    //系统模块
                    long currentTimeMillis8 = System.currentTimeMillis();
                    log.info("开始导出 开始写入系统模块数据" + LocalDateTime.now());
                    List<DeputyTmplModuleDTO> systemModules = deputyTmplDetail.getSystemModules();
                    addSheet(systemModules, caseDetails, writer, i,fieldValueMap);

                    long currentTimeMillis9 = System.currentTimeMillis();
                    log.info("开始导出 开始写入业务模块数据" + LocalDateTime.now());
                    log.info("时间差" + (currentTimeMillis9 - currentTimeMillis8));
                    List<DeputyTmplModuleDTO> businessModules = deputyTmplDetail.getBusinessModules();
                    addSheet(businessModules, caseDetails, writer, i,fieldValueMap);

                    long currentTimeMillis10 = System.currentTimeMillis();
                    log.info("开始导出 开始写入自定义模块数据" + LocalDateTime.now());
                    log.info("时间差" + (currentTimeMillis10 - currentTimeMillis9));
                    List<DeputyTmplModuleDTO> customModules = deputyTmplDetail.getCustomModules();
                    for (DeputyTmplModuleDTO customModule : customModules) {
                        String moduleTitle = customModule.getModuleTitle();
                        List<DeputyTmplMappingDTO> mappings = customModule.getMappings();
                        if (CollectionUtils.isEmpty(mappings)) {
                            continue;
                        }
                        List<List<String>> header = mappings.stream().map(a -> Collections.singletonList(a.getFieldName())).collect(Collectors.toList());
                        header.add(0, Collections.singletonList("案件编号"));

                        List<List<String>> data = new ArrayList<>();
                        for (CaseDetail caseDetail : caseDetails) {
                            List<String> stringData = new ArrayList<>();
                            stringData.add(caseDetail.getCaseNo());
                            Map<Integer, List<MdtCaseCustomModuleDTO>> customDataMap = caseDetail.getCustomDataMap();
                            if (CollectionUtils.isEmpty(customDataMap)) {
                                data.add(stringData);
                                continue;
                            }
                            List<MdtCaseCustomModuleDTO> customData = customDataMap.get(customModule.getTmplModuleId());
                            if (customData == null){
                                data.add(stringData);
                                continue;
                            }
                            for (MdtCaseCustomModuleDTO mdtCaseCustomModuleDTO : customData){
                                List<String> stringDataList = new ArrayList<>();
                                stringDataList.add(caseDetail.getCaseNo());
                                Map<String, String> dataList = mdtCaseCustomModuleDTO.getData();
                                for (DeputyTmplMappingDTO mapping : customModule.getMappings()) {
                                    String fieldTitle = mapping.getFieldTitle();
                                    String value;
                                    if (mapping.getOriginalName() != null) {
                                        value = dataList.get(mapping.getOriginalName());
                                    } else {
                                        value = dataList.get(fieldTitle);
                                    }
                                    value = value == null ? "" : value;
                                    // TODO 待转换
                                    if (StringUtils.isNotBlank(mapping.getConverter())) {

                                    }
                                    stringDataList.add(value);
                                }
                                data.add(stringDataList);
                            }
                        }

                        WriteSheet sheet = EasyExcel.writerSheet(i.getAndSet(i.get() + 1), moduleTitle)
                                .head(header)
                                .registerWriteHandler(MdtCaseBatchImportServiceImpl.pandaCaseExcelStyle())
                                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 40, (short) 23))
                                .build();
                        writer.write(data, sheet);
                    }
                    long currentTimeMillis11 = System.currentTimeMillis();
                    log.info("写入自定义模块数据结束" + LocalDateTime.now());
                    log.info("时间差" + (currentTimeMillis11 - currentTimeMillis10));
                    writer.finish();
                    InputStream input = new ByteArrayInputStream(out.toByteArray());
                    try {
                        out.close();
                    } catch (Exception ignored) {
                        log.error("导出案件关闭流失败");
                    }
                    InputStream mvInputStream = streamMap.get(tmplCase.getTmplTitle() + "-" + deputyTmplTitle + ".xlsx");
                    List<InputStream> inputStreams = inputStreamsMap.get(tmplCase.getTmplTitle() + "-" + deputyTmplTitle);
                    if (inputStreams == null || inputStreams.isEmpty()) {
                        inputStreams = new ArrayList<>();
                        inputStreamsMap.put(tmplCase.getTmplTitle() + "-" + deputyTmplTitle, inputStreams);
                    }
                    inputStreams.add(input);
                    if (mvInputStream != null && lvTotalPages > 1) {
                        ifNeedMerge = true;
                    } else {
                        streamMap.put(tmplCase.getTmplTitle() + "-" + deputyTmplTitle + ".xlsx", input);
                    }
                }
                long currentTimeMillis5 = System.currentTimeMillis();
                log.info("开始导出 写入sheet表数据结束" + LocalDateTime.now());
                log.info("时间差" + (currentTimeMillis5 - currentTimeMillis4));
            }
            for (DeputyTmplDetail deputyTmplDetail : deputyTmplDetailMap.get(tmplId)) {
                List<InputStream> inputStreams = inputStreamsMap.get(tmplCase.getTmplTitle() + "-" + deputyTmplDetail.getTmplTitle());
                if (ifNeedMerge && inputStreams.size() > 1) {
                    log.info("开始合并多页excel");
                    // 合并 InputStream 并转换为 ByteArrayInputStream
                    try {
                        // 使用 ByteArrayOutputStream 保存合并后的数据
                        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

                        // 存储合并后的数据，以 sheet 名称作为 key
                        Map<String, List<List<String>>> combinedData = new HashMap<>();
                        // 存储每个 sheet 的列名
                        Map<String, List<String>> headers = new HashMap<>();
                        List<String> sheetNames = new ArrayList<>();

                        for (InputStream inputStream : inputStreams) {
                            // 读取每个文件的所有 sheet
                            EasyExcel.read(inputStream, new ReadListener<Map<Integer, String>>() {
                                private Map<String, List<List<String>>> sheetData = new HashMap<>(); // 存储当前 sheet 的数据
                                private List<String> currentHeaders; // 当前 sheet 的列名
                                private String currentSheetName; // 当前 sheet 的名称

                                @Override
                                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                                    List<List<String>> sheetDataList = sheetData.get(currentSheetName);
                                    if (sheetDataList == null || sheetDataList.isEmpty()) {
                                        sheetDataList = new ArrayList<>();
                                    }
                                    // 对齐数据，确保每一列数据对应正确
                                    List<String> alignedRow = new ArrayList<>(Collections.nCopies(currentHeaders.size(), ""));
                                    for (int i = 0; i < Math.min(currentHeaders.size(), data.size()); i++) {
                                        alignedRow.set(i, data.get(i));
                                    }
                                    sheetDataList.add(alignedRow); // 收集对齐后的行数据
                                    sheetData.put(currentSheetName, sheetDataList);
                                }

                                @Override
                                public void doAfterAllAnalysed(AnalysisContext context) {
                                    // 如果已经包含该 sheet 的数据，则将新的数据添加到现有数据中
                                    combinedData.putIfAbsent(currentSheetName, new ArrayList<>());
                                    List<List<String>> valuseLists = sheetData.get(currentSheetName);
                                    if (valuseLists == null) {
                                        valuseLists = new ArrayList<>();
                                    }
                                    combinedData.get(currentSheetName).addAll(valuseLists); // 合并数据
                                    headers.put(currentSheetName, currentHeaders); // 记录列名
                                    if (!sheetNames.contains(currentSheetName)) {
                                        sheetNames.add(currentSheetName);
                                    }
                                }

                                @Override
                                public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                                    // 尝试通过 context 获取当前 sheet 名称
                                    currentSheetName = context.readSheetHolder().getSheetName();
                                    currentHeaders = new ArrayList<>();
                                    //添加header信息
                                    for (Integer i : headMap.keySet()) {
                                        currentHeaders.add(headMap.get(i).getStringValue());
                                    }
                                }

                                @Override
                                public boolean hasNext(AnalysisContext context) {
                                    return true;
                                }
                            }).doReadAll(); // 读取所有 sheet
                            inputStream.close(); // 关闭输入流
                        }
                        // 写入合并后的数据到 ByteArrayOutputStream
                        // 将数据写入 ByteArrayOutputStream
                        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).build();
                        for (String sheetName : sheetNames) {
                            List<List<String>> data = combinedData.get(sheetName);
                            List<String> columnHeaders = headers.get(sheetName); // 获取对应的列名

                            // 写入当前 sheet 的数据
                            List<List<String>> finalData = new ArrayList<>(data); // 添加合并的数据

                            List<List<String>> sheetHeaders = new ArrayList<>();
                            for (String columnHeader : columnHeaders) {
                                List<String> headerName = new ArrayList<>();
                                headerName.add(columnHeader);
                                sheetHeaders.add(headerName);
                            }

                            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName)
                                    .head(sheetHeaders)
                                    .registerWriteHandler(MdtCaseBatchImportServiceImpl.pandaCaseExcelStyle())
                                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 40, (short) 23))
                                    .build();
                            excelWriter.write(finalData, writeSheet);
                        }
                        excelWriter.finish();
                        // 将 ByteArrayOutputStream 转换为 ByteArrayInputStream
                        InputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
                        byteArrayOutputStream.close();
                        streamMap.put(tmplCase.getTmplTitle() + "-" + deputyTmplDetail.getTmplTitle() + ".xlsx", byteArrayInputStream);
                        log.info("合并完成，输出流已准备好。");
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        long currentTimeMillis6 = System.currentTimeMillis();
        log.info("开始导出 开始压缩excel" + LocalDateTime.now());
        try {
            if (streamMap.values().size() > 1) {
                // 压缩
                byte[] bytes = FileIOUtils.toZipBytes(streamMap);
                HttpResponseUtils.setResponseForDownloadFile(response, "导出案件.zip");
                IOUtils.copy(new ByteArrayInputStream(bytes), response.getOutputStream());
            } else {
                for (String key : streamMap.keySet()) {
                    InputStream stream = streamMap.get(key);
                    HttpResponseUtils.setResponseForDownloadFile(response, key);
                    IOUtils.copy(stream, response.getOutputStream());
                }
            }
        } catch (Exception e) {
            log.error("导出案件,压缩数据文件失败,失败时间:{}", LocalDateTime.now());
            ByteArrayOutputStream baos = exceptionPrintTransation(e);
            log.error("导出案件,压缩数据文件失败,失败具体原因:{}", baos.toString());
            throw new ValidateBusinessException("导出案件失败,压缩excel失败");
        }
        long currentTimeMillis7 = System.currentTimeMillis();
        log.info("开始导出 压缩excel结束" + LocalDateTime.now());
        log.info("时间差" + (currentTimeMillis7 - currentTimeMillis6));
        log.info("导出 结束:" + LocalDateTime.now());
    }

    @Override
    public CaseDetail caseDetail(Long caseId) {
        CaseDetail caseDetail = baseMapper.selectDetailCase(caseId);
        List<MdtCaseCustomModuleDTO> customData = caseDetail.getCustomData();
        //去除空值的自定义模块
        CollectionLocalUtils.checkAndSetNullIfEmptyFields(customData);
        Map<Integer, List<MdtCaseCustomModuleDTO>> customDataDTOList = customData.stream().collect(Collectors.groupingBy(MdtCaseCustomModuleDTO::getTmplModuleId));
        List<MdtCaseCustomModuleDTO> mdtCaseCustomList = new ArrayList<>();
        for (Map.Entry<Integer,List<MdtCaseCustomModuleDTO>> entry : customDataDTOList.entrySet()){
            Integer moduleId = entry.getKey();
            List<TmplCaseModuleField> tmplCaseModuleFields = tmplCaseModuleFieldMapper.selectList(new LambdaQueryWrapper<TmplCaseModuleField>().eq(TmplCaseModuleField::getTmplModuleId, moduleId));
            List<TmplModuleFieldDTO> tmplModuleFieldDTOS = CglibMapper.copyList(tmplCaseModuleFields, TmplModuleFieldDTO.class);
            List<MdtCaseCustomModuleDTO> mdtCaseCustomModuleDTOList = entry.getValue();
            MdtCaseCustomModuleDTO mdtCaseCustomModuleDTO = new MdtCaseCustomModuleDTO();
            MdtCaseCustomModuleDTO caseCustomModuleDTO = mdtCaseCustomModuleDTOList.get(0);
            mdtCaseCustomModuleDTO.setAllowMultipleRecord(caseCustomModuleDTO.getAllowMultipleRecord());
            mdtCaseCustomModuleDTO.setFields(tmplModuleFieldDTOS);
            mdtCaseCustomModuleDTO.setModuleSn(caseCustomModuleDTO.getModuleSn());
            mdtCaseCustomModuleDTO.setModuleTitle(caseCustomModuleDTO.getModuleTitle());
            mdtCaseCustomModuleDTO.setModuleType(caseCustomModuleDTO.getModuleType());
            mdtCaseCustomModuleDTO.setTmplModuleId(moduleId);
            if (mdtCaseCustomModuleDTOList.get(0).getAllowMultipleRecord() == 1){
                List<CustomDataDTO> values = new ArrayList<>();
                for (MdtCaseCustomModuleDTO customModuleDTO : mdtCaseCustomModuleDTOList){
                    CustomDataDTO customDataDTO = new CustomDataDTO();
                    List<PropMap<String,String>> valueList = new ArrayList<>();
                    List<TmplModuleFieldDTO> fields = tmplModuleFieldDTOS;
                    fields.sort(Comparator.comparingInt(TmplModuleFieldDTO::getFieldSn));
                    Map<String, String> data = customModuleDTO.getData();
                    for (TmplModuleFieldDTO field : fields) {
                        PropMap<String, String> value = new PropMap<>();
                        String fieldTitle = field.getFieldTitle();
                        String originalName = field.getOriginalName();
                        String s;
                        if (originalName != null) {
                            s = data.get(originalName);
                        } else {
                            s = data.get(fieldTitle);
                        }
                        value.setKey(fieldTitle);
                        value.setValue(s == null ? "" : s);
                        valueList.add(value);
                    }
                    if (caseCustomModuleDTO.getDataId() != null){
                        customDataDTO.setDataId(customModuleDTO.getDataId());
                        customDataDTO.setData(valueList);
                        values.add(customDataDTO);
                    }
                }
                mdtCaseCustomModuleDTO.setValues(values);
            } else {
                List<PropMap<String, String>> valueList = new ArrayList<>();
                List<TmplModuleFieldDTO> fields = tmplModuleFieldDTOS;
                fields.sort(Comparator.comparingInt(TmplModuleFieldDTO::getFieldSn));
                Map<String, String> data = caseCustomModuleDTO.getData();
                for (TmplModuleFieldDTO field : fields) {
                    PropMap<String, String> value = new PropMap<>();
                    String fieldTitle = field.getFieldTitle();
                    String originalName = field.getOriginalName();
                    String s;
                    if (originalName != null) {
                        s = data.get(originalName);
                    } else {
                        s = data.get(fieldTitle);
                    }
                    value.setKey(fieldTitle);
                    value.setValue(s == null ? "" : s);
                    valueList.add(value);
                }
                mdtCaseCustomModuleDTO.setValueList(valueList);
                mdtCaseCustomModuleDTO.setDataId(caseCustomModuleDTO.getDataId());
            }
            mdtCaseCustomList.add(mdtCaseCustomModuleDTO);
        }
        mdtCaseCustomList.sort(Comparator.comparingInt(MdtCaseCustomModuleDTO::getModuleSn));
        caseDetail.setCustomData(mdtCaseCustomList);
        List<MdtContactRepairRecordDto> mdtContactRepairRecordServiceLeastRecord = mdtContactRepairRecordService.getLeastRecord(caseId);
        Map<String, MdtContactRepairRecordDto> contactRepairRecordDtoMap = mdtContactRepairRecordServiceLeastRecord.stream()
                .collect(Collectors.toMap(MdtContactRepairRecordDto::getIdCard, item -> item));
        for (MdtCaseLitigantDTO litigant : caseDetail.getLitigants()) {
            String idCard = litigant.getIdNo();
            try {
                if (StringUtils.isNotBlank(litigant.getIdNo())) {
                    litigant.setIdNo(AESEncrypt.decrypt(idCard));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            MdtContactRepairRecordDto mdtContactRepairRecordDto = null;
            try {
                mdtContactRepairRecordDto = contactRepairRecordDtoMap.get(idCard);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (mdtContactRepairRecordDto != null) {
                litigant.setVirtualNo(mdtContactRepairRecordDto.getVirtualNo());
                litigant.setMatchRepairResult(mdtContactRepairRecordDto.getMatchRepairResult());
                litigant.setNumberRepairResult(mdtContactRepairRecordDto.getNumberRepairResult());

                if (mdtContactRepairRecordDto.getRepairVirtualNoTime() != null) {
                    ZonedDateTime zonedDateTime = mdtContactRepairRecordDto.getRepairVirtualNoTime().plusMinutes(15).atZone(ZoneId.systemDefault());
                    Date now = new Date();
                    Date deadLine = Date.from(zonedDateTime.toInstant());
                    long diffInMillis = deadLine.getTime() - now.getTime();
                    litigant.setBatchExpireDate(TimeUnit.MILLISECONDS.toSeconds(diffInMillis));
                    litigant.setIfShowVirtualButton(getIfShowVirtualButton(mdtContactRepairRecordDto));
                } else {
                    litigant.setIfShowVirtualButton(false);
                }
            }
        }
        for (MdtCaseLitigantDTO litigant : caseDetail.getLitigants()) {
            Long litigantId = litigant.getLitigantId();
            MdtRepairPhone mdtRepairPhone = mdtRepairPhoneMapper.selectOne(new LambdaQueryWrapper<MdtRepairPhone>().eq(MdtRepairPhone::getCaseId, caseId).eq(MdtRepairPhone::getLitigantId, litigantId).isNotNull(MdtRepairPhone::getVirtualNo));
            if (mdtRepairPhone != null){
                if (mdtRepairPhone.getVirtualNo() != null){
                    litigant.setVirtualNo(mdtRepairPhone.getVirtualNo());
                    litigant.setBatchExpireDate(Duration.between(LocalDateTime.now(),mdtRepairPhone.getNumberExpireTime()).toMinutes());
                }
            }
        }

        //调解记录按调解时间排序,如果相等就按创建时间排序
        if (caseDetail.getCollectRecords() != null) {
            Comparator<MdtCaseCollectRecordDTO> comparator =
                    Comparator
                            .comparing(MdtCaseCollectRecordDTO::getMediateTime, Comparator.nullsLast(Comparator.reverseOrder()))
                            .thenComparing(MdtCaseCollectRecordDTO::getCreateTm, Comparator.nullsLast(Comparator.reverseOrder()));

            caseDetail.getCollectRecords().sort(comparator);
        }

        //如果业务类型不为空就检索到对应的业务类型
        if(StrUtil.isNotBlank(caseDetail.getBusinessType())){
            caseDetail.setBusinessTypeObj(sysBusinessTypeService.getById(caseDetail.getBusinessType()));
        }


        return caseDetail;
    }

    @Override
    public Long caseUpdate(CaseUpdateBO param, Long accountId) {

        //获取所有字模板
        DeputyTmplDetail mappingList = deputyTmplMappingService.tmplList(param.getTmplModuleId());
        Map<String, DeputyTmplModuleDTO> moduleTitleMap = mappingList.getAllModules().stream().collect(Collectors.toMap(DeputyTmplModuleDTO::getModuleTitle, Function.identity()));
        if (moduleTitleMap.keySet().size() > 1) {
            throw new ValidateBusinessException("数据出错,不允许有两个一样的自定义模块");
        }
        DeputyTmplModuleDTO deputyTmplModuleDTO = moduleTitleMap.get(moduleTitleMap.keySet().iterator().next());
        Map<String, DeputyTmplMappingDTO> mappings = deputyTmplModuleDTO.getMappings().stream().collect(Collectors.toMap(DeputyTmplMappingDTO::getFieldTitle, item -> item));
        Map<String, String> tmplMap = new HashMap<>();
        List<String> tmplKey = new ArrayList<>();
        for (String key : param.getData().keySet()) {
            String value = param.getData().get(key);
            DeputyTmplMappingDTO mappingDTO = mappings.get(key);
            boolean checkValue = checkValue(value, mappingDTO, deputyTmplModuleDTO);
            if (!checkValue) {
                throw new ValidateBusinessException(String.format("%s数据格式不正确", mappingDTO.getFieldTitle()));
            }
            if (mappingDTO.getOriginalName() != null) {
                tmplMap.put(mappingDTO.getOriginalName(), value);
                tmplKey.add(key);
            }
        }
        param.getData().putAll(tmplMap);
        for (String key : tmplKey) {
            param.getData().remove(key);
        }
        if (param.getDataId() != null) {
            MdtCaseCustomData customData = mdtCaseCustomDataService.getById(param.getDataId());
            ValidateUtils.notNull(customData, "自定义数据不存在");
            MdtCaseCustomData newData = new MdtCaseCustomData();
            newData.setFieldValue(JacksonUtils.writeValueAsString(param.getData()));
            newData.setDataId(param.getDataId());
            newData.setUpdaterId(accountId);
            mdtCaseCustomDataService.updateById(newData);
            return param.getDataId();
        } else {
            long id = IdWorker.getId();
            MdtCaseCustomData data = new MdtCaseCustomData();
            data.setDataId(id);
            data.setCaseId(param.getCaseId());
            data.setCreatorId(accountId);
            data.setUpdaterId(accountId);
            data.setTmplModuleId(param.getTmplModuleId());
            data.setFieldValue(JacksonUtils.writeValueAsString(param.getData()));
            if (deputyTmplModuleDTO.getAllowMultipleRecord() == 0){
                if (mdtCaseCustomDataService.getBaseMapper().selectCount(new LambdaQueryWrapper<MdtCaseCustomData>().eq(MdtCaseCustomData::getCaseId,param.getCaseId()).eq(MdtCaseCustomData::getTmplModuleId,param.getTmplModuleId())) > 0){
                    throw new ValidateBusinessException("有其他用户正在保存该自定义模块，请重新刷新页面后再添加数据。");
                }
            }
            mdtCaseCustomDataService.save(data);
            return id;
        }

    }

    @Override
    public CaseLitigantInfo getCaseIdByPhone(String phone) {
        List<MdtCaseLitigant> list = mdtCaseLitigantService.getByPhoneLike(phone);
        if (!CollectionUtils.isEmpty(list)) {
            Map<Long, List<MdtCaseLitigant>> collect = list.stream().collect(Collectors.groupingBy(MdtCaseLitigant::getCaseId));
            if (collect.size() == 1) {
                CaseLitigantInfo info = new CaseLitigantInfo();
                for (Long caseId : collect.keySet()) {
                    List<MdtCaseLitigant> mdtCaseLitigants = collect.get(caseId);
                    String name = mdtCaseLitigants.stream().map(MdtCaseLitigant::getLitigantName).distinct().collect(Collectors.joining(","));
                    info.setCaseId(caseId);
                    info.setCalleeName(name);
                    List<Long> litigantId = mdtCaseLitigants.stream().map(MdtCaseLitigant::getLitigantId).distinct().collect(Collectors.toList());
                    info.setLitigantId(litigantId);
                    return info;
                }
            }
        }
        return null;
    }

    @Override
    public PageDTO<MdtCaseCollectionRecordReceiveLogDto> getPageCaseCollectionRecordReceiveLog(PagingParam<MdtCaseCollectionRecordReceiveLogBo> pagingParam) {
        Page<MdtCaseCollectionRecordReceiveLogDto> page = PageUtil.getPage(pagingParam, MdtCaseCollectionRecordReceiveLogDto.class);
        MdtCaseCollectionRecordReceiveLogBo mdtCaseCollectionRecordReceiveLogBo = pagingParam.getParam();
        if (mdtCaseCollectionRecordReceiveLogBo != null) {
            //查询案件接收方式
            String receiveMessage = ReceiveType.getMessageByCode(mdtCaseCollectionRecordReceiveLogBo.getReceiveType());
            if (receiveMessage != null) mdtCaseCollectionRecordReceiveLogBo.setCollectionType(receiveMessage);
            //查询案件创建结果
            String createResultsMessage = CreateResultsEnum.getMessageByCode(mdtCaseCollectionRecordReceiveLogBo.getCreateResult());
            if (createResultsMessage != null)
                mdtCaseCollectionRecordReceiveLogBo.setCollectionResult(createResultsMessage);
        }
        baseMapper.getCaseCollectionRecordReviewLogPageList(page.setOptimizeCountSql(false), pagingParam.getParam());
        PageDTO<MdtCaseCollectionRecordReceiveLogDto> result = PageDTO.getInstance(page);
        return result;
    }

    @Override
    public List<MdtCaseCollectionRecordReceiveLogDto> getCaseCollectionRecordReceiveLogListByCaseId(Long caseId) {
        List<MdtCaseCollectionRecordReceiveLogDto> result = baseMapper.getCaseCollectionRecordReceiveLogListByCaseId(caseId);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSendSms(MdtCaseBatchSmsBo mdtCaseBatchSmsBo) {
        //1.查询所有案件当事人及其手机号码---------------这里会有问题，因为我们要发送信息给每一个当事人，所以会不可避免出现重复数据问题-----------
        List<MdtCaseBatchSmsDto> mdtCaseBatchSmsDtoList = baseMapper.getCaseConcatPhone(mdtCaseBatchSmsBo);
        if (mdtCaseBatchSmsDtoList == null || mdtCaseBatchSmsDtoList.size() < 1) {
            throw new ValidateBusinessException("案件无对应当事人类型信息");
        }
        //2.去重复当事人名称
        Map<Long, Map<String, Map<Long, String>>> litigantIdentityType = new HashMap<>();
        for (MdtCaseBatchSmsDto mdtCaseBatchSmsDto : mdtCaseBatchSmsDtoList) {
            Map<String, Map<Long, String>> record = new HashMap<>();
            Map<Long, String> litigantMap = new HashMap<>();
            Map<Long, String> respondMap = new HashMap<>();
            if (litigantIdentityType.get(mdtCaseBatchSmsDto.getCaseId()) != null) {
                litigantMap = litigantIdentityType.get(mdtCaseBatchSmsDto.getCaseId()).get(LitigantIdentityType.PLAINTIFF.getTitle());
                respondMap = litigantIdentityType.get(mdtCaseBatchSmsDto.getCaseId()).get(LitigantIdentityType.DEFENDANT.getTitle());
                //如果是相同的当事人 这里会覆盖掉  以此来达到去重的效果
                litigantMap.put(mdtCaseBatchSmsDto.getClaimantId(), mdtCaseBatchSmsDto.getClaimantName());
                respondMap.put(mdtCaseBatchSmsDto.getRespondId(), mdtCaseBatchSmsDto.getRespondName());
            } else {
                litigantMap.put(mdtCaseBatchSmsDto.getClaimantId(), mdtCaseBatchSmsDto.getClaimantName());
                respondMap.put(mdtCaseBatchSmsDto.getRespondId(), mdtCaseBatchSmsDto.getRespondName());
            }
            record.put(LitigantIdentityType.PLAINTIFF.getTitle(), litigantMap);
            record.put(LitigantIdentityType.DEFENDANT.getTitle(), respondMap);
            litigantIdentityType.put(mdtCaseBatchSmsDto.getCaseId(), record);
        }
        //3.设置每个案件原告，被告
        Map<Long, Map<String, String>> caseIdentityType = new HashMap<>();
        for (Long caseId : litigantIdentityType.keySet()) {
            Map<Long, String> litigantMap = litigantIdentityType.get(caseId).get(LitigantIdentityType.PLAINTIFF.getTitle()); //原告
            Map<Long, String> respondMap = litigantIdentityType.get(caseId).get(LitigantIdentityType.DEFENDANT.getTitle()); //被告
            StringBuilder litigantNames = new StringBuilder();
            StringBuilder respondNames = new StringBuilder();
            //设置原告人名称
            for (Long litigantId : litigantMap.keySet()) {
                litigantNames.append(litigantMap.get(litigantId)).append(",");
            }
            //设置被告人名称
            for (Long respondId : respondMap.keySet()) {
                respondNames.append(respondMap.get(respondId)).append(",");
            }
            Map<String, String> record = new HashMap<>();
            record.put(LitigantIdentityType.PLAINTIFF.getTitle(), String.valueOf(litigantNames).replaceAll(",$", ""));
            record.put(LitigantIdentityType.DEFENDANT.getTitle(), String.valueOf(respondNames).replaceAll(",$", ""));
            caseIdentityType.put(caseId, record);
        }
        //4.查询模板信息
        MdtSmsTemplateDTO mdtSmsTemplateDTO = mdtSmsTemplateService.getTemplateById(mdtCaseBatchSmsBo.getTmplId());
        ValidateUtils.notNull(mdtSmsTemplateDTO, "无模板信息");
        //5.查询案件分派信息
        List<CaseDistributeLogDto> mdtCaseDistributeRecordList = mdtCaseDistributeRecordService.getLastDistributeRecordList(mdtCaseBatchSmsBo.getCaseIdList());
        if (mdtCaseDistributeRecordList == null || mdtCaseDistributeRecordList.size() < 1) {
            throw new ValidateBusinessException("无相关案件分派信息");
        }
        //6.筛选最近分派信息
        Map<Long, CaseDistributeLogDto> caseDistributeLogDtoMap = new HashMap<>();
        for (CaseDistributeLogDto caseDistributeLogDto : mdtCaseDistributeRecordList) {
            CaseDistributeLogDto distributeLogDto = caseDistributeLogDtoMap.get(caseDistributeLogDto.getCaseId());
            if (distributeLogDto == null || distributeLogDto.getOperateTime().isBefore(caseDistributeLogDto.getOperateTime())) {
                caseDistributeLogDtoMap.put(caseDistributeLogDto.getCaseId(), caseDistributeLogDto);
            }
        }
        //7.写入param message
        List<Map<String, String>> entrustsSmsMessageParamList = new ArrayList<>();
        List<String> phoneNumbers = new ArrayList<>();
        //8.去重----------------------去掉重复发送信息的可能性---------------------------
        List<MdtCaseBatchSmsDto> distinctCaseBatchSms = new ArrayList<>(mdtCaseBatchSmsDtoList.stream()
                .collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getCaseId() + ":" + o.getLitigantId())))));
        //9.写入
        HashMap<String, String> phoneIdentityType = new HashMap<>();
        distinctCaseBatchSms.forEach(item -> {
            ValidateUtils.notNull(item.getRespondName(), "被申请人姓名不能为空");
            EntrustsSmsMessage entrustsSmsMessage = new EntrustsSmsMessage(
                    caseIdentityType.get(item.getCaseId()).get(LitigantIdentityType.PLAINTIFF.getTitle()),
                    caseIdentityType.get(item.getCaseId()).get(LitigantIdentityType.DEFENDANT.getTitle()),
                    item.getCaseNo(),
                    caseDistributeLogDtoMap.get(item.getCaseId()).getDistributeTargetName(),
                    caseDistributeLogDtoMap.get(item.getCaseId()).getMediatorConcatPhone()
            );
            Map<String, String> smsMessageParam = new HashMap<>();
            //entrustsSmsMessage转换为json string
            Gson gson = new Gson();
            String entrustsSmsMessageJson = gson.toJson(entrustsSmsMessage);
            smsMessageParam.put(item.getCaseId().toString(), entrustsSmsMessageJson);
            entrustsSmsMessageParamList.add(smsMessageParam);
            phoneNumbers.add(item.getLitigantPhone());
            phoneIdentityType.put(item.getCaseId() + item.getLitigantPhone(), item.getLitigantName());
        });
        String txTemplateId = mdtSmsTemplateDTO.getTxTemplateId();
        SplitMobileAndParamsUtil.splitMobileAndParams(phoneNumbers,entrustsSmsMessageParamList);
        //10.根据联系人号码发送短信
        SendBatchSmsResponse sendBatchSmsResponse = smsCloudTencentUtil.sendBatchSmsByTemplate(txTemplateId, entrustsSmsMessageParamList, phoneNumbers);
        //11.信息入库
        MdtSmsTemplate template = mdtSmsTemplateService.getById(mdtSmsTemplateDTO.getSmsTemplateId());
        for (Long caseId : caseDistributeLogDtoMap.keySet()) {
            String names = caseIdentityType.get(caseId).get(LitigantIdentityType.PLAINTIFF.getTitle()) + "," + caseIdentityType.get(caseId).get(LitigantIdentityType.DEFENDANT.getTitle());
            //发送短信数据入库
            mdtSmsSendService.saveSmsWithResponse(caseId, null, sendBatchSmsResponse, template, entrustsSmsMessageParamList,
                    names, phoneNumbers, phoneIdentityType);
        }
        return true;
    }

    @Override
    public List<MdtCase> listByCaseIds(List<Long> caseIds) {
//        return baseMapper.listByCaseIds(caseIds);
        if (CollUtil.isEmpty(caseIds)) {
            return new ArrayList<>();
        }
        return baseMapper.selectBatchIds(caseIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MdtCaseDTO singleImport(MdtCaseSingleImportBo mdtCaseSingleImportBo) {
        //1.添加案件信息
        MdtCase mdtCase = new MdtCase();
        long caseId = IdWorker.getId();
        mdtCase.setCaseId(caseId);
        SysEntrusts sysEntrusts = sysEntrustsService.getById(mdtCaseSingleImportBo.getEntrustsId());
        Long entrustsId = sysEntrusts.getEntrustsId();
        ValidateUtils.notNull(sysEntrusts, "案源方不存在");
        Long count = baseMapper.selectCount(new LambdaQueryWrapper<MdtCase>().eq(MdtCase::getCaseNo, mdtCaseSingleImportBo.getCaseNo()));
        if (count > 0) {
            throw new ValidateBusinessException("案件编号已存在");
        }
        mdtCase.setCaseNo(mdtCaseSingleImportBo.getCaseNo());
        mdtCase.setCaseName(mdtCaseSingleImportBo.getCaseName());
        mdtCase.setCompanyId(sysEntrusts.getCompanyId());
        mdtCase.setCaseNatureContent(mdtCaseSingleImportBo.getCaseNatureContent());
        mdtCase.setCaseSubjectMatter(mdtCaseSingleImportBo.getCaseSubjectMatter());
        mdtCase.setMdtCaseStatus(CaseStatus.UN_DISTRIBUTE.getCode());
        mdtCase.setCreateTime(LocalDateTime.now());
        mdtCase.setEntrustsId(entrustsId);
        mdtCase.setEntrustsDeptId(ServiceThreadLocal.getCurrentUser().getDeptId());
        mdtCase.setMediateStatus(MediateStatus.NULL.getCode());
        mdtCase.setBusinessType(mdtCaseSingleImportBo.getBusinessType());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date mediateBeginDate = null;
        Date mediateEndDate = null;
        if (!StringUtils.isAllBlank(mdtCaseSingleImportBo.getMediateBeginTime(), mdtCaseSingleImportBo.getMediateEndTime())) {
            try {
                mediateBeginDate = simpleDateFormat.parse(mdtCaseSingleImportBo.getMediateBeginTime());
                mediateEndDate = simpleDateFormat.parse(mdtCaseSingleImportBo.getMediateEndTime());
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (mediateBeginDate != null && mediateEndDate != null) {
            Instant beginInstant = mediateBeginDate.toInstant();
            Instant endInstant = mediateEndDate.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            mdtCase.setMediateBeginTime(beginInstant.atZone(zoneId).toLocalDateTime());
            mdtCase.setExpirationTime(endInstant.atZone(zoneId).toLocalDateTime());
        }
        mdtCase.setCompleteReview(mdtCaseSingleImportBo.getCompleteReview());
        DeputyTmplDetail mappingList = deputyTmplMappingService.mappingList(mdtCaseSingleImportBo.getDeputyTmplId());
        if (mappingList == null || CollectionUtils.isEmpty(mappingList.getAllModules())) {
            throw new ValidateBusinessException("模板未配置");
        }
        mdtCase.setTmplCaseId(mappingList.getTmplId());
        Long accountId = ServiceThreadLocal.getCurrentUser().getAccountId();
        UserInfoDTO userInfo = authEmployeeService.getUserInfo(ServiceThreadLocal.getCurrentUser().getEmployeeId());
        mdtCase.setCreatorId(accountId);
        mdtCase.setUpdateTime(LocalDateTime.now());
        mdtCase.setUpdaterId(accountId);
        boolean save = this.save(mdtCase);
        boolean entrustsUpdate = sysEntrustsService.updateById(sysEntrusts);
        if (!(save && entrustsUpdate)) {
            throw new ValidateBusinessException("添加案件失败");
        }
        //2.添加当事人信息
        List<MdtCaseLitigant> mdtCaseLitigantList = new ArrayList<>();
        for (MdtCaseLitigantSaveBO mdtCaseLitigantSaveBO : mdtCaseSingleImportBo.getMdtCaseLitigantSaveBOList()) {
            MdtCaseLitigant mdtCaseLitigant = new MdtCaseLitigant();
            long litigantId = IdWorker.getId();
            mdtCaseLitigant.setLitigantId(litigantId);
            mdtCaseLitigant.setCaseId(caseId);
            mdtCaseLitigant.setLitigantType(mdtCaseLitigantSaveBO.getLitigantType());
            mdtCaseLitigant.setIdentityType(mdtCaseLitigantSaveBO.getIdentityType());
            mdtCaseLitigant.setLitigantName(mdtCaseLitigantSaveBO.getLitigantName());
            mdtCaseLitigant.setIdType(mdtCaseLitigantSaveBO.getIdType());
            try {
                mdtCaseLitigant.setIdNo(AESEncrypt.encrypt(mdtCaseLitigantSaveBO.getIdNo()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            mdtCaseLitigant.setLitigantPhone(mdtCaseLitigantSaveBO.getLitigantPhone());
            mdtCaseLitigant.setSex(mdtCaseLitigantSaveBO.getSex());
            mdtCaseLitigant.setPost(mdtCaseLitigantSaveBO.getPost());
            mdtCaseLitigant.setPhoneStatus(mdtCaseLitigantSaveBO.getPhoneStatus());
            mdtCaseLitigant.setLitigantDesc(mdtCaseLitigantSaveBO.getLitigantDesc());
            mdtCaseLitigant.setCreateTime(LocalDateTime.now());
            mdtCaseLitigant.setCreatorId(accountId);
            mdtCaseLitigant.setUpdateTime(LocalDateTime.now());
            mdtCaseLitigant.setUpdaterId(accountId);
            mdtCaseLitigantList.add(mdtCaseLitigant);
        }
        boolean litigantSaveBatch = mdtCaseLitigantService.saveBatch(mdtCaseLitigantList);
        if (!litigantSaveBatch) {
            throw new ValidateBusinessException("添加当事人失败");
        }
        //3.添加案件接收记录
        List<Map<String, String>> caseInfo = new ArrayList<>();
        Map<String, String> info = new HashMap<>();
        info.put("caseNo", mdtCase.getCaseNo());
        info.put("caseId", mdtCase.getCaseId().toString());
        caseInfo.add(info);
        MdtCaseCollectionRecord record = new MdtCaseCollectionRecord();
        record.setAcceptTime(LocalDateTime.now());
        record.setCollectionResult("创建成功");
        record.setCollectionType("单案录入");
        record.setCreatorName(userInfo.getEmployeeName());
        record.setEntrustsName(sysEntrusts.getEntrustsName());
        record.setCompanyName(userInfo.getCompanyName());
        record.setTmplName(mappingList.getTmplTitle());
        record.setCaseInfo(JacksonUtils.writeValueAsString(caseInfo));
        mdtCaseCollectionRecordService.save(record);
        MdtCaseDTO mdtCaseDTO = new MdtCaseDTO();
        mdtCaseDTO.setCaseId(mdtCase.getCaseId());
        return mdtCaseDTO;
    }

    @Override
    public List<MdtCase> getCaseByOrgId(Long orgId) {
        QueryWrapper<MdtCase> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MdtCase::getOrgId, orgId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MediationSuccessRateDTO> getMediationSuccessRate(List<Long> caseIds, Integer groupCondition) {
        return baseMapper.getMediationSuccessRate(caseIds, groupCondition);
    }

    @Override
    public List<DiversionRateDTO> getDiversionRate(List<Long> caseIds, Integer groupCondition) {
        return baseMapper.getDiversionRate(caseIds, groupCondition);
    }

    @Override
    public List<CaseCloseRateDTO> getCaseCloseRate(List<Long> caseIds, Integer groupCondition) {
        return baseMapper.getCaseCloseRate(caseIds, groupCondition);
    }

    @Override
    public List<CaseReachRateDTO> getCaseReachRate(LocalDateTime startTime, LocalDateTime endTime, Integer groupCondition) {
        return baseMapper.getCaseReachRate(startTime, endTime, groupCondition);
    }

    @Override
    @SneakyThrows
    public MdtCaseLitigantDTO detail(Long litigantId) {
        MdtCaseLitigant mdtCaseLitigant = mdtCaseLitigantService.getById(litigantId);
        mdtCaseLitigant.setIdNo(AESEncrypt.decrypt(mdtCaseLitigant.getIdNo()));
        ValidateUtils.notNull(mdtCaseLitigant, "无对应当事人信息");
        return CglibMapper.copy(mdtCaseLitigant, MdtCaseLitigantDTO.class);
    }

    @Override
    public void exportCollectionReceiveLog(MdtCaseCollectionRecordReceiveLogBo mdtCaseCollectionRecordReceiveLogBo, HttpServletResponse response) {
        //1.查询需要导出的数据，最大50w条
        List<MdtCaseCollectionRecordReceiveLogDto> mdtCaseCollectionRecordReceiveLogDtos = baseMapper.selectCollectionReceiveLog(mdtCaseCollectionRecordReceiveLogBo);
        ExcelExportUtil<MdtCaseCollectionRecordReceiveLogDto> excelExportUtil = new ExcelExportUtil<>(MdtCaseCollectionRecordReceiveLogDto.class);
        excelExportUtil.exportRecordToExcel(response, mdtCaseCollectionRecordReceiveLogDtos, "导出案件接收记录.xlsx", "案件接收记录", MAX_ROWS);
    }

    @Override
    public List<Long> getAllListId(MediateCasePageBO mediateCasePageBO) {
        setParam(mediateCasePageBO, null);
        return baseMapper.getAllListId(mediateCasePageBO);
    }

    private void addSheet(List<DeputyTmplModuleDTO> modules, List<CaseDetail> caseDetails, ExcelWriter writer, AtomicReference<Integer> i, Map<String,List<String>> fieldValueMap) {
        for (DeputyTmplModuleDTO module : modules) {
            String tableName = module.getTableName();
            String moduleTitle = module.getModuleTitle();
            List<DeputyTmplMappingDTO> mappings = module.getMappings();
            if (CollectionUtils.isEmpty(mappings)) {
                continue;
            }
            List<List<String>> header = mappings.stream().map(a -> Collections.singletonList(a.getFieldName())).collect(Collectors.toList());
            if (!ModuleTableEnum.MDT_CASE.getTableName().equals(tableName)) {
                header.add(0, Collections.singletonList("案件编号"));
            }
            if (ModuleTableEnum.MDT_CASE.getTableName().equals(tableName)){
                //新增两个临时需求固定列
                header.add(Collections.singletonList("案件延期截止日期"));
                header.add(Collections.singletonList("案件办理申请时间"));
            }


            List<List<String>> data = new ArrayList<>();
            for (CaseDetail caseDetail : caseDetails) {
                if (ModuleTableEnum.LITIGANT.getTableName().equals(tableName)) {
                    List<MdtCaseLitigantDTO> litigants = caseDetail.getLitigants();
                    for (MdtCaseLitigantDTO litigant : litigants) {
                        List<String> stringData = getData(mappings, litigant,fieldValueMap);
                        stringData.add(0, caseDetail.getCaseNo());
                        data.add(stringData);
                    }
                } else if (ModuleTableEnum.COLLECT_RECORD.getTableName().equals(tableName)) {
                    List<MdtCaseCollectRecordDTO> collectRecords = caseDetail.getCollectRecords();
                    for (MdtCaseCollectRecordDTO collectRecord : collectRecords) {
                        List<String> stringData = getData(mappings, collectRecord,fieldValueMap);
                        stringData.add(0, caseDetail.getCaseNo());
                        data.add(stringData);
                    }
                } else if (ModuleTableEnum.MDT_CASE.getTableName().equals(tableName)) {
                    List<String> stringData = getData(mappings, caseDetail,fieldValueMap);

                    //新增两个临时需求固定列
                    //获取最新的审批记录
                    List<MdtCaseApprovalDTO> caseApprovals = caseDetail.getCaseApprovals();
                    if (CollUtil.isNotEmpty(caseApprovals)){
                        //获取申请日期最靠后的那个
                        MdtCaseApprovalDTO mdtCaseApprovalDTO = caseApprovals.stream().max(Comparator.comparing(MdtCaseApprovalDTO::getStartTime)).get();
                        stringData.add(LocalDateTimeUtil.format(mdtCaseApprovalDTO.getCaseDelayTime(), "yyyy-MM-dd"));
                        stringData.add(DateUtil.formatLocalDateTime(mdtCaseApprovalDTO.getStartTime()));
                    }



                    data.add(stringData);
                } else if (ModuleTableEnum.TASK_INSTANCE.getTableName().equals(tableName)) {
                    List<TaskInstanceDTO> taskCompleteRegisters = caseDetail.getTaskInstances();
                    if(taskCompleteRegisters.isEmpty()){
                        TaskInstanceDTO taskInstanceDTO = new TaskInstanceDTO();
                        // 任务默认值? 应该永远不会有这个情况
                        taskCompleteRegisters.add(taskInstanceDTO);
                    }
                    List<TaskInstanceDTO> taskInstanceDTOList = taskCompleteRegisters.stream().distinct().collect(Collectors.toList());
                    for (TaskInstanceDTO taskInstanceDTO : taskInstanceDTOList) {
                        List<String> stringData = getData(mappings, taskInstanceDTO,fieldValueMap);
                        stringData.add(0, caseDetail.getCaseNo());
                        data.add(stringData);
                    }
                } else {
                    throw new ValidateBusinessException("未知的模块类型表名[{}]", tableName);
                }
            }


            WriteSheet sheet = EasyExcel.writerSheet(i.getAndSet(i.get() + 1), moduleTitle)
                    .head(header)
                    .registerWriteHandler(MdtCaseBatchImportServiceImpl.pandaCaseExcelStyle())
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 40, (short) 23))
                    .build();
            writer.write(data, sheet);
        }
    }

    private List<String> getData(List<DeputyTmplMappingDTO> mappings, Object object, Map<String,List<String>> fieldValueMap) {
        List<String> stringData = new ArrayList<>();
        for (DeputyTmplMappingDTO mapping : mappings) {
            String tableFieldName = mapping.getTableFieldName();
            tableFieldName = com.baomidou.mybatisplus.core.toolkit.StringUtils.underlineToCamel(tableFieldName);
            String fieldValuesString = mapping.getFieldValues();
            StringBuilder result = new StringBuilder();
            getResult(fieldValuesString, result, object, tableFieldName,fieldValueMap);
            Object obj = new Object();
            String value;
            if (StringUtils.isNotBlank(result.toString())) {
                value = result.toString();
            } else {
                obj = getValue(object, tableFieldName);
                value = obj == null ? "" : obj.toString();
            }
            String fieldDataType = mapping.getFieldDataType();
            if (StringUtils.isNotBlank(value)) {
                switch (fieldDataType) {
                    case FieldDataType.DICT:
                        if (StringUtils.isNotBlank(mapping.getTableFieldName())) {
                            if (!"litigant_type".equals(mapping.getTableFieldName()) && !"identity_type".equals(mapping.getTableFieldName())){
                                BaseEnum baseEnum = EnumManage.getBaseEnum(mapping.getTableFieldName());
                                if (baseEnum != null) {
                                    value = baseEnum.getTitleByCode(value);
                                }
                            }
                        }
                        break;
                    case FieldDataType.DATETIME:
                        value = ((LocalDateTime) obj).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        break;
                    case FieldDataType.SYSTEM_DICT:
                        value = getSystemDictTagByKey( mapping, value);
                        break;
                    default:
                        break;
                }
            }
            // TODO 待转换
            if (StringUtils.isNotBlank(mapping.getConverter())) {

            }

            stringData.add(value);

        }
        return stringData;
    }

    private String getSystemDictTagByKey( DeputyTmplMappingDTO mapping, String value) {
        String fieldDictType = mapping.getFieldDictType();
        if (StrUtil.isBlank(fieldDictType)) {
            // 如果没有配置字典类型，直接返回原值
            return value;
        }

        Map<String, Map<String, String>> allDictDataMap = this.dictDataMapForDataKey.get();
        Map<String, String> dictDataMap = allDictDataMap.get(fieldDictType);
        if (dictDataMap == null) {
            // 如果字典类型不存在，直接返回原值
            return value;
        }

        try {
            return dictDataMap.getOrDefault(value,value);
        } catch (Exception e) {
            log.error("获取系统字典值失败", e);
            return value;
        }
    }

    /**
     * 获取field字段值
     */
    private void getResult(String fieldValuesString, StringBuilder result, Object object, String tableFieldName,Map<String,List<String>> fieldValueMap) {
        if (StringUtils.isNotBlank(fieldValuesString)) {
            Gson gson = new Gson();
            Type fieldEnumerationType = new TypeToken<List<FieldEnumeration>>() {
            }.getType();
            FieldValues fieldValues = gson.fromJson(fieldValuesString, FieldValues.class);
            String type = fieldValues.getType();
            //需要进行转换的字段
            if (FieldValuesTypeEnum.COLLECTION.getType().equals(type)) {
                Object obj = getValue(object, tableFieldName);
                String value = obj == null ? "" : obj.toString();
                result.append(value).append(",");
            } else if (FieldValuesTypeEnum.ENUMERATION.getType().equals(type)) {
                String values = fieldValues.getValues();
                Object obj = getValue(object, tableFieldName);
                String value = obj == null ? "" : obj.toString();
                List<FieldEnumeration> fieldEnumerations = gson.fromJson(values, fieldEnumerationType);
                for (FieldEnumeration fieldEnumeration : fieldEnumerations) {
                    if (value.equals(fieldEnumeration.getKey().toString())) {
                        result.append(fieldEnumeration.getValue()).append(",");
                    }
                }
            } else if (FieldValuesTypeEnum.DICTIONARY.getType().equals(type)) {
                List<SysDictData> sysDictDataList = sysDictDataService.list(new QueryWrapper<SysDictData>().lambda().eq(SysDictData::getDictId, Integer.valueOf(fieldValues.getDictId())));
                Object obj = getValue(object, tableFieldName);
                String value = obj == null ? "" : obj.toString();
                for (SysDictData sysDictData : sysDictDataList) {
                    if (sysDictData.getDictDataId().equals(Integer.valueOf(value))) {
                        result.append(sysDictData.getDictTag()).append(",");
                    }
                }
            } else if (FieldValuesTypeEnum.FOREIGN_KEY.getType().equals(type)) {
                //根据外键获取数据
                String[] tableNameSplit = fieldValues.getTableName().split(",");
                String[] valueFieldNameSplit = fieldValues.getValueFieldName().split(",");
                String[] keyFieldNameSplit = fieldValues.getKeyFieldName().split(",");
                String tableName = tableNameSplit[0];
                String keyFieldName = keyFieldNameSplit[0];
                String valueFieldName = valueFieldNameSplit[0];
                Object obj = getValue(object, tableFieldName);
                List<String> fieldValue = new ArrayList<>();
                if (obj != null && fieldValueMap.get(obj.toString()) != null){
                    fieldValue = fieldValueMap.get(obj.toString());
                }else {
                    fieldValue = baseMapper.getFieldDataByFieldValue(tableName, keyFieldName, valueFieldName, obj == null ? "" : obj.toString());
                    if (obj != null){
                        fieldValueMap.put(obj.toString(),fieldValue);
                    }
                }
                if (tableNameSplit.length > 1 && valueFieldNameSplit.length > 1 && keyFieldNameSplit.length > 1) {
                    String tableName2 = tableNameSplit[1];
                    String keyFieldName2 = keyFieldNameSplit[1];
                    String valueFieldName2 = valueFieldNameSplit[1];
                    if (fieldValue != null && fieldValue.size() > 0) {
                        List<String> fieldDataByFieldValueList = baseMapper.getFieldDataByFieldValueList(tableName2, keyFieldName2, valueFieldName2, fieldValue);
                        for (String value : fieldDataByFieldValueList) {
                            result.append(value).append(",");
                        }
                    }
                } else {
                    for (String value : fieldValue) {
                        result.append(value).append(",");
                    }
                }
            }else if (FieldValuesTypeEnum.MULTIPLE_FOREIGN_KEY.getType().equals(type)) {
                //根据外键获取数据
                String[] tableNameSplit = fieldValues.getTableName().split(",");
                String[] valueFieldNameSplit = fieldValues.getValueFieldName().split(",");
                String[] keyFieldNameSplit = fieldValues.getKeyFieldName().split(",");
                String tableName = tableNameSplit[0];
                String keyFieldName = keyFieldNameSplit[0];
                String valueFieldName = valueFieldNameSplit[0];
                //将字符串obj转为List<String>
                Object obj = getValue(object, tableFieldName);
                List<Long> objList = (List<Long>)obj;
                if (CollUtil.isEmpty(objList)){
                    return;
                }
                List<String> fieldDataByFieldValueList = baseMapper.getFieldDataByFieldValueList(tableName, keyFieldName, valueFieldName, objList);
                for (String value : fieldDataByFieldValueList) {
                    result.append(value).append(",");
                }
            }
            if (result.length() > 0 && result.charAt(result.length() - 1) == ',') {
                result.deleteCharAt(result.length() - 1);  // 删除最后一个字符
            }
        }
    }

    private Object getValue(Object object, String key) {
        for (Class c = object.getClass(); c != Object.class; c = c.getSuperclass()) {
            try {
                Field field = c.getDeclaredField(key);
                if (field == null) {
                    continue;
                }
                field.setAccessible(true);

                return field.get(object);

            } catch (Exception ignored) {

            }
        }
        log.error("[获取属性失败:{}]", key);
        throw new ValidateBusinessException("[获取属性失败:{}]", key);
    }

    private DocumentEnum getDocumentEnum(String fileType) {
        String title = FileTypeEnum.getTitleByCode(fileType);
        return DocumentEnum.getEnumByName(title);
    }

    private AttachmentEnum getAttachmentEnum(String fileType) {
        String title = FileTypeEnum.getTitleByCode(fileType);
        return AttachmentEnum.getEnumByDesc(title);
    }

    private String getFileByFileType(String code, MdtCaseBatchFileDTO mdtCase) {
        switch (code) {
            case "1":
                return mdtCase.getMediationAgreement();
            case "2":
                return mdtCase.getMediationNotice();
            case "3":
                return mdtCase.getMediationNotification();
            default:
                return null;
        }
    }

    private MdtCommissionDTO getCommissionMsg() {
        SysParameterDTO cName = sysParameterService.getSysParameterByModuleAndParam(
                ParameterModuleCode.COMMISSION,
                ParameterParamCode.COMMISSION_C_NAME);
        if (cName == null) {
            return null;
        }
        String eName = sysParameterService.getSysParameterByModuleAndParam(
                ParameterModuleCode.COMMISSION,
                ParameterParamCode.COMMISSION_E_NAME).getParamValue();
        String phone = sysParameterService.getSysParameterByModuleAndParam(
                ParameterModuleCode.COMMISSION,
                ParameterParamCode.COMMISSION_PHONE).getParamValue();
        String address = sysParameterService.getSysParameterByModuleAndParam(
                ParameterModuleCode.COMMISSION,
                ParameterParamCode.COMMISSION_ADDRESS).getParamValue();
        String url = sysParameterService.getSysParameterByModuleAndParam(
                ParameterModuleCode.SYSTEM,
                ParameterParamCode.SYSTEM_LOGO_URL).getParamValue();
        return new MdtCommissionDTO(cName.getParamValue(), eName, phone, address, url);
    }

    private Boolean checkFileSign(String filePath) {
        return false;
    }

    @Override
    public MdtCaseFrontDTO getOneFront(Long caseId) {
        MdtCaseFrontDTO mdtCaseFrontDTO = baseMapper.getOneFront(caseId);
        mdtCaseFrontDTO.setAgreementFlag(checkFileSign(mdtCaseFrontDTO.getMediationAgreement()));
        mdtCaseFrontDTO.setNoticeFlag(checkFileSign(mdtCaseFrontDTO.getMediationNotice()));
        return mdtCaseFrontDTO;
    }

    @Override
    public void setParam(MediateCasePageBO bo, PagingParam<MediateCasePageBO> param) {
        TokenUser currentUser = ServiceThreadLocal.getCurrentUser();
        String companyType = currentUser.getCompanyType();
        Long companyId = currentUser.getCompanyId();
        Long deptId = currentUser.getDeptId();
        Long accountId = currentUser.getAccountId();
        if (bo == null) {
            bo = new MediateCasePageBO();
            if (param != null) {
                param.setParam(bo);
            }
        }
        List<EntrustsOrgBO> entrustsOrgBOList = bo.getEntrustsOrgBOList();
        if (entrustsOrgBOList != null && !entrustsOrgBOList.isEmpty()){

            List<Long> entrustsIds = new ArrayList<>();
            List<Long> entrustsDeptIds = new ArrayList<>();
            List<Long> entrustsJudgeIds = new ArrayList<>();

            for (EntrustsOrgBO entrustsOrgBO : entrustsOrgBOList){
                if ("1".equals(entrustsOrgBO.getType())){
                    entrustsIds.add(entrustsOrgBO.getOrgId());
                } else if ("2".equals(entrustsOrgBO.getType())){
                    entrustsDeptIds.add(entrustsOrgBO.getOrgId());
                } else if ("3".equals(entrustsOrgBO.getType())){
                    entrustsJudgeIds.add(entrustsOrgBO.getOrgId());
                }
            }

            bo.setEntrustsIds(entrustsIds);
            bo.setEntrustsDeptIds(entrustsDeptIds);
            bo.setEntrustsJudgeIds(entrustsJudgeIds);
        }

        if (CompanyType.PLATFORM.getCode().equals(companyType)) {

        } else if (CompanyType.ENTRUSTS.getCode().equals(companyType)) {
            // 案源方可以查看自己的案件
            List<String> roleNames = currentUser.getRoleNames();
            Long entrustsId = sysEntrustsService.getEntrustsIdByCompanyId(companyId);
            //案源方负责人能看到案源方所有案件
            if (entrustsOrgBOList == null || entrustsOrgBOList.isEmpty()){
                bo.setEntrustsIds(Collections.singletonList(entrustsId));
            }
            if (roleNames.contains(SysRoleName.COURT_DIRECTOR.getRoleName())) {
                //庭室负责人只能看到当前庭室的数据
                bo.setEntrustsDeptId(deptId);
            } else if (!roleNames.contains(SysRoleName.ENTRUSTS_ADMIN.getRoleName())) {
                //指导法官和司法辅助人员只能看到自己的案件数据
                bo.setEntrustsDeptId(deptId);
                bo.setCreatorId(accountId);
            }
        } else if (CompanyType.MDT_ORG.getCode().equals(companyType)) {
            List<String> roleNames = currentUser.getRoleNames();
            Long orgId = sysMdtOrgService.getOrgIdByCompanyId(companyId);
            bo.setOrgIds(Collections.singletonList(orgId));
            if (roleNames.contains(SysRoleName.ORG_ADMIN.getRoleName())) {

            } else if (roleNames.contains(SysRoleName.DEPT_ADMIN.getRoleName())) {
                // 调解组长看自己组下的
                bo.setDeptIds(Collections.singletonList(deptId));
            } else if (roleNames.contains(SysRoleName.MEDIATOR.getRoleName())) {
                // 调解员看自己调解的案件
                bo.setCurrentMediatorIds(Collections.singletonList(accountId));
            }
        } else {
            bo.setCurrentMediatorIds(Collections.singletonList(accountId));
        }

        //多行案件编号拆分
        String caseNo = bo.getCaseNo();
        if (StrUtil.isNotBlank(caseNo) && StrUtil.containsAny(caseNo,"\r\n","\n","\r")){
            List<String> caseNoList = Arrays.stream(caseNo.split("\\r?\\n|\\r")).map(StrUtil::trim).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            bo.setCaseNoList(caseNoList);
            bo.setCaseNo(null);
        }
    }

    @Override
    public List<TmplDTO> tmplList(MediateCasePageBO bo) {
        return baseMapper.tmplList2(bo);
    }

    @Override
    public void caseDelete(Long dataId, Long currentAccountId) {
        if (dataId == null){
            throw new ValidateBusinessException("找不到对应的自定义数据");
        }
        MdtCaseCustomData mdtCaseCustomData = mdtCaseCustomDataService.getById(dataId);
        if (mdtCaseCustomData == null){
            throw new ValidateBusinessException("找不到对应的自定义数据");
        }
        mdtCaseCustomDataService.getBaseMapper().deleteById(dataId);
    }

    @Override
    public List<MdtCase> getAllMdtCase(MediateCasePageBO bo) {

        return baseMapper.getAllMdtCase(bo);
    }

    @Override
    public List<MdtCase> getCreatorIdAndEntrustsDeptIds(MediateCasePageBO bo){
        return baseMapper.getCreatorIdAndEntrustsDeptIds(bo);
    }

    protected boolean checkValue(String value, DeputyTmplMappingDTO mapping, DeputyTmplModuleDTO module) {
        if (StringUtils.isBlank(value)) {
            if (StatusConstants.YES.equals(mapping.getIsRequire())) {
                return false;
            } else {
                return true;
            }
        }
        if (StatusConstants.NO.equals(mapping.getIsUpdate())) {
            return false;
        }

        String componentRelation = mapping.getComponentRelation();
        if (componentRelation != null) {
            if (componentRelation.equals("ARangePicker") || componentRelation.equals("ARangeTimePicker")) {
                try {
                    DateUtil.parse(value);
                    DateUtil.parse(value);
                } catch (Exception ignored) {
                    try {
                        DateTimeUtils.parseDateTime(value);
                        DateTimeUtils.parseDateTime(value);
                    } catch (Exception e) {
                        return false;
                    }
                }
            } else if (componentRelation.equals("AInputNumber")) {
                try {
                    new BigDecimal(value);
                } catch (Exception ignored) {
                    return false;
                }
            }
        }

        if (StatusConstants.YES.equals(mapping.getIsValidated())) {
            String validation = mapping.getValidation();
            // TODO 校验
        }
        return true;
    }

    private void setActive(List<Long> caseIds, Boolean mediatorFlag, Long accountId, MyCaseDetailDTO myCaseDetailDTO) {
        QueryWrapper<MdtCaseCollectRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(MdtCaseCollectRecord::getCaseId, caseIds)
                .between(MdtCaseCollectRecord::getCreateTm,
                        LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN),
                        LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
        int yesterdayNumber = mdtCaseCollectRecordService.list(queryWrapper).stream().map(MdtCaseCollectRecord::getCaseId).collect(Collectors.toSet()).size();
        myCaseDetailDTO.setYesterdayActive(yesterdayNumber);
        //跟进过的案件集合
        Set<Long> caseIdSet = mdtCaseCollectRecordService.list(new QueryWrapper<MdtCaseCollectRecord>()
                .lambda().in(MdtCaseCollectRecord::getCaseId, caseIds))
                .stream().map(MdtCaseCollectRecord::getCaseId)
                .collect(Collectors.toSet());

        int count = 0;
        if (!CollectionUtils.isEmpty(caseIdSet)) {
            QueryWrapper<MdtCaseCollectRecord> recordQueryWrapper = new QueryWrapper<>();
            recordQueryWrapper.lambda()
                    .in(MdtCaseCollectRecord::getCaseId, caseIdSet)
                    .ge(MdtCaseCollectRecord::getCreateTm, LocalDateTime.of(LocalDate.now().minusDays(3), LocalTime.MIN));
            //距离上次跟进超过三天的
            count = caseIdSet.size() - mdtCaseCollectRecordService.list(recordQueryWrapper).stream().map(MdtCaseCollectRecord::getCaseId).collect(Collectors.toSet()).size();
        }
        //从未跟进超过三天的
        caseIds.removeAll(caseIdSet);
        int caseNum = 0;
        if (!caseIds.isEmpty()) {
            LambdaQueryWrapper<MdtCase> lambdaQueryWrapper = new QueryWrapper<MdtCase>()
                    .lambda()
                    .eq(MdtCase::getMdtCaseStatus, CaseStatus.PROCESS_ING.getCode())
                    .in(MdtCase::getCaseId, caseIds)
                    .le(MdtCase::getCreateTime, LocalDateTime.of(LocalDate.now().minusDays(3), LocalTime.MIN));
            if (mediatorFlag) {
                lambdaQueryWrapper.eq(MdtCase::getCurrentMediatorId, accountId);
            }
            caseNum = baseMapper.selectList(lambdaQueryWrapper).size();
        }
        myCaseDetailDTO.setThreeDayNotActive(caseNum + count);
    }

    private String getChineseNumber(int i) {
        if (i < 1 || i > 12) {
            throw new RuntimeException("只能转换1-12的数字");
        }
        return chineseNum[i];
    }

    /**
     * 日志转换打印
     */
    private ByteArrayOutputStream exceptionPrintTransation(Exception e) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(baos));
        return baos;
    }

    /**
     * 获取是否可以显示虚拟号刷新按钮
     */
    private Boolean getIfShowVirtualButton(MdtContactRepairRecordDto mdtContactRepairRecordDto) {
        return RepairStatusEnum.SUCCESS_REPAIR.getCode().equals(Integer.valueOf(mdtContactRepairRecordDto.getBatchStatus()))
                && LocalDateTime.now().isAfter(mdtContactRepairRecordDto.getRepairVirtualNoTime().plusMinutes(15))
                && new Date().before(mdtContactRepairRecordDto.getBatchExpireDate());
    }

    class EntrustsSmsMessage {
        private String claimantName;
        private String responseName;
        private String caseNo;
        private String mediatorName;
        private String agentPhone;

        public EntrustsSmsMessage(String claimantName, String responseName, String caseNo, String mediatorName, String agentPhone) {
            this.claimantName = claimantName;
            this.responseName = responseName;
            this.caseNo = caseNo;
            this.mediatorName = mediatorName;
            this.agentPhone = agentPhone;
        }

        public String getClaimantName() {
            return claimantName;
        }

        public void setClaimantName(String claimantName) {
            this.claimantName = claimantName;
        }

        public String getResponseName() {
            return responseName;
        }

        public void setResponseName(String responseName) {
            this.responseName = responseName;
        }

        public String getCaseNo() {
            return caseNo;
        }

        public void setCaseNo(String caseNo) {
            this.caseNo = caseNo;
        }

        public String getMediatorName() {
            return mediatorName;
        }

        public void setMediatorName(String mediatorName) {
            this.mediatorName = mediatorName;
        }

        public String getAgentPhone() {
            return agentPhone;
        }

        public void setAgentPhone(String agentPhone) {
            this.agentPhone = agentPhone;
        }
    }

    @Override
    public boolean save(MdtCase entity) {
        entity.setCaseNo(this.applyCaseNumberPrivacy(entity.getCaseNo()));

        return super.save(entity);
    }

    /**
     * 重写saveBatch方法，在批量保存案件前应用案件编号隐私脱敏规则
     */
    @Override
    public boolean saveBatch(Collection<MdtCase> entityList) {
        if (CollUtil.isNotEmpty(entityList)) {
            for (MdtCase entity : entityList) {
                entity.setCaseNo(this.applyCaseNumberPrivacy(entity.getCaseNo()));
            }
        }
        return super.saveBatch(entityList);
    }

    @Override
    public void updateCaseSubjectMatter(MdtCase mdtCase) {
        if(mdtCase == null || mdtCase.getCaseId() == null || mdtCase.getCaseSubjectMatter() == null){
            throw new ValidateBusinessException("修改失败");
        }
        this.update(new LambdaUpdateWrapper<MdtCase>().set(MdtCase::getCaseSubjectMatter,mdtCase.getCaseSubjectMatter()).eq(MdtCase::getCaseId,mdtCase.getCaseId()));
    }
}
