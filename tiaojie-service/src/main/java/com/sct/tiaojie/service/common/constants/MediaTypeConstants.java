package com.sct.tiaojie.service.common.constants;

import org.springframework.http.MediaType;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MediaTypeConstants {
    public static final Map<String, MediaType> mediaTypes = new HashMap<>();

    static {
        mediaTypes.put("pdf", MediaType.APPLICATION_PDF);
        mediaTypes.put("json", MediaType.APPLICATION_JSON);
        mediaTypes.put("gif", MediaType.IMAGE_GIF);
        mediaTypes.put("jpg", MediaType.IMAGE_JPEG);
        mediaTypes.put("jpeg", MediaType.IMAGE_JPEG);
        mediaTypes.put("png", MediaType.IMAGE_PNG);
        mediaTypes.put("txt", MediaType.TEXT_PLAIN);
        mediaTypes.put("xml", MediaType.TEXT_XML);
        mediaTypes.put("html", MediaType.TEXT_HTML);
        mediaTypes.put("htm", MediaType.TEXT_HTML);
        mediaTypes.put("md", MediaType.TEXT_MARKDOWN);
    }

    public static MediaType getMediaType(String extension) {
        return mediaTypes.getOrDefault(extension, MediaType.APPLICATION_OCTET_STREAM);
    }

}
