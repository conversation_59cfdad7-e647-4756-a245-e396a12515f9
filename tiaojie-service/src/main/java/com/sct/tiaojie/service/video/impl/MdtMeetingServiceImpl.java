package com.sct.tiaojie.service.video.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sct.tiaojie.common.exception.ValidateBusinessException;
import com.sct.tiaojie.common.util.ValidateUtils;
import com.sct.tiaojie.repository.auth.entity.AuthEmployee;
import com.sct.tiaojie.repository.mdt.entity.MdtCase;
import com.sct.tiaojie.service.auth.AuthEmployeeService;
import com.sct.tiaojie.service.mdt.MdtCaseService;
import com.sct.tiaojie.service.oss.ObjectFileContextService;
import com.sct.tiaojie.service.oss.OssFileService;
import com.sct.tiaojie.service.video.MdtMeetingService;
import com.sct.tiaojie.service.video.bo.MdtMeetingLitigantBO;
import com.sct.tiaojie.service.video.bo.MeetingRecordFileBO;
import com.sct.tiaojie.service.video.bo.TokenBO;
import com.sct.tiaojie.service.video.dto.MdtMeetingLitigantDTO;
import com.sct.tiaojie.service.video.dto.TokenDTO;
import com.sct.tiaojie.service.video.dto.VRoomDTO;
import com.sct.tiaojie.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MdtMeetingServiceImpl implements MdtMeetingService {

    @Resource
    private MdtCaseService mdtCaseService;
    @Resource
    private AuthEmployeeService authEmployeeService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ObjectFileContextService objectFileContextService;
    private OssFileService ossFileService;
    @Value("${vroom.domain}")
    private String domain;
    @Value("${vroom.tokenUrl}")
    private String tokenUrl;
    @Value("${vroom.litigantInfoUrl}")
    private String litigantInfoUrl;
    @Value("${vroom.authenticationName}")
    private String authenticationName;
    @Value("${vroom.authenticationValue}")
    private String authenticationValue;
    @Value("${vroom.mediationUrl}")
    private String mediationUrl;

    @PostConstruct
    private void init() {
        ossFileService = objectFileContextService.getFileService();
    }

    @Override
    public String joinMediation(Long caseId, Long accountId) {
        MdtCase mdtCase = mdtCaseService.getById(caseId);
        ValidateUtils.notNull(mdtCase, "案件不存在");
        AuthEmployee employee = authEmployeeService.getOne(new QueryWrapper<AuthEmployee>().lambda()
                .eq(AuthEmployee::getAccountId, accountId));
        TokenBO tokenBO = new TokenBO();
        tokenBO.setCaseId(caseId.toString());
        tokenBO.setCaseName(mdtCase.getCaseNo());
        tokenBO.setCaseNum(mdtCase.getCaseNo());
        tokenBO.setUserId(accountId.toString());
        tokenBO.setUserName(employee.getEmployeeName());
        String body = exchange(tokenUrl, tokenBO);
        VRoomDTO<TokenDTO> token = JacksonUtils.readValue(body, new TypeReference<VRoomDTO<TokenDTO>>() {
        });
        TokenDTO data = token.getData();
        ValidateUtils.notNull(data, "查询会议token返回data为空");
        ValidateUtils.notEmpty(data.getToken(), "查询会议token返回token为空");
        ValidateUtils.notEmpty(data.getMediationId(), "查询会议token返回mediationId为空");
        return mediationUrl.replace("{mediationId}", data.getMediationId()).replace("{token}", data.getToken());
    }

    @Override
    public MdtMeetingLitigantDTO getLitigantInfo(MdtMeetingLitigantBO param) {
        String body = exchange(litigantInfoUrl, param);
        VRoomDTO<MdtMeetingLitigantDTO> result = JacksonUtils.readValue(body, new TypeReference<VRoomDTO<MdtMeetingLitigantDTO>>() {
        });
        MdtMeetingLitigantDTO data = result.getData();
        ValidateUtils.notNull(data, "查询会议当事人返回data为空");
        return data;
    }

    @Override
    public String getMeetingRecordFilePath(MeetingRecordFileBO param) {
        String filePath = param.getFilePath();
        return ossFileService.getShareDownloadUrl(filePath);
    }

    @Override
    public byte[] downloadMeetingVideo(String downloadMeetingVideoPath) {
        return ossFileService.downloadFile(downloadMeetingVideoPath);
    }

    private <T> String exchange(String url, T param){
        HttpHeaders headers = getHeaders();
        HttpEntity<T> httpEntity = new HttpEntity<>(param, headers);
        url = getUrl(url);
        log.info("视频调解请求vroom地址：" + url);
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        } catch (Exception e){
            log.error("视频调解请求vroom失败", e);
            throw new ValidateBusinessException("视频调解请求vroom失败");
        }
        if (responseEntity.getStatusCode().isError()){
            throw new ValidateBusinessException("视频调解请求vroom失败,errorCode:[{}],msg:[{}]", responseEntity.getStatusCodeValue(), responseEntity.getStatusCode().getReasonPhrase());
        }
        String body = responseEntity.getBody();
        if (StringUtils.isBlank(body)){
            throw new ValidateBusinessException("视频调解请求vroom返回值为空");
        }
        log.info("视频调解请求vroom返回：{}", body);
        return body;
    }

    private String getUrl(String url) {
        return domain + url;
    }

    private HttpHeaders getHeaders() {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(authenticationName, authenticationValue);
        return httpHeaders;
    }
}
