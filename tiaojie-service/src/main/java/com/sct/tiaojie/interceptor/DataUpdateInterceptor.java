//package com.sct.tiaojie.interceptor;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.ReflectUtil;
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.Wrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.baomidou.mybatisplus.core.metadata.TableInfo;
//import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
//import com.baomidou.mybatisplus.core.toolkit.Constants;
//import com.google.common.base.CaseFormat;
//import com.sct.tiaojie.common.enums.RedisKeyEnums;
//import com.sct.tiaojie.config.redis.RedisUtils;
//import com.sct.tiaojie.repository.mdt.entity.MdtCaseCustomData;
//import com.sct.tiaojie.service.task.AutoTaskService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.executor.Executor;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.SqlCommandType;
//import org.apache.ibatis.plugin.*;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Component;
//
//import java.io.Serializable;
//import java.lang.reflect.Field;
//import java.lang.reflect.Method;
//import java.lang.reflect.ParameterizedType;
//import java.lang.reflect.Type;
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicBoolean;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//import static org.apache.ibatis.ognl.OgnlOps.convertValue;
//
//@Slf4j
////@Lazy
////@Component(value = "DataUpdateInterceptor")
////@Intercepts({
////        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
////})
//public class DataUpdateInterceptor implements Interceptor {
//    private static final Set<String> HANDLE_TABLES = Collections.unmodifiableSet(
//            new HashSet<String>(Arrays.asList(
//                    "mdt_case", "mdt_case_litigant", "mdt_case_collect_record", "task_instance",
//                    "mdt_case_custom_data"
//            ))
//    );
//    // 排除字段：一些公共字段+每个表的主键
//    private static final Set<String> EXCLUDED_FIELDS = Collections.unmodifiableSet(
//            new HashSet<String>(Arrays.asList(
//                    "creatorId", "createTime", "updateId", "updateTime",
//                    "createTm", "updateTm", "createdBy", "updatedBy", "caseId", "litigantId", "recordId",
//                    "instanceId", "dataId"
//            ))
//    );
//    private AutoTaskService autoTaskService;
//
//    public void setAutoTaskService(AutoTaskService autoTaskService) {
//        this.autoTaskService = autoTaskService;
//    }
//
//    private static final ThreadLocal<Boolean> processing = ThreadLocal.withInitial(() -> false);
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        // 如果当前线程已经在处理中，直接跳过
//        if (processing.get()) {
//            return invocation.proceed();
//        }
//        try {
//            processing.set(true); // 标记当前线程正在处理
//            Object[] args = invocation.getArgs();
//            MappedStatement ms = (MappedStatement) args[0];
//            Object parameter = args[1];
//            // 获取表信息
//            TableInfo tableInfo = getTableInfo(ms.getId());
//            if (tableInfo == null || !HANDLE_TABLES.contains(tableInfo.getTableName())) {
//                return invocation.proceed();
//            }
//            // 处理UPDATE操作
//            if (SqlCommandType.UPDATE == ms.getSqlCommandType()) {
//                // 处理更新参数
//                processUpdate(parameter, tableInfo);
//                return invocation.proceed();
//            } else if (SqlCommandType.INSERT == ms.getSqlCommandType()) {
//                // 处理INSERT
//                log.info("处理INSERT操作");
//                processInsert(parameter, tableInfo);
//            }
//            return invocation.proceed();
//        } catch (Exception e) {
//            log.error("DataUpdateInterceptor执行异常", e);
//            // 发生异常时仍然继续执行，避免影响正常业务
//            return invocation.proceed();
//        } finally {
//            processing.remove(); // 确保移除标记
//        }
//    }
//
//    /**
//     * @param parameter
//     * @param tableInfo
//     */
//    @SuppressWarnings("unchecked")
//    private void processInsert(Object parameter, TableInfo tableInfo) {
//        if (parameter == null || tableInfo == null) {
//            return;
//        }
//
//        try {
//            // 获取当前用户ID和时间
//            Long currentUserId = getCurrentUserId();
//            Date currentTime = new Date();
//
//            if (parameter instanceof Map) {
//                Map<String, Object> paramMap = (Map<String, Object>) parameter;
//                Object entity = paramMap.get(Constants.ENTITY);
//                if (entity != null) {
//                    // 处理Map方式的更新
//                    processEntityInsert(entity, tableInfo, currentUserId, currentTime);
//                }
//            } else {
//                // 直接添加实体对象
//                processEntityInsert(parameter, tableInfo, currentUserId, currentTime);
//            }
//        } catch (Exception e) {
//            log.warn("处理更新信息失败", e);
//        }
//    }
//
//    /**
//     * 处理更新操作的参数
//     * 自动填充更新人ID和更新时间，并比对原始值与更新值
//     *
//     * @param parameter 更新参数
//     * @param tableInfo 表信息
//     */
//    @SuppressWarnings("unchecked")
//    private void processUpdate(Object parameter, TableInfo tableInfo) {
//        if (parameter == null || tableInfo == null) {
//            return;
//        }
//
//        try {
//            // 获取当前用户ID和时间
//            Date currentTime = new Date();
//
//            if (parameter instanceof Map) {
//                Map<String, Object> paramMap = (Map<String, Object>) parameter;
//                Object entity = paramMap.get(Constants.ENTITY);
//                Object wrapper = paramMap.getOrDefault(Constants.WRAPPER, null);
//
//                if (wrapper != null) {
//                    // 处理UpdateWrapper方式的更新
//                    processUpdateWrapper(paramMap, tableInfo);
//                } else if (entity != null) {
//                    // 处理updateById方式的更新
//                    processEntityUpdate(entity, tableInfo);
//                }
//            } else {
//                // 直接更新实体对象
//                processEntityUpdate(parameter, tableInfo);
//            }
//        } catch (Exception e) {
//            log.warn("处理更新信息失败", e);
//        }
//    }
//
//    /**
//     * 从Mapper类名获取对应的实体类
//     * 通过分析Mapper接口的泛型参数，获取BaseMapper<Entity>中的Entity类型
//     *
//     * @param mapperClassName Mapper类的全限定名
//     * @return 实体类Class对象
//     */
//    public Class<?> getEntityClassByMapper(String mapperClassName) {
//        try {
//            // 1. 加载Mapper接口类
//            Class<?> mapperClass = Class.forName(mapperClassName);
//            return getEntityClassByMapperClass(mapperClass);
//        } catch (ClassNotFoundException e) {
//            log.error("找不到Mapper类: {}", mapperClassName, e);
//            throw new RuntimeException("找不到Mapper类: " + mapperClassName, e);
//        }
//    }
//
//    /**
//     * 从Mapper类获取对应的实体类
//     *
//     * @param mapperClass Mapper类
//     * @return 实体类Class对象
//     */
//    private Class<?> getEntityClassByMapperClass(Class<?> mapperClass) {
//        // 1. 获取Mapper接口继承的泛型父接口
//        Type[] genericInterfaces = mapperClass.getGenericInterfaces();
//
//        for (Type genericInterface : genericInterfaces) {
//            // 2. 检查是否是参数化类型 (如 BaseMapper<Entity>)
//            if (genericInterface instanceof ParameterizedType) {
//                ParameterizedType parameterizedType = (ParameterizedType) genericInterface;
//
//                // 3. 检查原始类型是否是BaseMapper
//                Type rawType = parameterizedType.getRawType();
//                if (rawType instanceof Class &&
//                    BaseMapper.class.isAssignableFrom((Class<?>) rawType)) {
//
//                    // 4. 获取泛型参数数组
//                    Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
//                    if (actualTypeArguments.length > 0) {
//                        Type entityType = actualTypeArguments[0];
//
//                        // 5. 返回实体类Class对象
//                        if (entityType instanceof Class) {
//                            return (Class<?>) entityType;
//                        }
//                    }
//                }
//            }
//        }
//
//        // 6. 检查父类，可能是继承了某个实现了BaseMapper的类
//        Class<?> superClass = mapperClass.getSuperclass();
//        if (superClass != null && superClass != Object.class) {
//            try {
//                return getEntityClassByMapperClass(superClass);
//            } catch (Exception ignored) {
//                // 忽略父类分析错误，继续检查接口
//            }
//        }
//
//        // 7. 如果当前类没有找到，检查所有接口
//        for (Class<?> interfaceClass : mapperClass.getInterfaces()) {
//            try {
//                return getEntityClassByMapperClass(interfaceClass);
//            } catch (Exception ignored) {
//                // 忽略接口分析错误，继续检查下一个接口
//            }
//        }
//
//        throw new IllegalArgumentException("无法从Mapper类获取实体类: " + mapperClass.getName());
//    }
//
//    @Override
//    public Object plugin(Object target) {
//        return Plugin.wrap(target, this);
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//        // 可以在这里添加配置属性的处理
//    }
//
//    /**
//     * 根据MappedStatement ID获取表信息
//     *
//     * @param msId MappedStatement ID，格式为：mapperClassName.methodName
//     * @return 表信息对象
//     */
//    private TableInfo getTableInfo(String msId) {
//        try {
//            // 从MappedStatement ID中提取Mapper类名
//            String mapperClassName = msId.substring(0, msId.lastIndexOf("."));
//
//            // 获取实体类
//            Class<?> entityClass = getEntityClassByMapper(mapperClassName);
//            if (entityClass == null) {
//                log.warn("无法从Mapper类获取实体类: {}", mapperClassName);
//                return null;
//            }
//
//            // 获取表信息
//            TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
//            if (tableInfo != null) {
//                log.debug("获取到表信息: {}", tableInfo.getTableName());
//            } else {
//                log.warn("无法获取表信息: {}", entityClass.getName());
//            }
//
//            return tableInfo;
//        } catch (Exception e) {
//            log.error("获取表信息失败: {}", msId, e);
//            return null;
//        }
//    }
//
//    /**
//     * 处理UpdateWrapper方式的更新
//     */
//    private void processUpdateWrapper(Map<String, Object> paramMap, TableInfo tableInfo) {
//        try {
//            Object wrapper = paramMap.get(Constants.WRAPPER);
//            if (wrapper == null) return;
//
//            Map<String, Object> updateFields = extractUpdateFields(wrapper);
//            if (updateFields.isEmpty()) {
//                log.debug("未从UpdateWrapper中提取到更新字段");
//                return;
//            }
//            BaseMapper<?> baseMapper = getBaseMapper(tableInfo);
//            if (baseMapper == null) return;
//
//            List<?> oldEntities = getAffectedEntities(baseMapper, wrapper);
//            if (oldEntities.isEmpty()) {
//                log.debug("未找到受影响的记录");
//                return;
//            }
//
//
//            // 记录变更
//            for (Object oldEntity : oldEntities) {
//
//                Object mockNewEntity = generateMockEntity(oldEntity, updateFields, (Wrapper<?>) wrapper);
//                if (mockNewEntity != null) {
//                    Long caseId = getCaseId(mockNewEntity);
//                    if (caseId == null) {
//                        caseId = getCaseId(oldEntity);
//                    }
//                    Map<String, Map<String, Object>> changes = compareEntities(oldEntity, mockNewEntity);
//                    if (!changes.isEmpty()) {
//                        Object entityId = getEntityId(oldEntity, tableInfo);
//                        if (entityId != null) {
//                            logChanges(tableInfo.getTableName(), Collections.singletonList(entityId), changes, caseId, mockNewEntity);
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.warn("处理UpdateWrapper更新失败", e);
//        }
//    }
//
//    public Map<String, Object> extractLambadaUpdateValues(LambdaUpdateWrapper<?> updateWrapper) {
//        try {
//            // 反射获取 UpdateWrapper 中的 paramNameValuePairs
//            return updateWrapper.getParamNameValuePairs();
//        } catch (Exception e) {
//            throw new RuntimeException("提取 UpdateWrapper 参数失败", e);
//        }
//    }
//
//    /**
//     * 处理实体方式的插入
//     *
//     * @param entity
//     * @param tableInfo
//     * @param currentUserId
//     * @param currentTime
//     */
//    private void processEntityInsert(Object entity, TableInfo tableInfo, Long currentUserId, Date currentTime) {
//        try {
//            Long caseId = getCaseId(entity);
//            String tableName = tableInfo.getTableName();
//            Map<String, Object> changes = new HashMap<>();
//            try {
//                // 获取所有字段
//                Field[] fields = entity.getClass().getDeclaredFields();
//
//                for (Field field : fields) {
//                    String fieldName = field.getName();
//
//                    // 排除不需要比对的字段
//                    if (EXCLUDED_FIELDS.contains(fieldName)) {
//                        continue;
//                    }
//                    field.setAccessible(true);
//
//                    // 获取新值
//                    Object newValue = null;
//                    if (entity instanceof Map) {
//                        newValue = ((Map<?, ?>) entity).get(fieldName);
//                    } else {
//                        Field newField = entity.getClass().getDeclaredField(fieldName);
//
//                        newField.setAccessible(true);
//                        newValue = newField.get(entity);
//                        // 如果新值不等于null,添加到change中
//                        if (newValue != null) {
//                            Map<String, Object> change = new HashMap<>();
//                            change.put("old", null);
//                            change.put("new", newValue);
//                            changes.put(fieldName, change);
//                        }
//                    }
//                }
//                if (!changes.isEmpty()) {
//                    Object entityId = getEntityId(entity, tableInfo);
//                    Map<String, Map<String, Object>> result = new HashMap<>();
//                    result.put(String.valueOf(entity.hashCode()), changes);
//                    logChanges(tableInfo.getTableName(), Collections.singletonList(entityId), result, caseId, entity);
//                }
//            } catch (Exception e) {
//                log.warn("比对实体失败", e);
//            }
//        } catch (Exception e) {
//            log.warn("处理实体插入失败", e);
//        }
//    }
//
//    /**
//     * 处理实体方式的更新
//     */
//    private void processEntityUpdate(Object entity, TableInfo tableInfo) {
//        try {
//            Object entityId = getEntityId(entity, tableInfo);
//            if (entityId != null) {
//                Object oldEntity = getOldEntity(tableInfo, entityId);
//                if (oldEntity != null) {
//                    Map<String, Map<String, Object>> changes = compareEntities(oldEntity, entity);
//                    if (!changes.isEmpty()) {
//                        Long caseId = getCaseId(oldEntity);
//                        if (caseId == null) {
//                            caseId = getCaseId(entity);
//                        }
//                        logChanges(tableInfo.getTableName(), Collections.singletonList(entityId), changes, caseId, entity);
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.warn("处理实体更新失败", e);
//        }
//    }
//
//    // 新增辅助方法
//    private BaseMapper<?> getBaseMapper(TableInfo tableInfo) {
//        try {
//            // 获取Mapper类
//            Class<?> mapperClass = Class.forName(tableInfo.getCurrentNamespace());
//
//            // 尝试从Spring容器中获取Mapper实例，而不是手动创建
//            String beanName = getBeanNameFromClass(mapperClass);
//            BaseMapper<?> baseMapper = null;
//
//            try {
//                // 先尝试通过名称获取
//                baseMapper = (BaseMapper<?>) SpringUtils.getBean(beanName);
//            } catch (Exception e) {
//                // 如果通过名称获取失败，尝试通过类型获取
//                try {
//                    baseMapper = (BaseMapper<?>) SpringUtils.getBean(mapperClass);
//                } catch (Exception ex) {
//                    log.warn("无法获取Mapper实例: {}", tableInfo.getCurrentNamespace(), ex);
//                    return null;
//                }
//            }
//
//            return baseMapper;
//        } catch (Exception e) {
//            log.error("获取Mapper实例失败", e);
//            return null;
//        }
//    }
//
//    private Map<String, Object> extractUpdateFields(Object wrapper) {
//        try {
//            Field sqlSetField = wrapper.getClass().getDeclaredField("sqlSet");
//            sqlSetField.setAccessible(true);
//            List<String> sqlSet = (ArrayList) sqlSetField.get(wrapper);
//
//            Map<String, Object> fields = new HashMap<>();
//            if (sqlSet != null) {
//                for (String pair : sqlSet) {
//                    String[] parts = pair.trim().split("=");
//                    if (parts.length == 2) {
//                        String field = parts[0].trim();
//                        String value = parts[1].trim().replaceAll("'", "");
//                        fields.put(field, value);
//                    }
//                }
//            }
//            return fields;
//        } catch (Exception e) {
//            // 添加日志记录异常信息
//            log.error("提取更新字段失败", e);
//            return new HashMap<>(); // 返回空Map而不是null，避免NPE
//        }
//    }
//
//    @SuppressWarnings("unchecked")
//    private List<?> getAffectedEntities(BaseMapper<?> baseMapper, Object wrapper) {
//        try {
//            // 将baseMapper转换为原始类型，然后调用selectList，同时将wrapper转换为Wrapper类型（原始类型）
//            BaseMapper rawMapper = baseMapper;
//            Wrapper rawWrapper = (Wrapper) wrapper;
//            return rawMapper.selectList(rawWrapper);
//        } catch (Exception e) {
//            log.error("获取受影响实体失败", e);
//            return Collections.emptyList();
//        }
//    }
//
//    private Object generateMockEntity(Object oldEntity, Map<String, Object> updateFields, Wrapper<?> wrapper) {
//        try {
//            // 提取真实参数值
//            Map<String, Object> paramMap = Collections.emptyMap();
//            if (wrapper instanceof LambdaUpdateWrapper) {
//                paramMap = extractLambadaUpdateValues((LambdaUpdateWrapper<?>) wrapper);
//            }
//
//            Object newEntity = BeanUtil.copyProperties(oldEntity, oldEntity.getClass());
//
//            for (Map.Entry<String, Object> entry : updateFields.entrySet()) {
//                // 下划线转驼峰
//                String fieldName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, entry.getKey());
//                Field field = ReflectUtil.getField(oldEntity.getClass(), fieldName);
//                if (field == null) continue;
//
//                // 解析占位符值
//                Object value = entry.getValue();
//                if (value instanceof String) {
//                    Matcher matcher = Pattern.compile("MPGENVAL\\d+").matcher((String) value);
//                    if (matcher.find()) {
//                        value = paramMap.get(matcher.group());
//                    }
//                }
//
//                if (field != null) {
//                    field.setAccessible(true);
//                    field.set(newEntity, convertValue(value, field.getType()));
//                }
//            }
//            return newEntity;
//        } catch (Exception e) {
//            log.error("生成模拟实体失败", e);
//            return null;
//        }
//    }
//
//    private void invokeSetMethod(Object wrapper, String field, Object value) {
//        try {
//            Method setMethod = wrapper.getClass().getMethod("set", String.class, Object.class);
//            setMethod.invoke(wrapper, field, value);
//        } catch (Exception e) {
//            log.warn("调用set方法失败: {}", e.getMessage());
//        }
//    }
//
//    /**
//     * 获取实体ID
//     *
//     * @param entity    实体对象
//     * @param tableInfo 表信息
//     * @return 实体ID
//     */
//    private Object getEntityId(Object entity, TableInfo tableInfo) {
//        try {
//            String keyProperty = tableInfo.getKeyProperty();
//            if (keyProperty != null) {
//                if (entity instanceof Map) {
//                    return ((Map<?, ?>) entity).get(keyProperty);
//                } else {
//                    return ReflectUtil.getFieldValue(entity, keyProperty);
//                }
//            }
//        } catch (Exception e) {
//            log.warn("获取实体ID失败", e);
//        }
//        return null;
//    }
//
//    private Long getCaseId(Object entity) {
//        try {
//            String keyProperty = "caseId";
//            // 通过反射获取caseId字段的值
//            Field caseIdField = ReflectUtil.getField(entity.getClass(), keyProperty);
//            if (caseIdField != null) {
//                caseIdField.setAccessible(true);
//                return (Long) caseIdField.get(entity);
//            }
//        } catch (Exception e) {
//            log.warn("获取caseId失败", e);
//        }
//        return null;
//    }
//
//    /**
//     * 如果目标对象的字段值为null，则从源对象复制该字段值
//     */
//    private void copyFieldIfNull(Object target, Object source, String fieldName) {
//        if (ObjectUtil.isNull(ReflectUtil.getFieldValue(target, fieldName))) {
//            Object fieldValue = ReflectUtil.getFieldValue(source, fieldName);
//            if (fieldValue != null) {
//                ReflectUtil.setFieldValue(target, fieldName, fieldValue);
//            }
//        }
//    }
//
//    /**
//     * 判断是否需要复制updaterId
//     * 条件：字段值为null或-1L
//     */
//    private boolean shouldCopyUpdaterId(Object entity) {
//        Object updaterId = ReflectUtil.getFieldValue(entity, "updaterId");
//        return ObjectUtil.isNull(updaterId) || updaterId.equals(-1L);
//    }
//
//    /**
//     * 比对两个实体对象，找出变更的字段
//     *
//     * @param oldEntity 原始实体
//     * @param newEntity 更新实体
//     * @return 变更字段映射 {实体ID -> {字段名 -> {old: 旧值, new: 新值}}}
//     */
//    @SuppressWarnings("unchecked")
//    private Map<String, Map<String, Object>> compareEntities(Object oldEntity, Object newEntity) {
//        Map<String, Map<String, Object>> result = new HashMap<>();
//        Map<String, Object> changes = new HashMap<>();
//        // 复制creatorId逻辑：如果newEntity的creatorId为空，则从oldEntity复制
//        copyFieldIfNull(newEntity, oldEntity, "creatorId");
//
//        // 处理updaterId逻辑：如果newEntity的updaterId为null或-1，则从oldEntity复制creatorId
//        if (shouldCopyUpdaterId(newEntity)) {
//            Object creatorId = ReflectUtil.getFieldValue(oldEntity, "creatorId");
//            if (creatorId != null) {
//                ReflectUtil.setFieldValue(newEntity, "updaterId", creatorId);
//            }
//        }
//
//        // 如果 entity是自定义数据类型，单独处理
//        if (newEntity instanceof MdtCaseCustomData) {
//            // 通过反射找到 newEntity和oldEntity的fieldValue字段值
//            MdtCaseCustomData oldData = (MdtCaseCustomData) oldEntity;
//            MdtCaseCustomData newData = (MdtCaseCustomData) newEntity;
//            // 填充为空的数据
//            if (newData.getCaseId() == null) {
//                newData.setCaseId(oldData.getCaseId());
//            }
//            if (newData.getTmplModuleId() == null) {
//                newData.setTmplModuleId(oldData.getTmplModuleId());
//            }
//            if (newData.getCreatorId() == null) {
//                newData.setCreatorId(oldData.getCreatorId());
//            }
//            String oldFieldValue = oldData.getFieldValue();
//            String newFieldValue = newData.getFieldValue();
//            if (!oldFieldValue.equals(newFieldValue)) {
//                JSONObject oldJson = JSONObject.parseObject(oldFieldValue);
//                JSONObject newJson = JSONObject.parseObject(newFieldValue);
//                for (String key : newJson.keySet()) {
//                    if (oldJson.containsKey(key)) {
//                        String oldValue = oldJson.getString(key);
//                        // 案件刚导入时，mdt_case_custom_data表中fieldValue中的json的value值为空
//                        if (oldValue == null) {
//                            oldJson.put(key, "");
//                        }
//                        // 比较具体的value值是否更改了，如果更改了，直接跳出，在change中添加一个记录，put(key,oldFieldValue)
//                        if (!newJson.getString(key).equals(oldJson.getString(key))) {
//                            Map<String, Object> change = new HashMap<>();
//                            change.put("old", oldJson.toJSONString());
//                            change.put("new", newFieldValue);
//                            changes.put(key, change);
//                            break;
//                        }
//                    }
//                }
//            }
//        } else {
//            try {
//                // 获取所有字段
//                Field[] fields = oldEntity.getClass().getDeclaredFields();
//
//                for (Field field : fields) {
//                    String fieldName = field.getName();
//
//                    // 排除不需要比对的字段
//                    if (EXCLUDED_FIELDS.contains(fieldName)) {
//                        continue;
//                    }
//
//
//                    field.setAccessible(true);
//                    Object oldValue = field.get(oldEntity);
//
//                    // 获取新值
//                    Object newValue = null;
//                    if (newEntity instanceof Map) {
//                        newValue = ((Map<?, ?>) newEntity).get(fieldName);
//                    } else {
//                        Field newField = newEntity.getClass().getDeclaredField(fieldName);
//
//                        newField.setAccessible(true);
//                        newValue = newField.get(newEntity);
//                        // 如果新值是null，使用老对象中的值
//                        if (newValue == null) {
//                            newField.set(newEntity, ReflectUtil.getFieldValue(oldEntity, fieldName));
//                            continue;
//                        }
//                        if ("mdtCaseStatus".equals(fieldName) && "40".equals(newValue)) {
//                            ReflectUtil.setFieldValue(oldEntity, fieldName, "35");
//                            ReflectUtil.setFieldValue(newEntity, fieldName, "40");
//                            oldValue = "35";
//                        }
//                    }
//                    // 比较值是否相同
//                    if (!areValuesEqual(oldValue, newValue)) {
//                        Map<String, Object> change = new HashMap<>();
//                        change.put("old", oldValue);
//                        change.put("new", newValue);
//                        changes.put(fieldName, change);
//                    } else {
//                        // 如果值相同，把旧、新值置为空
////                        ReflectUtil.setFieldValue(oldEntity, fieldName, null);
////                        ReflectUtil.setFieldValue(newEntity, fieldName, null);
//                    }
//                }
//            } catch (Exception e) {
//                log.warn("比对实体失败", e);
//            }
//        }
//        // 如果有变更，添加到结果中
//        if (!changes.isEmpty()) {
//            // 使用实体的hashCode作为键，因为我们可能没有实际的ID
//            result.put(String.valueOf(oldEntity.hashCode()), changes);
//        }
//        return result;
//    }
//
//    public static boolean areValuesEqual(Object oldValue, Object newValue) {
//        // 处理两个null的情况
//        if (oldValue == null && newValue == null) {
//            return true;
//        }
//        // 处理一个null的情况
//        if (oldValue == null || newValue == null) {
//            return false;
//        }
//
//        // 处理JSONObject比较（基于内容）
//        if (oldValue instanceof JSONObject && newValue instanceof JSONObject) {
//            // FastJSON的JSONObject没有similar方法，使用equals或手动比较
//            return ((JSONObject) oldValue).equals(newValue) ||
//                   ((JSONObject) oldValue).toJSONString().equals(((JSONObject) newValue).toJSONString());
//        }
//
//        // 数值类型统一转换为BigDecimal比较
//        if (isNumber(oldValue) && isNumber(newValue)) {
//            BigDecimal bd1 = convertToBigDecimal(oldValue);
//            BigDecimal bd2 = convertToBigDecimal(newValue);
//            return bd1.compareTo(bd2) == 0;
//        }
//
//        // 其他情况：严格类型匹配 + equals比较
//        return Objects.equals(oldValue, newValue);
//    }
//
//    // 检查对象是否为数值类型
//    private static boolean isNumber(Object obj) {
//        return obj instanceof Number;
//    }
//
//    // 将数值对象安全转换为BigDecimal
//    private static BigDecimal convertToBigDecimal(Object num) {
//        if (num instanceof BigDecimal) {
//            return (BigDecimal) num;
//        } else if (num instanceof Integer) {
//            return new BigDecimal((Integer) num);
//        } else if (num instanceof Long) {
//            return BigDecimal.valueOf((Long) num);
//        } else if (num instanceof Float) {
//            return BigDecimal.valueOf((Float) num); // 避免精度问题
//        } else if (num instanceof Double) {
//            return BigDecimal.valueOf((Double) num); // 避免精度问题
//        } else if (num instanceof Byte || num instanceof Short) {
//            return BigDecimal.valueOf(((Number) num).doubleValue());
//        } else {
//            throw new IllegalArgumentException("Unsupported number type: " + num.getClass());
//        }
//    }
//
//    /**
//     * 记录数据变更日志
//     *
//     * @param tableName  表名
//     * @param ids        影响的ID列表
//     * @param allChanges 所有变更 {实体ID -> {字段名 -> {old: 旧值, new: 新值}}}
//     */
//    @SuppressWarnings("unchecked")
//    private void logChanges(String tableName, List<?> ids, Map<String, Map<String, Object>> allChanges, Long caseId, Object entity) {
//        if (allChanges.isEmpty() || caseId == null) {
//            return;
//        }
//        // 如果entity 的caseId为空，设置caseId
//        if (ReflectUtil.getFieldValue(entity, "caseId") == null) {
//            ReflectUtil.setFieldValue(entity, "caseId", caseId);
//        }
//
//        Map<String, Integer> caseIdTmplIdCache = RedisUtils.getCacheMap(RedisKeyEnums.MDT_CASE_TMPL_ID.getKey());
//        // 获取模板id
//        Integer tmplId = caseIdTmplIdCache.get(String.valueOf(caseId));
//        if (tmplId == null) {
//            tmplId = (Integer) ReflectUtil.getFieldValue(entity, "tmplCaseId");
//            if (tmplId == null) {
//                log.warn("无法获取模板ID: {}", caseId);
//                return;
//            }
//        }
//        List<List<String>> tmplModuleFieldCache = RedisUtils.getCacheList(RedisKeyEnums.TMPL_CASE_MODULE_FIELDS.getKey() + tmplId);
//        if (tmplModuleFieldCache.isEmpty()) {
//            log.warn("无法获取模板字段列表: {}", tmplId);
//            return;
//        }
//        List<String> moduleFields = tmplModuleFieldCache.get(0);
//
//        if("mdt_case_custom_data".equals(tableName)) {
//            moduleFields.add("field_value");
//        }
//        StringBuilder sb = new StringBuilder();
//        sb.append("\n=== 数据更新追踪 ===\n");
//        sb.append("表名: ").append(tableName).append("\n");
//        sb.append("影响ID: ").append(ids).append("\n");
//
//        for (Map.Entry<String, Map<String, Object>> entry : allChanges.entrySet()) {
//            sb.append("\n记录ID: ").append(entry.getKey()).append("\n");
//            Map<String, Object> changes = entry.getValue();
//            if (changes.isEmpty()) {
//                sb.append("  无字段变更\n");
//            } else {
//                // 实体类是否需要触发
//                AtomicBoolean trigger = new AtomicBoolean(false);
//                // 定义变更字段列表
//                List<String> changedFieldList = new ArrayList<>();
//                changes.forEach((field, diff) -> {
//                    // 判断字段是否在 tmplModuleFieldCache中
//                    String underlineCase = StrUtil.toUnderlineCase(field);
//                    log.info("驼峰字段 {} 转成下划线后：{}", field, underlineCase);
//                    if (moduleFields.contains(underlineCase)) {
//                        log.info("字段 {} 在模板字段列表中，需要触发", underlineCase);
//                        trigger.set(true);
//                        changedFieldList.add(field);
//                    }
//                });
//                if (trigger.get()) {
//                    Long accountId = null;
//                    try {
//                        accountId = (Long) ReflectUtil.getFieldValue(entity, "creatorId");
//                        if (accountId == null) {
//                            accountId = (Long) ReflectUtil.getFieldValue(entity, "updateId");
//                        }
//                    } catch (Exception e) {
//                        log.error("获取创建人ID失败", e);
//                        accountId = -1L;
//                    }
//                    // 日志记录
//                    autoTaskService.caseAllTrigger(tableName, caseId, tmplId.longValue(), new ArrayList<Object>() {{
//                        add(entity);
//                    }}, accountId, changedFieldList);
//                }
//            }
//        }
//        sb.append("==================\n");
//
//        // 使用SLF4J记录日志，而不是System.out
//        log.info(sb.toString());
//    }
//
//    /**
//     * 格式化值，避免null值导致的异常
//     *
//     * @param value 需要格式化的值
//     * @return 格式化后的字符串
//     */
//    private String formatValue(Object value) {
//        if (value == null) {
//            return "null";
//        }
//        return String.valueOf(value);
//    }
//
//    /**
//     * 生成新的实体对象，将原始实体和更新实体合并
//     * 注：此方法保留作为工具方法，当前比对逻辑使用compareEntities方法
//     *
//     * @param source    源实体（原始数据）
//     * @param update    更新实体（包含要更新的字段）
//     * @param tableInfo 表信息
//     * @return 合并后的新实体
//     */
//    private Object generateNewEntity(Object source, Object update, TableInfo tableInfo) {
//        try {
//            // 创建源实体的副本
//            Object newEntity = source.getClass().getDeclaredConstructor().newInstance();
//            BeanUtil.copyProperties(source, newEntity);
//
//            // 将更新实体中的非空字段复制到新实体
//            if (update instanceof Map) {
//                // 处理Map类型的更新实体
//                Map<?, ?> updateMap = (Map<?, ?>) update;
//                for (Field field : newEntity.getClass().getDeclaredFields()) {
//                    String fieldName = field.getName();
//                    if (updateMap.containsKey(fieldName) && updateMap.get(fieldName) != null) {
//                        field.setAccessible(true);
//                        field.set(newEntity, updateMap.get(fieldName));
//                    }
//                }
//            } else {
//                // 处理普通实体类型的更新实体
//                Field[] fields = update.getClass().getDeclaredFields();
//                for (Field field : fields) {
//                    field.setAccessible(true);
//                    Object value = field.get(update);
//                    if (value != null) {
//                        try {
//                            Field targetField = newEntity.getClass().getDeclaredField(field.getName());
//                            targetField.setAccessible(true);
//                            targetField.set(newEntity, value);
//                        } catch (NoSuchFieldException e) {
//                            // 忽略目标实体中不存在的字段
//                        }
//                    }
//                }
//            }
//            return newEntity;
//        } catch (Exception e) {
//            log.error("生成新实体失败", e);
//            throw new RuntimeException("生成新实体失败", e);
//        }
//    }
//
//    /**
//     * 获取数据库中的原始实体
//     *
//     * @param tableInfo 表信息
//     * @param id        实体ID
//     * @return 原始实体对象
//     */
//    private Object getOldEntity(TableInfo tableInfo, Object id) {
//        try {
//            // 获取Mapper类
//            Class<?> mapperClass = Class.forName(tableInfo.getCurrentNamespace());
//
//            // 尝试从Spring容器中获取Mapper实例
//            // 注意：Spring Bean名称通常是类名首字母小写
//            String beanName = getBeanNameFromClass(mapperClass);
//            BaseMapper<?> baseMapper = null;
//
//            try {
//                // 先尝试通过名称获取
//                baseMapper = (BaseMapper<?>) SpringUtils.getBean(beanName);
//            } catch (Exception e) {
//                // 如果通过名称获取失败，尝试通过类型获取
//                try {
//                    baseMapper = (BaseMapper<?>) SpringUtils.getBean(mapperClass);
//                } catch (Exception ex) {
//                    log.warn("无法获取Mapper实例: {}", tableInfo.getCurrentNamespace(), ex);
//                    return null;
//                }
//            }
//
//            // 使用Mapper查询原始实体
//            if (baseMapper != null && id != null) {
//                return baseMapper.selectById((Serializable) id);
//            }
//        } catch (Exception e) {
//            log.warn("获取原始实体失败: {}", e.getMessage());
//        }
//        return null;
//    }
//
//    /**
//     * 从类获取Bean名称（首字母小写）
//     *
//     * @param clazz 类
//     * @return Bean名称
//     */
//    private String getBeanNameFromClass(Class<?> clazz) {
//        String className = clazz.getSimpleName();
//        return Character.toLowerCase(className.charAt(0)) + className.substring(1);
//    }
//
//    /**
//     * 设置实体属性值
//     *
//     * @param entity     实体对象
//     * @param userId     用户ID
//     * @param updateTime 更新时间
//     */
//    private void setEntityProperties(Object entity, Long userId, Date updateTime) {
//        try {
//            if (ReflectUtil.hasField(entity.getClass(), "updaterId")) {
//                BeanUtil.setProperty(entity, "updaterId", userId);
//            }
//            if (ReflectUtil.hasField(entity.getClass(), "updateTime")) {
//                BeanUtil.setProperty(entity, "updateTime", updateTime);
//            }
//        } catch (Exception e) {
//            log.warn("设置实体属性失败: {}", e.getMessage());
//        }
//    }
//
//    /**
//     * 获取当前用户ID
//     * 如果获取失败则返回默认值
//     *
//     * @return 当前用户ID
//     */
//    private Long getCurrentUserId() {
//        try {
//            Long userId = SpringUtils.getCurrentUserId();
//            return userId != null ? userId : -1L;
//        } catch (Exception e) {
//            log.warn("获取当前用户ID失败", e);
//            return -1L;
//        }
//    }
//
//}
